# Vercel Environment Variables Fix

## Problem
The frontend is still using the full Railway URL instead of relative paths, causing 404 errors for admin API calls.

## Solution
Update Vercel environment variables to use empty string for REACT_APP_API_URL.

## Steps to Fix

### 1. Go to Vercel Dashboard
1. Visit https://vercel.com/dashboard
2. Find your AI-Pomo project
3. Click on the project

### 2. Update Environment Variables
1. Go to "Settings" tab
2. Click "Environment Variables" in the sidebar
3. Find `REACT_APP_API_URL`
4. Change its value from `https://ai-pomo-production.up.railway.app` to empty string (leave blank)
5. Make sure it applies to "Production" environment
6. Click "Save"

### 3. Redeploy
1. Go to "Deployments" tab
2. Click "..." on the latest deployment
3. Click "Redeploy"
4. Wait for deployment to complete

## Expected Result
After redeployment, the frontend should use relative paths like `/api/admin/send-reactivation-email` instead of full URLs, and the Vercel proxy will forward these to Railway.

## Verification
Check browser console - you should see:
```
API Service initialized with baseURL: 
```
Instead of:
```
API Service initialized with baseURL: https://ai-pomo-production.up.railway.app
```
