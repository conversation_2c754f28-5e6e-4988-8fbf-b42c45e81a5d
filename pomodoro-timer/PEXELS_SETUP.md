# Pexels API Setup Guide

This guide explains how to set up the Pexels API for the blog image selector feature.

## Getting Your Pexels API Key

1. **Create a Pexels Account**
   - Go to https://www.pexels.com/
   - Sign up for a free account or log in

2. **Access the API**
   - Visit https://www.pexels.com/api/
   - Click "Get Started"

3. **Generate API Key**
   - Fill out the API application form
   - Describe your use case (e.g., "Blog image selection for productivity app")
   - Accept the terms and conditions
   - Submit the application

4. **Get Your API Key**
   - Once approved, you'll receive an API key
   - Copy this key for the next step

## Configuration

1. **Add API Key to Environment Variables**
   ```bash
   # In your .env.local file
   REACT_APP_PEXELS_API_KEY=your_actual_api_key_here
   ```

2. **Restart Development Server**
   ```bash
   npm start
   ```

## Features

The new image selector includes:

- **Search Images**: Search Pexels' database of high-quality photos
- **Filter by Orientation**: Landscape, portrait, or square
- **Quick Search Tags**: Common productivity-related keywords
- **Load More**: Infinite scroll to browse more images
- **Attribution**: Automatic photographer credit

## API Limits

- **Free Tier**: 200 requests per hour, 20,000 requests per month
- **Paid Plans**: Available for higher usage

## Usage in Blog Posts

1. Open the blog post editor
2. Click on the image selection field
3. Search for relevant keywords
4. Select an image
5. The image URL and attribution are automatically saved

## Attribution

When using Pexels images, proper attribution is automatically included:
- Photographer name
- Link to original photo page
- Pexels credit

This ensures compliance with Pexels' terms of service.