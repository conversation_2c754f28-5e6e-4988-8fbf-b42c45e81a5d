{"name": "ai-pomo", "version": "1.5.1", "private": true, "description": "AI-enhanced Pomodoro timer with task management and productivity tracking", "author": "AI Pomo Team", "homepage": "/", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hello-pangea/dnd": "^18.0.1", "@paypal/react-paypal-js": "^8.8.3", "axios": "^1.8.4", "chart.js": "^4.5.0", "html2canvas": "^1.4.1", "jwt-decode": "^4.0.0", "openai": "^4.98.0", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-lite-youtube-embed": "^2.5.1", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "react-youtube": "^10.1.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "styled-components": "^6.1.17", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "vercel-build": "GENERATE_SOURCEMAP=false react-scripts build", "analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.27.1", "babel-plugin-styled-components": "^2.1.4", "webpack-bundle-analyzer": "^4.10.2"}, "proxy": "http://localhost:5000"}