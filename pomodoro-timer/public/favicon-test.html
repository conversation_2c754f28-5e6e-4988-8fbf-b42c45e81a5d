<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Test - AI Pomo</title>
    
    <!-- Favicon configuration for Vercel -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico?v=3" />
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico?v=3" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.ico?v=3" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon.ico?v=3" />
    <link rel="icon" type="image/png" sizes="192x192" href="/ai-pomo.png?v=3" />
    <link rel="icon" type="image/png" sizes="512x512" href="/ai-pomo.png?v=3" />
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-item h3 {
            margin-top: 0;
            color: #d95550;
        }
        img {
            margin: 10px;
            border: 1px solid #ccc;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>AI Pomo Favicon Test Page</h1>
    <p>This page tests if the favicon and icons are loading correctly on www.ai-pomo.com (Vercel deployment)</p>
    
    <div class="test-item">
        <h3>1. Favicon.ico Test</h3>
        <p>Direct link to favicon.ico:</p>
        <img src="/favicon.ico?v=3" alt="Favicon" width="32" height="32">
        <p><a href="/favicon.ico?v=3" target="_blank">Open favicon.ico directly</a></p>
    </div>
    
    <div class="test-item">
        <h3>2. AI Pomo PNG Test</h3>
        <p>Direct link to ai-pomo.png:</p>
        <img src="/ai-pomo.png?v=3" alt="AI Pomo PNG" width="64" height="64">
        <p><a href="/ai-pomo.png?v=3" target="_blank">Open ai-pomo.png directly</a></p>
    </div>
    
    <div class="test-item">
        <h3>3. Browser Tab Icon</h3>
        <p>Check if the favicon appears in the browser tab. You should see the AI Pomo icon next to the page title.</p>
    </div>
    
    <div class="test-item">
        <h3>4. Troubleshooting Steps</h3>
        <ul>
            <li>Clear browser cache and hard refresh (Ctrl+F5 or Cmd+Shift+R)</li>
            <li>Try opening in incognito/private browsing mode</li>
            <li>Check browser developer tools Network tab for 404 errors</li>
            <li>Verify the domain is correctly set to www.ai-pomo.com</li>
        </ul>
    </div>
    
    <script>
        // Test if favicon loads successfully
        function testFavicon() {
            const img = new Image();
            img.onload = function() {
                console.log('✅ Favicon loaded successfully');
                document.getElementById('favicon-status').innerHTML = '<span class="success">✅ Favicon loads correctly</span>';
            };
            img.onerror = function() {
                console.log('❌ Favicon failed to load');
                document.getElementById('favicon-status').innerHTML = '<span class="error">❌ Favicon failed to load</span>';
            };
            img.src = '/favicon.ico?v=3&test=' + Date.now();
        }
        
        // Test if PNG icon loads successfully
        function testPngIcon() {
            const img = new Image();
            img.onload = function() {
                console.log('✅ PNG icon loaded successfully');
                document.getElementById('png-status').innerHTML = '<span class="success">✅ PNG icon loads correctly</span>';
            };
            img.onerror = function() {
                console.log('❌ PNG icon failed to load');
                document.getElementById('png-status').innerHTML = '<span class="error">❌ PNG icon failed to load</span>';
            };
            img.src = '/ai-pomo.png?v=3&test=' + Date.now();
        }
        
        // Run tests when page loads
        window.onload = function() {
            testFavicon();
            testPngIcon();
        };
    </script>
    
    <div class="test-item">
        <h3>5. Automatic Test Results</h3>
        <p id="favicon-status">Testing favicon...</p>
        <p id="png-status">Testing PNG icon...</p>
    </div>
    
    <div class="test-item">
        <h3>6. Return to Main Site</h3>
        <p><a href="/">← Back to AI Pomo</a></p>
    </div>
</body>
</html>
