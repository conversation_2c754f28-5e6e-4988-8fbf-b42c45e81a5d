// Pexels API Service
const PEXELS_API_KEY = process.env.REACT_APP_PEXELS_API_KEY || 'YOUR_PEXELS_API_KEY';
const PEXELS_BASE_URL = 'https://api.pexels.com/v1';

class PexelsApiService {
  constructor() {
    this.apiKey = PEXELS_API_KEY;
  }

  // Helper method to make API requests
  async makeRequest(endpoint, params = {}) {
    if (!this.apiKey || this.apiKey === 'YOUR_PEXELS_API_KEY') {
      throw new Error('Pexels API key is not configured');
    }

    const url = new URL(`${PEXELS_BASE_URL}${endpoint}`);
    
    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    try {
      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': this.apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Pexels API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Pexels API request failed:', error);
      throw error;
    }
  }

  // Search for photos
  async searchPhotos(query, options = {}) {
    const params = {
      query,
      per_page: options.per_page || 20,
      page: options.page || 1,
      orientation: options.orientation || 'all', // 'landscape', 'portrait', 'square'
      size: options.size || 'all', // 'large', 'medium', 'small'
      color: options.color || 'all',
      locale: options.locale || 'en-US',
    };

    return await this.makeRequest('/search', params);
  }

  // Get curated photos (featured/popular photos)
  async getCuratedPhotos(options = {}) {
    const params = {
      per_page: options.per_page || 20,
      page: options.page || 1,
    };

    return await this.makeRequest('/curated', params);
  }

  // Get photo by ID
  async getPhotoById(id) {
    return await this.makeRequest(`/photos/${id}`);
  }

  // Format photo data for our application
  formatPhoto(photo) {
    return {
      id: photo.id,
      url: photo.src.large,
      urlMedium: photo.src.medium,
      urlSmall: photo.src.small,
      urlTiny: photo.src.tiny,
      urlOriginal: photo.src.original,
      title: photo.alt || `Photo by ${photo.photographer}`,
      alt: photo.alt || `Photo by ${photo.photographer}`,
      photographer: photo.photographer,
      photographerUrl: photo.photographer_url,
      pageUrl: photo.url,
      width: photo.width,
      height: photo.height,
      avgColor: photo.avg_color,
    };
  }

  // Format search results
  formatSearchResults(data) {
    return {
      photos: data.photos.map(photo => this.formatPhoto(photo)),
      totalResults: data.total_results,
      page: data.page,
      perPage: data.per_page,
      nextPage: data.next_page,
      prevPage: data.prev_page,
    };
  }
}

export const pexelsApi = new PexelsApiService();
export default pexelsApi;