import { api } from './apiService';

export const blogApi = {
  // Get all blog posts with pagination and filters
  getBlogPosts: async (page = 1, limit = 10, category = '', tag = '', search = '') => {
    try {
      let url = `/blog?page=${page}&limit=${limit}`;
      if (category) url += `&category=${category}`;
      if (tag) url += `&tag=${tag}`;
      if (search) url += `&search=${search}`;

      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      throw error;
    }
  },

  // Get all blog posts for admin (including unpublished)
  getAllBlogPosts: async () => {
    try {
      const response = await api.get('/blog/all');
      return response.data;
    } catch (error) {
      console.error('Error fetching all blog posts:', error);
      throw error;
    }
  },

  // Get a single blog post by slug
  getBlogPostBySlug: async (slug) => {
    try {
      const response = await api.get(`/blog/post/${slug}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching blog post:', error);
      throw error;
    }
  },

  // Get a single blog post by ID (for admin editing)
  getBlogPostById: async (id) => {
    try {
      const response = await api.get(`/blog/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching blog post by ID:', error);
      throw error;
    }
  },

  // Get all blog categories
  getCategories: async () => {
    try {
      const response = await api.get('/blog/categories');
      return response.data;
    } catch (error) {
      console.error('Error fetching blog categories:', error);
      throw error;
    }
  },

  // Admin: Create a new blog post
  createBlogPost: async (postData) => {
    try {
      const response = await api.post('/blog', postData);
      return response.data;
    } catch (error) {
      console.error('Error creating blog post:', error);
      throw error;
    }
  },

  // Admin: Update a blog post
  updateBlogPost: async (id, postData) => {
    try {
      const response = await api.put(`/blog/${id}`, postData);
      return response.data;
    } catch (error) {
      console.error('Error updating blog post:', error);
      throw error;
    }
  },

  // Admin: Delete a blog post
  deleteBlogPost: async (id) => {
    try {
      const response = await api.delete(`/blog/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting blog post:', error);
      throw error;
    }
  },

  // Admin: Import multiple blog posts
  importBlogPosts: async (articles) => {
    try {
      const response = await api.post('/blog/import', { articles });
      return response.data;
    } catch (error) {
      console.error('Error importing blog posts:', error);
      throw error;
    }
  },

  // Admin: Create a new category
  createCategory: async (categoryData) => {
    try {
      const response = await api.post('/blog/categories', categoryData);
      return response.data;
    } catch (error) {
      console.error('Error creating blog category:', error);
      throw error;
    }
  },
};
