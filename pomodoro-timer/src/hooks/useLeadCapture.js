import { useState, useEffect } from 'react';

const useLeadCapture = (config = {}) => {
  const {
    // Trigger conditions
    exitIntent = true,
    timeDelay = 30000, // 30 seconds
    scrollPercentage = 70,
    pageViews = 2,
    
    // Modal config
    title = "Wait! Don't miss out on your FREE productivity boost!",
    subtitle = "Get our exclusive AI-powered productivity guide before you go",
    leadMagnet = "exit_intent_guide",
    
    // Behavior
    showOnce = true,
    cookieDuration = 7 // days
  } = config;

  const [showModal, setShowModal] = useState(false);
  const [triggered, setTriggered] = useState(false);

  // Check if already shown
  const getCookieName = () => `ai_pomo_lead_${leadMagnet}_shown`;
  
  const hasBeenShown = () => {
    if (!showOnce) return false;
    const cookieName = getCookieName();
    return document.cookie.includes(`${cookieName}=true`);
  };

  const markAsShown = () => {
    const cookieName = getCookieName();
    const expires = new Date();
    expires.setDate(expires.getDate() + cookieDuration);
    document.cookie = `${cookieName}=true; expires=${expires.toUTCString()}; path=/`;
  };

  // Track page views
  const incrementPageViews = () => {
    const views = parseInt(localStorage.getItem('ai_pomo_page_views') || '0') + 1;
    localStorage.setItem('ai_pomo_page_views', views.toString());
    return views;
  };

  // Exit intent detection
  useEffect(() => {
    if (!exitIntent || triggered || hasBeenShown()) return;

    const handleMouseLeave = (e) => {
      if (e.clientY <= 0) {
        setShowModal(true);
        setTriggered(true);
      }
    };

    document.addEventListener('mouseleave', handleMouseLeave);
    return () => document.removeEventListener('mouseleave', handleMouseLeave);
  }, [exitIntent, triggered]);

  // Time delay trigger
  useEffect(() => {
    if (!timeDelay || triggered || hasBeenShown()) return;

    const timer = setTimeout(() => {
      setShowModal(true);
      setTriggered(true);
    }, timeDelay);

    return () => clearTimeout(timer);
  }, [timeDelay, triggered]);

  // Scroll percentage trigger
  useEffect(() => {
    if (!scrollPercentage || triggered || hasBeenShown()) return;

    const handleScroll = () => {
      const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
      if (scrolled >= scrollPercentage) {
        setShowModal(true);
        setTriggered(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [scrollPercentage, triggered]);

  // Page views trigger
  useEffect(() => {
    if (!pageViews || triggered || hasBeenShown()) return;

    const currentViews = incrementPageViews();
    if (currentViews >= pageViews) {
      setShowModal(true);
      setTriggered(true);
    }
  }, [pageViews, triggered]);

  const closeModal = () => {
    setShowModal(false);
    markAsShown();
  };

  const openModal = () => {
    setShowModal(true);
    setTriggered(true);
  };

  return {
    showModal,
    closeModal,
    openModal,
    modalProps: {
      title,
      subtitle,
      leadMagnet,
      source: 'auto_trigger'
    }
  };
};

// Predefined configurations for different use cases
export const leadCaptureConfigs = {
  // For pricing page - immediate exit intent
  pricing: {
    exitIntent: true,
    timeDelay: 10000, // 10 seconds
    title: "Wait! Get 50% OFF your first month!",
    subtitle: "Don't miss this exclusive offer for new users",
    leadMagnet: "pricing_discount",
    showOnce: true
  },

  // For blog posts - content engagement
  blog: {
    exitIntent: true,
    scrollPercentage: 80,
    timeDelay: 45000, // 45 seconds
    title: "Loved this article? Get more productivity tips!",
    subtitle: "Join our email list for weekly productivity insights",
    leadMagnet: "blog_subscription",
    showOnce: true
  },

  // For homepage - first-time visitors
  homepage: {
    exitIntent: true,
    timeDelay: 20000, // 20 seconds
    pageViews: 1,
    title: "Welcome to AI Pomo! 🚀",
    subtitle: "Get started with our free productivity guide",
    leadMagnet: "welcome_guide",
    showOnce: true
  },

  // For returning visitors
  returning: {
    exitIntent: true,
    pageViews: 3,
    title: "Ready to level up your productivity?",
    subtitle: "Get advanced strategies used by our power users",
    leadMagnet: "advanced_guide",
    showOnce: true,
    cookieDuration: 30 // Show again after 30 days
  },

  // For feature pages
  features: {
    scrollPercentage: 60,
    timeDelay: 30000,
    title: "See AI Pomo in action!",
    subtitle: "Watch our 5-minute demo and get a free trial",
    leadMagnet: "demo_access",
    showOnce: true
  }
};

export default useLeadCapture;