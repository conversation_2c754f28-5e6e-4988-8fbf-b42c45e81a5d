import React, { useState } from 'react';
import styled from 'styled-components';
import { FaTimes, FaGift, FaDownload, FaEnvelope, <PERSON>a<PERSON><PERSON>, FaSpinner } from 'react-icons/fa';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  
  @keyframes slideUp {
    from { 
      opacity: 0;
      transform: translateY(30px);
    }
    to { 
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const Header = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  text-align: center;
  position: relative;
  
  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Form = styled.form`
  padding: 32px 24px 24px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
    font-size: 14px;
  }
`;

const InputWrapper = styled.div`
  position: relative;
  
  svg {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #95a5a6;
    font-size: 16px;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  
  &:focus {
    outline: none;
    border-color: #3498db;
  }
  
  &::placeholder {
    color: #95a5a6;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  background: white;
  
  &:focus {
    outline: none;
    border-color: #3498db;
  }
`;

const CheckboxWrapper = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin: 20px 0;
  
  input[type="checkbox"] {
    margin-top: 4px;
  }
  
  label {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
    color: #34495e;
  }
  
  a {
    color: #3498db;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const SubmitButton = styled.button`
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const BenefitsList = styled.div`
  margin: 20px 0;
  
  h4 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 16px;
  }
`;

const Benefit = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #34495e;
  
  &:before {
    content: '✓';
    color: #27ae60;
    font-weight: bold;
    font-size: 16px;
  }
`;

const LeadCaptureModal = ({ 
  isOpen, 
  onClose, 
  title = "Get Your FREE Productivity Starter Kit",
  subtitle = "Join 1000+ users who've boosted their productivity with AI Pomo",
  leadMagnet = "productivity_guide",
  source = "modal"
}) => {
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    occupation: '',
    interests: [],
    gdprConsent: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.gdprConsent) {
      alert('Please agree to our privacy policy to continue');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/marketing/leads/capture', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          source: {
            type: source,
            campaign: leadMagnet,
            url: window.location.pathname
          },
          profile: {
            name: formData.name,
            occupation: formData.occupation,
            interests: formData.interests
          },
          gdprConsent: formData.gdprConsent
        })
      });

      if (response.ok) {
        setIsSubmitted(true);
        // Track conversion
        if (window.gtag) {
          window.gtag('event', 'lead_capture', {
            event_category: 'Marketing',
            event_label: leadMagnet,
            value: 1
          });
        }
        
        // Auto-close after 3 seconds
        setTimeout(() => {
          onClose();
          setIsSubmitted(false);
          setFormData({
            email: '',
            name: '',
            occupation: '',
            interests: [],
            gdprConsent: false
          });
        }, 3000);
      } else {
        throw new Error('Failed to submit');
      }
    } catch (error) {
      console.error('Error submitting lead:', error);
      alert('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  if (isSubmitted) {
    return (
      <ModalOverlay onClick={onClose}>
        <ModalContent onClick={e => e.stopPropagation()}>
          <Header>
            <CloseButton onClick={onClose}>
              <FaTimes />
            </CloseButton>
            <h2>🎉 Thank You!</h2>
            <p>Check your email for your free productivity guide</p>
          </Header>
          <div style={{ padding: '32px 24px', textAlign: 'center' }}>
            <div style={{ fontSize: '64px', marginBottom: '16px' }}>📧</div>
            <p style={{ fontSize: '16px', color: '#2c3e50', marginBottom: '8px' }}>
              Your download link has been sent to <strong>{formData.email}</strong>
            </p>
            <p style={{ fontSize: '14px', color: '#7f8c8d' }}>
              Don't forget to check your spam folder!
            </p>
          </div>
        </ModalContent>
      </ModalOverlay>
    );
  }

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <Header>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
          <h2>
            <FaGift style={{ marginRight: '8px' }} />
            {title}
          </h2>
          <p>{subtitle}</p>
        </Header>

        <Form onSubmit={handleSubmit}>
          <BenefitsList>
            <h4>What you'll get:</h4>
            <Benefit>AI-powered project planning templates</Benefit>
            <Benefit>Pomodoro technique optimization guide</Benefit>
            <Benefit>Productivity tracking worksheets</Benefit>
            <Benefit>Exclusive tips from productivity experts</Benefit>
          </BenefitsList>

          <FormGroup>
            <label htmlFor="email">Email Address *</label>
            <InputWrapper>
              <FaEnvelope />
              <Input
                type="email"
                id="email"
                name="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </InputWrapper>
          </FormGroup>

          <FormGroup>
            <label htmlFor="name">First Name</label>
            <InputWrapper>
              <FaUser />
              <Input
                type="text"
                id="name"
                name="name"
                placeholder="Your first name"
                value={formData.name}
                onChange={handleInputChange}
              />
            </InputWrapper>
          </FormGroup>

          <FormGroup>
            <label htmlFor="occupation">I am a...</label>
            <InputWrapper>
              <FaUser />
              <Select
                id="occupation"
                name="occupation"
                value={formData.occupation}
                onChange={handleInputChange}
              >
                <option value="">Select your role</option>
                <option value="student">Student</option>
                <option value="employee">Employee</option>
                <option value="freelancer">Freelancer</option>
                <option value="entrepreneur">Entrepreneur</option>
                <option value="other">Other</option>
              </Select>
            </InputWrapper>
          </FormGroup>

          <CheckboxWrapper>
            <input
              type="checkbox"
              id="gdprConsent"
              name="gdprConsent"
              checked={formData.gdprConsent}
              onChange={handleInputChange}
              required
            />
            <label htmlFor="gdprConsent">
              I agree to receive emails from AI Pomo and understand I can unsubscribe at any time. 
              Read our <a href="/privacy" target="_blank">Privacy Policy</a>.
            </label>
          </CheckboxWrapper>

          <SubmitButton type="submit" disabled={isSubmitting || !formData.gdprConsent}>
            {isSubmitting ? (
              <>
                <FaSpinner className="fa-spin" />
                Sending...
              </>
            ) : (
              <>
                <FaDownload />
                Get My Free Guide
              </>
            )}
          </SubmitButton>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default LeadCaptureModal;