import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaEnvelope, 
  FaReddit<PERSON>lien, 
  <PERSON>a<PERSON>wi<PERSON>, 
  FaLinkedin, 
  FaExternalLinkAlt,
  FaChartLine,
  FaUsers,
  FaRocket,
  FaEye,
  FaMousePointer,
  FaArrowUp,
  FaPlus,
  FaCog,
  FaDownload,
  FaSearch,
  FaFilter,
  FaCalendarAlt,
  FaCheckCircle,
  FaTimesCircle,
  FaClock
} from 'react-icons/fa';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import EmailLeadsManager from './EmailLeadsManager';
import RedditAnalyzer from './RedditAnalyzer';
import BacklinkManager from './BacklinkManager';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const DashboardContainer = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: ${props => props.primary ? '#3498db' : '#fff'};
  color: ${props => props.primary ? '#fff' : '#2c3e50'};
  border: 2px solid #3498db;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    background: ${props => props.primary ? '#2980b9' : '#3498db'};
    color: #fff;
    transform: translateY(-2px);
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: linear-gradient(135deg, ${props => props.gradient || '#3498db, #2980b9'});
  padding: 24px;
  border-radius: 12px;
  color: white;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
  }
`;

const StatIcon = styled.div`
  font-size: 24px;
  margin-bottom: 12px;
  opacity: 0.9;
`;

const StatValue = styled.div`
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
`;

const StatLabel = styled.div`
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 4px;
`;

const StatChange = styled.div`
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0.8;
`;

const TabsContainer = styled.div`
  margin-bottom: 30px;
`;

const TabsList = styled.div`
  display: flex;
  border-bottom: 2px solid #ecf0f1;
  margin-bottom: 20px;
`;

const Tab = styled.button`
  padding: 12px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: ${props => props.active ? '#3498db' : '#7f8c8d'};
  border-bottom: 2px solid ${props => props.active ? '#3498db' : 'transparent'};
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    color: #3498db;
  }
`;

const TabContent = styled.div`
  display: ${props => props.active ? 'block' : 'none'};
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: ${props => props.columns || '2fr 1fr'};
  gap: 30px;
  margin-bottom: 30px;
  
  /* Prevent grid items from expanding infinitely */
  & > * {
    min-height: 0;
    overflow: hidden;
  }
`;

const Card = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #ecf0f1;
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

const CardActions = styled.div`
  display: flex;
  gap: 8px;
`;

const IconButton = styled.button`
  padding: 8px;
  background: none;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    color: #333;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
  }
  
  th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
  }
  
  tr:hover {
    background: #f8f9fa;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: ${props => {
    switch (props.status) {
      case 'published': return '#d4edda';
      case 'scheduled': return '#fff3cd';
      case 'draft': return '#f8d7da';
      case 'active': return '#d1ecf1';
      default: return '#e2e3e5';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'published': return '#155724';
      case 'scheduled': return '#856404';
      case 'draft': return '#721c24';
      case 'active': return '#0c5460';
      default: return '#383d41';
    }
  }};
`;

const PlatformIcon = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  
  svg {
    color: ${props => {
      switch (props.platform) {
        case 'reddit': return '#FF4500';
        case 'twitter': return '#1DA1F2';
        case 'linkedin': return '#0077B5';
        case 'medium': return '#00ab6c';
        default: return '#666';
      }
    }};
  }
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  
  div {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: ${props => props.progress}%;
    transition: width 0.3s ease;
  }
`;

const MarketingDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/marketing/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
      } else {
        // 如果API失败，使用模拟数据
        setDashboardData({
          overview: {
            totalLeads: 23,
            totalCampaigns: 3,
            totalBacklinks: 0,
            conversionRate: 8.5
          },
          recentActivity: {
            leads: [],
            distributions: [],
            backlinks: []
          }
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // 使用模拟数据作为fallback
      setDashboardData({
        overview: {
          totalLeads: 23,
          totalCampaigns: 3,
          totalBacklinks: 0,
          conversionRate: 8.5
        },
        recentActivity: {
          leads: [],
          distributions: [],
          backlinks: []
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const statsCards = [
    {
      icon: <FaUsers />,
      value: dashboardData?.overview?.totalLeads || 0,
      label: 'Total Leads',
      change: '+12%',
      gradient: '#3498db, #2980b9'
    },
    {
      icon: <FaRocket />,
      value: dashboardData?.overview?.totalCampaigns || 0,
      label: 'Active Campaigns',
      change: '+5%',
      gradient: '#e74c3c, #c0392b'
    },
    {
      icon: <FaExternalLinkAlt />,
      value: dashboardData?.overview?.totalBacklinks || 0,
      label: 'Backlinks',
      change: '+8%',
      gradient: '#2ecc71, #27ae60'
    },
    {
      icon: <FaChartLine />,
      value: `${dashboardData?.overview?.conversionRate || 0}%`,
      label: 'Conversion Rate',
      change: '+3%',
      gradient: '#f39c12, #e67e22'
    }
  ];

  const chartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Leads Generated',
        data: [12, 19, 8, 15, 22, 18],
        borderColor: '#3498db',
        backgroundColor: 'rgba(52, 152, 219, 0.1)',
        tension: 0.4
      },
      {
        label: 'Conversions',
        data: [2, 5, 3, 8, 12, 7],
        borderColor: '#2ecc71',
        backgroundColor: 'rgba(46, 204, 113, 0.1)',
        tension: 0.4
      }
    ]
  };

  const platformPerformance = {
    labels: ['Reddit', 'Twitter', 'LinkedIn', 'Medium'],
    datasets: [{
      data: [30, 25, 25, 20],
      backgroundColor: ['#FF4500', '#1DA1F2', '#0077B5', '#00ab6c'],
      borderWidth: 0
    }]
  };

  if (loading) {
    return <div>Loading marketing dashboard...</div>;
  }

  return (
    <DashboardContainer>
      <Header>
        <h1>
          <FaRocket />
          Marketing Dashboard
        </h1>
        <ActionButtons>
          <ActionButton>
            <FaDownload />
            Export Report
          </ActionButton>
          <ActionButton primary>
            <FaPlus />
            New Campaign
          </ActionButton>
        </ActionButtons>
      </Header>

      <StatsGrid>
        {statsCards.map((stat, index) => (
          <StatCard key={index} gradient={stat.gradient}>
            <StatIcon>{stat.icon}</StatIcon>
            <StatValue>{stat.value}</StatValue>
            <StatLabel>{stat.label}</StatLabel>
            <StatChange>
              <FaArrowUp />
              {stat.change} from last month
            </StatChange>
          </StatCard>
        ))}
      </StatsGrid>

      <TabsContainer>
        <TabsList>
          <Tab active={activeTab === 'overview'} onClick={() => handleTabChange('overview')}>
            <FaChartLine />
            Overview
          </Tab>
          <Tab active={activeTab === 'leads'} onClick={() => handleTabChange('leads')}>
            <FaEnvelope />
            Email Leads
          </Tab>
          <Tab active={activeTab === 'social'} onClick={() => handleTabChange('social')}>
            <FaTwitter />
            Social Media
          </Tab>
          <Tab active={activeTab === 'forums'} onClick={() => handleTabChange('forums')}>
            <FaRedditAlien />
            Forums
          </Tab>
          <Tab active={activeTab === 'backlinks'} onClick={() => handleTabChange('backlinks')}>
            <FaExternalLinkAlt />
            Backlinks
          </Tab>
        </TabsList>

        <TabContent active={activeTab === 'overview'}>
          <ContentGrid>
            <Card>
              <CardHeader>
                <h3>
                  <FaChartLine />
                  Performance Trends
                </h3>
                <CardActions>
                  <IconButton><FaFilter /></IconButton>
                  <IconButton><FaCog /></IconButton>
                </CardActions>
              </CardHeader>
              <div style={{ height: '300px', position: 'relative' }}>
                <Line 
                  data={chartData} 
                  options={{ 
                    responsive: true, 
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true
                      }
                    }
                  }} 
                />
              </div>
            </Card>
            
            <Card>
              <CardHeader>
                <h3>
                  <FaEye />
                  Platform Distribution
                </h3>
              </CardHeader>
              <div style={{ height: '300px', position: 'relative' }}>
                <Doughnut 
                  data={platformPerformance} 
                  options={{ 
                    responsive: true, 
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom'
                      }
                    }
                  }} 
                />
              </div>
            </Card>
          </ContentGrid>

          <Card>
            <CardHeader>
              <h3>
                <FaRocket />
                Recent Campaigns
              </h3>
              <CardActions>
                <IconButton><FaSearch /></IconButton>
                <IconButton><FaFilter /></IconButton>
              </CardActions>
            </CardHeader>
            <Table>
              <thead>
                <tr>
                  <th>Campaign</th>
                  <th>Platform</th>
                  <th>Status</th>
                  <th>Leads</th>
                  <th>Conversion</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Reddit Productivity Posts</td>
                  <td>
                    <PlatformIcon platform="reddit">
                      <FaRedditAlien />
                      Reddit
                    </PlatformIcon>
                  </td>
                  <td><StatusBadge status="active">Active</StatusBadge></td>
                  <td>45</td>
                  <td>12%</td>
                  <td>
                    <IconButton><FaEye /></IconButton>
                    <IconButton><FaCog /></IconButton>
                  </td>
                </tr>
                <tr>
                  <td>Twitter Growth Strategy</td>
                  <td>
                    <PlatformIcon platform="twitter">
                      <FaTwitter />
                      Twitter
                    </PlatformIcon>
                  </td>
                  <td><StatusBadge status="scheduled">Scheduled</StatusBadge></td>
                  <td>23</td>
                  <td>8%</td>
                  <td>
                    <IconButton><FaEye /></IconButton>
                    <IconButton><FaCog /></IconButton>
                  </td>
                </tr>
              </tbody>
            </Table>
          </Card>
        </TabContent>

        <TabContent active={activeTab === 'leads'}>
          <EmailLeadsManager />
        </TabContent>

        {/* Add other tab contents here */}
        <TabContent active={activeTab === 'social'}>
          <Card>
            <CardHeader>
              <h3>
                <FaTwitter />
                Social Media Management
              </h3>
              <ActionButton primary>
                <FaPlus />
                Schedule Post
              </ActionButton>
            </CardHeader>
            <p>Social media management tools coming soon...</p>
          </Card>
        </TabContent>

        <TabContent active={activeTab === 'forums'}>
          <RedditAnalyzer />
        </TabContent>

        <TabContent active={activeTab === 'backlinks'}>
          <BacklinkManager />
        </TabContent>
      </TabsContainer>
    </DashboardContainer>
  );
};

export default MarketingDashboard;