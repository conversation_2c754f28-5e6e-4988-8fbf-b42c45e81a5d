import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaEnvelope, FaRedditAlien, FaTwitter, FaLinkedinIn, FaWordpress,
  FaChartLine, FaUsers, FaCalendarAlt, FaRocket, FaCog, FaPlay,
  FaPause, FaPlus, FaEdit, FaTrash, FaEye, FaDownload, FaUpload,
  FaFacebookF, FaInstagram, FaMedium, FaDev, FaNewspaper, FaLink
} from 'react-icons/fa';

const Container = styled.div`
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
`;

const Header = styled.div`
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    font-size: 28px;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
    font-size: 16px;
  }
`;

const TabNavigation = styled.div`
  display: flex;
  border-bottom: 2px solid #ecf0f1;
  margin-bottom: 30px;
  overflow-x: auto;
`;

const Tab = styled.button`
  padding: 15px 25px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 600;
  color: ${props => props.active ? '#3498db' : '#7f8c8d'};
  border-bottom: 3px solid ${props => props.active ? '#3498db' : 'transparent'};
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  font-size: 14px;
  
  &:hover {
    color: #3498db;
    background: rgba(52, 152, 219, 0.05);
  }
`;

const TabContent = styled.div`
  display: ${props => props.active ? 'block' : 'none'};
`;

const SectionCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 25px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: linear-gradient(135deg, ${props => props.color || '#3498db'}, ${props => props.colorSecondary || '#2980b9'});
  color: white;
  padding: 25px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  .icon {
    font-size: 32px;
    margin-bottom: 15px;
    opacity: 0.9;
  }
  
  .value {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .label {
    font-size: 14px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: ${props => props.primary ? '#3498db' : props.danger ? '#e74c3c' : '#fff'};
  color: ${props => props.primary || props.danger ? '#fff' : '#2c3e50'};
  border: 2px solid ${props => props.primary ? '#3498db' : props.danger ? '#e74c3c' : '#ddd'};
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 14px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: ${props => props.primary ? '#2980b9' : props.danger ? '#c0392b' : '#f8f9fa'};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const CampaignGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
`;

const CampaignCard = styled.div`
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
  }
  
  .title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
  }
  
  .status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
  }
  
  .description {
    color: #7f8c8d;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 15px;
  }
  
  .metric {
    text-align: center;
    
    .value {
      font-size: 20px;
      font-weight: bold;
      color: #2c3e50;
    }
    
    .label {
      font-size: 12px;
      color: #7f8c8d;
      text-transform: uppercase;
    }
  }
  
  .actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background: ${props => {
    switch (props.status) {
      case 'active': return '#d4edda';
      case 'paused': return '#fff3cd';
      case 'completed': return '#d1ecf1';
      case 'draft': return '#f8d7da';
      default: return '#e9ecef';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'active': return '#155724';
      case 'paused': return '#856404';
      case 'completed': return '#0c5460';
      case 'draft': return '#721c24';
      default: return '#495057';
    }
  }};
`;

const LoadingSpinner = styled.div`
  text-align: center;
  padding: 50px;
  
  .spinner {
    font-size: 24px;
    color: #3498db;
    margin-bottom: 16px;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
  
  .icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
  }
  
  h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }
  
  p {
    margin: 0 0 20px 0;
  }
`;

const ComprehensiveMarketingDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({
    stats: {
      emailCampaigns: 0,
      forumPosts: 0,
      socialPosts: 0,
      contentArticles: 0,
      totalLeads: 0,
      activeAutomations: 0
    },
    campaigns: {
      email: [],
      forum: [],
      social: [],
      content: []
    }
  });

  useEffect(() => {
    fetchMarketingData();
  }, []);

  const fetchMarketingData = async () => {
    setLoading(true);
    try {
      // 模拟API调用 - 实际项目中替换为真实API
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setData({
        stats: {
          emailCampaigns: 12,
          forumPosts: 45,
          socialPosts: 128,
          contentArticles: 23,
          totalLeads: 1247,
          activeAutomations: 8
        },
        campaigns: {
          email: [
            {
              id: 1,
              name: '新用户欢迎邮件序列',
              type: 'sequence',
              status: 'active',
              description: '7天自动化欢迎邮件，提高用户激活率',
              metrics: { sent: 156, opened: 89, clicked: 23 }
            },
            {
              id: 2,
              name: '产品更新通知',
              type: 'newsletter',
              status: 'draft',
              description: '每月产品功能更新和使用技巧分享',
              metrics: { sent: 0, opened: 0, clicked: 0 }
            }
          ],
          forum: [
            {
              id: 1,
              name: 'Reddit 生产力社区推广',
              platforms: ['reddit'],
              status: 'active',
              description: '在 r/productivity 等社区分享有价值的内容',
              metrics: { posts: 12, upvotes: 234, comments: 45 }
            }
          ],
          social: [
            {
              id: 1,
              name: '每日生产力小贴士',
              platforms: ['twitter', 'linkedin'],
              status: 'active',
              description: '每天发布一条生产力相关的小贴士或技巧',
              metrics: { posts: 28, likes: 567, shares: 89 }
            }
          ],
          content: [
            {
              id: 1,
              name: '生产力博客系列',
              platforms: ['wordpress', 'medium'],
              status: 'active',
              description: '深度的生产力方法和工具评测文章',
              metrics: { articles: 8, views: 2341, shares: 156 }
            }
          ]
        }
      });
    } catch (error) {
      console.error('Error fetching marketing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartCampaign = async (campaignId, type) => {
    try {
      console.log(`Starting ${type} campaign:`, campaignId);
      // 实际API调用
      await fetchMarketingData();
    } catch (error) {
      console.error('Error starting campaign:', error);
    }
  };

  const handlePauseCampaign = async (campaignId, type) => {
    try {
      console.log(`Pausing ${type} campaign:`, campaignId);
      await fetchMarketingData();
    } catch (error) {
      console.error('Error pausing campaign:', error);
    }
  };

  const tabs = [
    { key: 'overview', label: '总览', icon: FaChartLine },
    { key: 'email', label: '邮件营销', icon: FaEnvelope },
    { key: 'forum', label: '论坛发帖', icon: FaRedditAlien },
    { key: 'social', label: '社交媒体', icon: FaTwitter },
    { key: 'content', label: '内容发布', icon: FaNewspaper },
    { key: 'automation', label: '自动化', icon: FaRocket },
    { key: 'settings', label: '设置', icon: FaCog }
  ];

  const renderCampaignCard = (campaign, type) => (
    <CampaignCard key={campaign.id}>
      <div className="header">
        <h3 className="title">{campaign.name}</h3>
        <StatusBadge status={campaign.status}>{campaign.status}</StatusBadge>
      </div>
      
      <p className="description">{campaign.description}</p>
      
      <div className="metrics">
        {type === 'email' && (
          <>
            <div className="metric">
              <div className="value">{campaign.metrics.sent}</div>
              <div className="label">已发送</div>
            </div>
            <div className="metric">
              <div className="value">{campaign.metrics.opened}</div>
              <div className="label">已打开</div>
            </div>
            <div className="metric">
              <div className="value">{campaign.metrics.clicked}</div>
              <div className="label">已点击</div>
            </div>
          </>
        )}
        
        {type === 'forum' && (
          <>
            <div className="metric">
              <div className="value">{campaign.metrics.posts}</div>
              <div className="label">帖子数</div>
            </div>
            <div className="metric">
              <div className="value">{campaign.metrics.upvotes}</div>
              <div className="label">点赞数</div>
            </div>
            <div className="metric">
              <div className="value">{campaign.metrics.comments}</div>
              <div className="label">评论数</div>
            </div>
          </>
        )}
        
        {type === 'social' && (
          <>
            <div className="metric">
              <div className="value">{campaign.metrics.posts}</div>
              <div className="label">发布数</div>
            </div>
            <div className="metric">
              <div className="value">{campaign.metrics.likes}</div>
              <div className="label">点赞数</div>
            </div>
            <div className="metric">
              <div className="value">{campaign.metrics.shares}</div>
              <div className="label">分享数</div>
            </div>
          </>
        )}
        
        {type === 'content' && (
          <>
            <div className="metric">
              <div className="value">{campaign.metrics.articles}</div>
              <div className="label">文章数</div>
            </div>
            <div className="metric">
              <div className="value">{campaign.metrics.views}</div>
              <div className="label">浏览量</div>
            </div>
            <div className="metric">
              <div className="value">{campaign.metrics.shares}</div>
              <div className="label">分享数</div>
            </div>
          </>
        )}
      </div>
      
      <div className="actions">
        {campaign.status === 'active' ? (
          <ActionButton onClick={() => handlePauseCampaign(campaign.id, type)}>
            <FaPause /> 暂停
          </ActionButton>
        ) : (
          <ActionButton primary onClick={() => handleStartCampaign(campaign.id, type)}>
            <FaPlay /> 启动
          </ActionButton>
        )}
        <ActionButton>
          <FaEdit /> 编辑
        </ActionButton>
        <ActionButton>
          <FaEye /> 查看
        </ActionButton>
      </div>
    </CampaignCard>
  );

  if (loading) {
    return (
      <Container>
        <LoadingSpinner>
          <FaRocket className="spinner" />
          <p>加载营销数据中...</p>
        </LoadingSpinner>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <h1>🚀 AI Pomo 营销推广系统</h1>
        <p>全自动化的多渠道营销推广平台</p>
      </Header>

      <TabNavigation>
        {tabs.map(tab => (
          <Tab 
            key={tab.key} 
            active={activeTab === tab.key} 
            onClick={() => setActiveTab(tab.key)}
          >
            <tab.icon />
            {tab.label}
          </Tab>
        ))}
      </TabNavigation>

      {/* 总览页面 */}
      <TabContent active={activeTab === 'overview'}>
        <StatsGrid>
          <StatCard color="#3498db" colorSecondary="#2980b9">
            <div className="icon"><FaEnvelope /></div>
            <div className="value">{data.stats.emailCampaigns}</div>
            <div className="label">邮件营销活动</div>
          </StatCard>
          
          <StatCard color="#e67e22" colorSecondary="#d68910">
            <div className="icon"><FaRedditAlien /></div>
            <div className="value">{data.stats.forumPosts}</div>
            <div className="label">论坛帖子</div>
          </StatCard>
          
          <StatCard color="#9b59b6" colorSecondary="#8e44ad">
            <div className="icon"><FaTwitter /></div>
            <div className="value">{data.stats.socialPosts}</div>
            <div className="label">社交媒体发布</div>
          </StatCard>
          
          <StatCard color="#27ae60" colorSecondary="#229954">
            <div className="icon"><FaNewspaper /></div>
            <div className="value">{data.stats.contentArticles}</div>
            <div className="label">内容文章</div>
          </StatCard>
          
          <StatCard color="#f39c12" colorSecondary="#e67e22">
            <div className="icon"><FaUsers /></div>
            <div className="value">{data.stats.totalLeads}</div>
            <div className="label">总线索数</div>
          </StatCard>
          
          <StatCard color="#e74c3c" colorSecondary="#c0392b">
            <div className="icon"><FaRocket /></div>
            <div className="value">{data.stats.activeAutomations}</div>
            <div className="label">活跃自动化</div>
          </StatCard>
        </StatsGrid>

        <SectionCard>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>🎯 快速操作</h2>
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            <ActionButton primary onClick={() => setActiveTab('email')}>
              <FaPlus /> 创建邮件活动
            </ActionButton>
            <ActionButton primary onClick={() => setActiveTab('forum')}>
              <FaPlus /> 创建论坛活动
            </ActionButton>
            <ActionButton primary onClick={() => setActiveTab('social')}>
              <FaPlus /> 创建社交活动
            </ActionButton>
            <ActionButton primary onClick={() => setActiveTab('content')}>
              <FaPlus /> 创建内容活动
            </ActionButton>
          </div>
        </SectionCard>
      </TabContent>

      {/* 邮件营销页面 */}
      <TabContent active={activeTab === 'email'}>
        <SectionCard>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>📧 邮件营销活动</h2>
            <div style={{ display: 'flex', gap: '10px' }}>
              <ActionButton primary>
                <FaPlus /> 创建活动
              </ActionButton>
              <ActionButton>
                <FaDownload /> 导出数据
              </ActionButton>
            </div>
          </div>
          
          {data.campaigns.email.length > 0 ? (
            <CampaignGrid>
              {data.campaigns.email.map(campaign => renderCampaignCard(campaign, 'email'))}
            </CampaignGrid>
          ) : (
            <EmptyState>
              <FaEnvelope className="icon" />
              <h3>还没有邮件营销活动</h3>
              <p>创建你的第一个自动化邮件营销活动</p>
              <ActionButton primary>
                <FaPlus /> 立即创建
              </ActionButton>
            </EmptyState>
          )}
        </SectionCard>
      </TabContent>

      {/* 论坛发帖页面 */}
      <TabContent active={activeTab === 'forum'}>
        <SectionCard>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>💬 论坛发帖活动</h2>
            <div style={{ display: 'flex', gap: '10px' }}>
              <ActionButton primary>
                <FaPlus /> 创建活动
              </ActionButton>
              <ActionButton>
                <FaCog /> 管理账户
              </ActionButton>
            </div>
          </div>
          
          {data.campaigns.forum.length > 0 ? (
            <CampaignGrid>
              {data.campaigns.forum.map(campaign => renderCampaignCard(campaign, 'forum'))}
            </CampaignGrid>
          ) : (
            <EmptyState>
              <FaRedditAlien className="icon" />
              <h3>还没有论坛发帖活动</h3>
              <p>在Reddit、Quora等平台自动发布有价值的内容</p>
              <ActionButton primary>
                <FaPlus /> 立即创建
              </ActionButton>
            </EmptyState>
          )}
        </SectionCard>
      </TabContent>

      {/* 社交媒体页面 */}
      <TabContent active={activeTab === 'social'}>
        <SectionCard>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>📱 社交媒体活动</h2>
            <div style={{ display: 'flex', gap: '10px' }}>
              <ActionButton primary>
                <FaPlus /> 创建活动
              </ActionButton>
              <ActionButton>
                <FaLink /> 连接账户
              </ActionButton>
            </div>
          </div>
          
          {data.campaigns.social.length > 0 ? (
            <CampaignGrid>
              {data.campaigns.social.map(campaign => renderCampaignCard(campaign, 'social'))}
            </CampaignGrid>
          ) : (
            <EmptyState>
              <FaTwitter className="icon" />
              <h3>还没有社交媒体活动</h3>
              <p>在Twitter、LinkedIn、Facebook等平台自动发布内容</p>
              <ActionButton primary>
                <FaPlus /> 立即创建
              </ActionButton>
            </EmptyState>
          )}
        </SectionCard>
      </TabContent>

      {/* 内容发布页面 */}
      <TabContent active={activeTab === 'content'}>
        <SectionCard>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>📝 内容发布活动</h2>
            <div style={{ display: 'flex', gap: '10px' }}>
              <ActionButton primary>
                <FaPlus /> 创建活动
              </ActionButton>
              <ActionButton>
                <FaWordpress /> 连接平台
              </ActionButton>
            </div>
          </div>
          
          {data.campaigns.content.length > 0 ? (
            <CampaignGrid>
              {data.campaigns.content.map(campaign => renderCampaignCard(campaign, 'content'))}
            </CampaignGrid>
          ) : (
            <EmptyState>
              <FaNewspaper className="icon" />
              <h3>还没有内容发布活动</h3>
              <p>在WordPress、Medium、Dev.to等平台自动发布文章</p>
              <ActionButton primary>
                <FaPlus /> 立即创建
              </ActionButton>
            </EmptyState>
          )}
        </SectionCard>
      </TabContent>

      {/* 自动化页面 */}
      <TabContent active={activeTab === 'automation'}>
        <SectionCard>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h2>🤖 营销自动化</h2>
            <ActionButton primary>
              <FaPlus /> 创建自动化
            </ActionButton>
          </div>
          
          <EmptyState>
            <FaRocket className="icon" />
            <h3>构建营销自动化工作流</h3>
            <p>创建触发器驱动的自动化营销流程</p>
            <ActionButton primary>
              <FaPlus /> 开始构建
            </ActionButton>
          </EmptyState>
        </SectionCard>
      </TabContent>

      {/* 设置页面 */}
      <TabContent active={activeTab === 'settings'}>
        <SectionCard>
          <h2>⚙️ 营销设置</h2>
          <p>配置你的营销推广参数和连接</p>
          
          <div style={{ marginTop: '30px', display: 'grid', gap: '20px' }}>
            <div style={{ padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
              <h3>邮件服务配置</h3>
              <p>配置SMTP服务器或第三方邮件服务</p>
              <ActionButton><FaCog /> 配置邮件服务</ActionButton>
            </div>
            
            <div style={{ padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
              <h3>社交媒体账户</h3>
              <p>连接Twitter、LinkedIn、Facebook等社交媒体账户</p>
              <ActionButton><FaLink /> 管理账户连接</ActionButton>
            </div>
            
            <div style={{ padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
              <h3>发布平台</h3>
              <p>配置WordPress、Medium、Dev.to等内容发布平台</p>
              <ActionButton><FaWordpress /> 管理发布平台</ActionButton>
            </div>
          </div>
        </SectionCard>
      </TabContent>
    </Container>
  );
};

export default ComprehensiveMarketingDashboard;