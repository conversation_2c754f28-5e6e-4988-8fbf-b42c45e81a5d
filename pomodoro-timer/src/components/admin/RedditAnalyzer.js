import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FaReddit<PERSON>lien, FaS<PERSON>ch, FaChartLine, FaEye, FaCopy, 
  FaSpinner, FaArrowUp, FaComments, FaCalendarAlt, FaLightbulb,
  FaUsers, FaClock, FaFire, FaExternalLinkAlt
} from 'react-icons/fa';

const Container = styled.div`
  padding: 20px;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
`;

const AnalyzerForm = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
  }
  
  input, select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: #3498db;
    }
  }
`;

const KeywordsInput = styled.div`
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
  }
  
  input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: #3498db;
    }
  }
  
  small {
    color: #7f8c8d;
    font-size: 12px;
  }
`;

const AnalyzeButton = styled.button`
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #FF4500, #FF6500);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 69, 0, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
`;

const ResultsContainer = styled.div`
  display: grid;
  gap: 20px;
`;

const ResultCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
`;

const Metric = styled.div`
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  
  .icon {
    font-size: 24px;
    color: #FF4500;
    margin-bottom: 8px;
  }
  
  .value {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 4px;
  }
  
  .label {
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
  }
`;

const PostList = styled.div`
  display: grid;
  gap: 12px;
`;

const PostItem = styled.div`
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #FF4500;
    transform: translateY(-1px);
  }
  
  .title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .stats {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #7f8c8d;
  }
  
  .stat {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .url {
    font-size: 12px;
    color: #3498db;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const RecommendationsList = styled.div`
  display: grid;
  gap: 12px;
`;

const Recommendation = styled.div`
  background: #f8f9fa;
  border-left: 4px solid #3498db;
  padding: 16px;
  border-radius: 0 8px 8px 0;
  
  .type {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    text-transform: capitalize;
  }
  
  .suggestion {
    color: #34495e;
    margin-bottom: 4px;
  }
  
  .evidence {
    font-size: 12px;
    color: #7f8c8d;
  }
`;

const TimesList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
`;

const TimeSlot = styled.div`
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  
  .time {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
  }
  
  .posts {
    font-size: 14px;
    color: #7f8c8d;
  }
`;

const CopyButton = styled.button`
  padding: 6px 12px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  
  &:hover {
    background: #2980b9;
  }
`;

const LoadingSpinner = styled.div`
  text-align: center;
  padding: 50px;
  
  .spinner {
    font-size: 24px;
    color: #FF4500;
    margin-bottom: 16px;
  }
  
  p {
    color: #7f8c8d;
  }
`;

const RedditAnalyzer = () => {
  const [formData, setFormData] = useState({
    subreddit: 'productivity',
    keywords: 'pomodoro, time management, productivity tools',
    limit: '50'
  });
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAnalyze = async () => {
    setLoading(true);
    setAnalysis(null);
    
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟分析结果
      const mockAnalysis = {
        subreddit: formData.subreddit,
        keywords: formData.keywords.split(',').map(k => k.trim()),
        totalPosts: 45,
        analysis: {
          averageUpvotes: 127,
          averageComments: 23,
          bestPostingTimes: [
            { hour: 9, posts: 8, timeRange: '9:00-10:00' },
            { hour: 14, posts: 6, timeRange: '14:00-15:00' },
            { hour: 20, posts: 5, timeRange: '20:00-21:00' }
          ],
          topPerformingTypes: [
            ['question', { avgEngagement: 156 }],
            ['tip', { avgEngagement: 134 }],
            ['tool', { avgEngagement: 98 }]
          ],
          engagementPatterns: {
            highPerformers: [
              {
                title: 'What\'s your favorite Pomodoro app for deep work?',
                upvotes: 234,
                comments: 89,
                url: 'https://reddit.com/r/productivity/example1',
                ratio: 0.95
              },
              {
                title: 'I built a simple productivity timer, feedback welcome!',
                upvotes: 187,
                comments: 56,
                url: 'https://reddit.com/r/productivity/example2',
                ratio: 0.92
              },
              {
                title: 'How I use AI to plan my Pomodoro sessions',
                upvotes: 156,
                comments: 43,
                url: 'https://reddit.com/r/productivity/example3',
                ratio: 0.89
              }
            ],
            engagementRate: 0.78
          },
          contentRecommendations: [
            {
              type: 'title_keywords',
              suggestion: 'Use these popular keywords in titles: pomodoro, productivity, timer, focus, work',
              evidence: 'Found in 45 high-performing posts'
            },
            {
              type: 'title_length',
              suggestion: 'Optimal title length: 67 characters',
              evidence: 'Average of top performing posts'
            },
            {
              type: 'content_format',
              suggestion: 'Question-based titles perform well in this community',
              evidence: '15/20 top posts are questions'
            }
          ]
        },
        competitorAnalysis: [
          {
            name: 'Focus Keeper',
            mentions: 12,
            totalEngagement: 456,
            posts: [
              {
                title: 'Focus Keeper vs other Pomodoro apps?',
                url: 'https://reddit.com/r/productivity/focus1',
                engagement: 89
              }
            ]
          },
          {
            name: 'Forest App',
            mentions: 8,
            totalEngagement: 298,
            posts: [
              {
                title: 'Forest app gamification keeps me motivated',
                url: 'https://reddit.com/r/productivity/forest1',
                engagement: 67
              }
            ]
          }
        ],
        opportunities: [
          {
            type: 'unanswered_questions',
            description: 'Questions with low response rates',
            posts: [
              {
                title: 'Best Pomodoro technique for ADHD students?',
                url: 'https://reddit.com/r/productivity/adhd1',
                upvotes: 23,
                comments: 2
              },
              {
                title: 'How to use Pomodoro for creative work?',
                url: 'https://reddit.com/r/productivity/creative1',
                upvotes: 18,
                comments: 1
              }
            ],
            action: 'Provide helpful answers to build authority'
          }
        ]
      };
      
      setAnalysis(mockAnalysis);
    } catch (error) {
      console.error('Error analyzing subreddit:', error);
      alert('分析失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    alert('已复制到剪贴板！');
  };

  return (
    <Container>
      <Header>
        <h1>
          <FaRedditAlien />
          Reddit 社区分析工具
        </h1>
        <p>分析Reddit社区，发现营销机会和内容策略</p>
      </Header>

      <AnalyzerForm>
        <FormGrid>
          <FormGroup>
            <label>目标Subreddit</label>
            <input
              name="subreddit"
              value={formData.subreddit}
              onChange={handleInputChange}
              placeholder="例如: productivity"
            />
          </FormGroup>
          
          <FormGroup>
            <label>分析帖子数量</label>
            <select
              name="limit"
              value={formData.limit}
              onChange={handleInputChange}
            >
              <option value="25">25 篇</option>
              <option value="50">50 篇</option>
              <option value="100">100 篇</option>
            </select>
          </FormGroup>
        </FormGrid>
        
        <KeywordsInput>
          <label>关键词 (用逗号分隔)</label>
          <input
            name="keywords"
            value={formData.keywords}
            onChange={handleInputChange}
            placeholder="pomodoro, time management, productivity tools"
          />
          <small>输入与你的产品相关的关键词，工具会寻找包含这些词的帖子</small>
        </KeywordsInput>
        
        <AnalyzeButton onClick={handleAnalyze} disabled={loading}>
          {loading ? (
            <>
              <FaSpinner className="fa-spin" />
              分析中...
            </>
          ) : (
            <>
              <FaSearch />
              开始分析
            </>
          )}
        </AnalyzeButton>
      </AnalyzerForm>

      {loading && (
        <LoadingSpinner>
          <FaSpinner className="fa-spin spinner" />
          <p>正在分析 r/{formData.subreddit} 社区...</p>
          <p>这可能需要30-60秒</p>
        </LoadingSpinner>
      )}

      {analysis && (
        <ResultsContainer>
          {/* 总体指标 */}
          <ResultCard>
            <h3>
              <FaChartLine />
              r/{analysis.subreddit} 社区概览
            </h3>
            <MetricsGrid>
              <Metric>
                <div className="icon"><FaRedditAlien /></div>
                <div className="value">{analysis.totalPosts}</div>
                <div className="label">相关帖子</div>
              </Metric>
              <Metric>
                <div className="icon"><FaArrowUp /></div>
                <div className="value">{analysis.analysis.averageUpvotes}</div>
                <div className="label">平均点赞</div>
              </Metric>
              <Metric>
                <div className="icon"><FaComments /></div>
                <div className="value">{analysis.analysis.averageComments}</div>
                <div className="label">平均评论</div>
              </Metric>
              <Metric>
                <div className="icon"><FaFire /></div>
                <div className="value">{(analysis.analysis.engagementPatterns.engagementRate * 100).toFixed(0)}%</div>
                <div className="label">参与率</div>
              </Metric>
            </MetricsGrid>
          </ResultCard>

          {/* 最佳发帖时间 */}
          <ResultCard>
            <h3>
              <FaClock />
              最佳发帖时间
            </h3>
            <TimesList>
              {analysis.analysis.bestPostingTimes.map(time => (
                <TimeSlot key={time.hour}>
                  <div className="time">{time.timeRange}</div>
                  <div className="posts">{time.posts} 篇热门帖子</div>
                </TimeSlot>
              ))}
            </TimesList>
          </ResultCard>

          {/* 高表现帖子 */}
          <ResultCard>
            <h3>
              <FaFire />
              高表现帖子分析
            </h3>
            <PostList>
              {analysis.analysis.engagementPatterns.highPerformers.map((post, index) => (
                <PostItem key={index}>
                  <div className="title">
                    {post.title}
                    <CopyButton onClick={() => copyToClipboard(post.title)}>
                      <FaCopy />
                      复制
                    </CopyButton>
                  </div>
                  <div className="stats">
                    <div className="stat">
                      <FaArrowUp />
                      {post.upvotes} 点赞
                    </div>
                    <div className="stat">
                      <FaComments />
                      {post.comments} 评论
                    </div>
                    <div className="stat">
                      <FaChartLine />
                      {(post.ratio * 100).toFixed(0)}% 好评率
                    </div>
                  </div>
                  <a href={post.url} target="_blank" rel="noopener noreferrer" className="url">
                    <FaExternalLinkAlt /> 查看原帖
                  </a>
                </PostItem>
              ))}
            </PostList>
          </ResultCard>

          {/* 内容建议 */}
          <ResultCard>
            <h3>
              <FaLightbulb />
              内容策略建议
            </h3>
            <RecommendationsList>
              {analysis.analysis.contentRecommendations.map((rec, index) => (
                <Recommendation key={index}>
                  <div className="type">{rec.type.replace('_', ' ')}</div>
                  <div className="suggestion">{rec.suggestion}</div>
                  <div className="evidence">{rec.evidence}</div>
                </Recommendation>
              ))}
            </RecommendationsList>
          </ResultCard>

          {/* 竞争对手分析 */}
          <ResultCard>
            <h3>
              <FaUsers />
              竞争对手提及分析
            </h3>
            <PostList>
              {analysis.competitorAnalysis.map((competitor, index) => (
                <PostItem key={index}>
                  <div className="title">{competitor.name}</div>
                  <div className="stats">
                    <div className="stat">
                      <FaComments />
                      {competitor.mentions} 次提及
                    </div>
                    <div className="stat">
                      <FaFire />
                      {competitor.totalEngagement} 总参与度
                    </div>
                  </div>
                  {competitor.posts[0] && (
                    <a href={competitor.posts[0].url} target="_blank" rel="noopener noreferrer" className="url">
                      <FaExternalLinkAlt /> {competitor.posts[0].title}
                    </a>
                  )}
                </PostItem>
              ))}
            </PostList>
          </ResultCard>

          {/* 营销机会 */}
          <ResultCard>
            <h3>
              <FaLightbulb />
              发现的营销机会
            </h3>
            {analysis.opportunities.map((opp, index) => (
              <div key={index}>
                <h4>{opp.description}</h4>
                <p><strong>建议行动:</strong> {opp.action}</p>
                <PostList>
                  {opp.posts.map((post, postIndex) => (
                    <PostItem key={postIndex}>
                      <div className="title">{post.title}</div>
                      <div className="stats">
                        <div className="stat">
                          <FaArrowUp />
                          {post.upvotes} 点赞
                        </div>
                        <div className="stat">
                          <FaComments />
                          {post.comments} 评论
                        </div>
                      </div>
                      <a href={post.url} target="_blank" rel="noopener noreferrer" className="url">
                        <FaExternalLinkAlt /> 去回答这个问题
                      </a>
                    </PostItem>
                  ))}
                </PostList>
              </div>
            ))}
          </ResultCard>
        </ResultsContainer>
      )}
    </Container>
  );
};

export default RedditAnalyzer;