import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaPlus, FaEdit, FaTrash, FaEye, FaCheck, FaTimes, FaStar, FaFileImport, FaSpinner } from 'react-icons/fa';
import { format } from 'date-fns';
import { blogApi } from '../../services/blogApi';
import { toast } from 'react-toastify';

const BlogPostList = () => {
  const navigate = useNavigate();

  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importContent, setImportContent] = useState('');
  const [importing, setImporting] = useState(false);
  const [importResults, setImportResults] = useState(null);
  const [uploadMethod, setUploadMethod] = useState('file'); // 'file' or 'paste'

  // Fetch blog posts
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        // Get all posts including unpublished ones (admin view)
        const data = await blogApi.getAllBlogPosts();
        setPosts(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        setError('Failed to load blog posts');
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  // Handle delete confirmation
  const handleDeleteClick = (postId) => {
    setConfirmDelete(postId);
  };

  // Cancel delete
  const cancelDelete = () => {
    setConfirmDelete(null);
  };

  // Confirm delete
  const confirmDeletePost = async (postId) => {
    try {
      await blogApi.deleteBlogPost(postId);
      setPosts(posts.filter(post => post._id !== postId));
      toast.success('Blog post deleted successfully');
      setConfirmDelete(null);
    } catch (err) {
      console.error('Error deleting post:', err);
      toast.error('Failed to delete blog post');
    }
  };

  // Toggle post published status
  const togglePublished = async (post) => {
    try {
      const updatedPost = { ...post, published: !post.published };
      await blogApi.updateBlogPost(post._id, updatedPost);

      // Update local state
      setPosts(posts.map(p =>
        p._id === post._id ? { ...p, published: !p.published } : p
      ));

      toast.success(`Post ${updatedPost.published ? 'published' : 'unpublished'} successfully`);
    } catch (err) {
      console.error('Error updating post:', err);
      toast.error('Failed to update post status');
    }
  };

  // Toggle post featured status
  const toggleFeatured = async (post) => {
    try {
      const updatedPost = { ...post, featured: !post.featured };
      await blogApi.updateBlogPost(post._id, updatedPost);

      // Update local state
      setPosts(posts.map(p =>
        p._id === post._id ? { ...p, featured: !p.featured } : p
      ));

      toast.success(`Post ${updatedPost.featured ? 'featured' : 'unfeatured'} successfully`);
    } catch (err) {
      console.error('Error updating post:', err);
      toast.error('Failed to update featured status');
    }
  };

  // Parse markdown content to extract multiple articles
  const parseMarkdownContent = (markdownText) => {
    const articles = [];
    
    // Split by H1 headers (# at start of line)
    const sections = markdownText.split(/^# /gm).filter(section => section.trim());
    
    sections.forEach((section, index) => {
      const lines = section.trim().split('\n');
      if (lines.length === 0) return;
      
      const article = {
        title: '',
        description: '',
        keywords: '',
        tags: [],
        content: '',
        published: true
      };
      
      // First line is the title (after #)
      article.title = lines[0].trim();
      
      let currentSection = '';
      let inFullArticle = false;
      const contentLines = [];
      
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // Skip empty lines and metadata we don't need
        if (!line || line.startsWith('Keyword:') || line.startsWith('Word Count:') || 
            line.startsWith('Focus Keywords:') || line.startsWith('Format:') || 
            line.startsWith('Length:') || line.match(/^\*+$/)) {
          continue;
        }
        
        // Check for specific sections
        if (line.startsWith('**SEO Title:**')) {
          currentSection = 'seoTitle';
          continue;
        } else if (line.startsWith('**Meta Description:**')) {
          currentSection = 'description';
          continue;
        } else if (line.startsWith('**Focus Keywords:**')) {
          currentSection = 'keywords';
          continue;
        } else if (line.startsWith('**Tags:**')) {
          currentSection = 'tags';
          continue;
        } else if (line.startsWith('**Full Article:**') || line.includes('**Full Article:**')) {
          currentSection = 'content';
          inFullArticle = true;
          continue;
        }
        
        // Process content based on current section
        if (currentSection === 'seoTitle' && line) {
          article.title = line; // Override with SEO title
          currentSection = '';
        } else if (currentSection === 'description' && line) {
          article.description = line;
          currentSection = '';
        } else if (currentSection === 'keywords' && line) {
          article.keywords = line;
          currentSection = '';
        } else if (currentSection === 'tags' && line) {
          article.tags = line.split(',').map(tag => tag.trim());
          currentSection = '';
        } else if (inFullArticle) {
          // Everything after "Full Article:" is content
          // Stop at next article boundary
          if (line === '---' && lines[i+1] && lines[i+1].trim().startsWith('**About the Author**')) {
            // Found author section, include it
            contentLines.push(lines[i]); // Add the ---
            contentLines.push(''); // Add empty line
            
            // Add author section
            let j = i + 1;
            while (j < lines.length && !lines[j].startsWith('===============')) {
              contentLines.push(lines[j]);
              j++;
            }
            break;
          } else if (line.startsWith('================')) {
            break;
          }
          
          // Process the content line - be more careful with headers
          let processedLine = lines[i];
          
          // Only convert standalone header lines that are actually headers
          const trimmedLine = processedLine.trim();
          
          // Check if this is a real markdown header (starts with # and has space after)
          if (trimmedLine.match(/^#{1,6}\s+\S/)) {
            // This is a proper markdown header - convert level
            if (trimmedLine.startsWith('#### ')) {
              processedLine = processedLine.replace(/^(\s*)####\s+/, '$1### ');
            } else if (trimmedLine.startsWith('### ')) {
              processedLine = processedLine.replace(/^(\s*)###\s+/, '$1## ');
            } else if (trimmedLine.startsWith('## ')) {
              // Don't convert ## to # to avoid conflicts with article title
              // Keep as ## (H2)
            }
          }
          
          contentLines.push(processedLine);
        }
      }
      
      article.content = contentLines.join('\n').trim();
      
      // Skip if no title or content
      if (article.title && article.content) {
        articles.push(article);
      }
    });
    
    return articles;
  };

  // Handle file upload
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Check file type
    if (!file.name.endsWith('.md') && !file.name.endsWith('.txt')) {
      toast.error('Please upload a .md or .txt file');
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target.result;
      setImportContent(content);
      
      // Auto-start import after file is loaded
      setTimeout(() => {
        handleImportArticles(content);
      }, 100);
    };
    
    reader.onerror = () => {
      toast.error('Error reading file');
    };
    
    reader.readAsText(file);
  };

  // Handle article import
  const handleImportArticles = async (content = null) => {
    const contentToImport = content || importContent;
    
    if (!contentToImport.trim()) {
      toast.error(uploadMethod === 'file' ? 'Please upload a file' : 'Please paste the markdown content');
      return;
    }

    setImporting(true);
    
    try {
      // Parse the markdown content
      const articles = parseMarkdownContent(contentToImport);
      
      if (articles.length === 0) {
        toast.error('No valid articles found in the content');
        return;
      }

      // Import articles
      const result = await blogApi.importBlogPosts(articles);
      
      if (result.success) {
        setImportResults(result);
        toast.success(`Successfully imported ${result.successCount} articles!`);
        
        // Refresh posts list
        const data = await blogApi.getAllBlogPosts();
        setPosts(data);
        
        // Reset import content but keep modal open to show results
        setImportContent('');
      } else {
        toast.error('Import failed: ' + result.message);
      }
    } catch (err) {
      console.error('Error importing articles:', err);
      toast.error('Import failed: ' + err.message);
    } finally {
      setImporting(false);
    }
  };

  // Close import modal and reset state
  const closeImportModal = () => {
    setShowImportModal(false);
    setImportContent('');
    setImportResults(null);
    setUploadMethod('file');
  };

  return (
    <ListContainer>
      <ListHeader>
        <h1>Blog Posts</h1>

        <div style={{ display: 'flex', gap: '10px' }}>
          <ImportButton onClick={() => setShowImportModal(true)}>
            <FaFileImport />
            <span>Import Articles</span>
          </ImportButton>
          
          <CreateButton onClick={() => navigate('/admin/blog/new')}>
            <FaPlus />
            <span>Create New Post</span>
          </CreateButton>
        </div>
      </ListHeader>

      {loading ? (
        <LoadingMessage>Loading blog posts...</LoadingMessage>
      ) : error ? (
        <ErrorMessage>{error}</ErrorMessage>
      ) : posts.length === 0 ? (
        <EmptyState>
          <p>No blog posts found. Create your first post to get started.</p>
          <CreateButton onClick={() => navigate('/admin/blog/new')}>
            <FaPlus />
            <span>Create New Post</span>
          </CreateButton>
        </EmptyState>
      ) : (
        <PostsTable>
          <thead>
            <tr>
              <TableHeader>Title</TableHeader>
              <TableHeader>Category</TableHeader>
              <TableHeader>Date</TableHeader>
              <TableHeader>Status</TableHeader>
              <TableHeader>Featured</TableHeader>
              <TableHeader>Actions</TableHeader>
            </tr>
          </thead>
          <tbody>
            {posts.map(post => (
              <TableRow key={post._id}>
                <TableCell>
                  <PostTitle>{post.title}</PostTitle>
                </TableCell>
                <TableCell>
                  <CategoryBadge>{post.category}</CategoryBadge>
                </TableCell>
                <TableCell>
                  <DateInfo>
                    {format(new Date(post.createdAt), 'MMM d, yyyy')}
                  </DateInfo>
                </TableCell>
                <TableCell>
                  <StatusToggle
                    onClick={() => togglePublished(post)}
                    $isPublished={post.published}
                    title={post.published ? 'Unpublish post' : 'Publish post'}
                  >
                    {post.published ? (
                      <>
                        <FaCheck />
                        <span>Published</span>
                      </>
                    ) : (
                      <>
                        <FaTimes />
                        <span>Draft</span>
                      </>
                    )}
                  </StatusToggle>
                </TableCell>
                <TableCell>
                  <FeaturedToggle
                    onClick={() => toggleFeatured(post)}
                    $isFeatured={post.featured}
                    title={post.featured ? 'Remove from featured' : 'Add to featured'}
                  >
                    <FaStar />
                  </FeaturedToggle>
                </TableCell>
                <TableCell>
                  <ActionButtons>
                    <ActionButton
                      onClick={() => window.open(`/blog/${post.slug}`, '_blank')}
                      title="View post"
                    >
                      <FaEye />
                    </ActionButton>
                    <ActionButton
                      onClick={() => navigate(`/admin/blog/edit/${post._id}`)}
                      title="Edit post"
                    >
                      <FaEdit />
                    </ActionButton>
                    {confirmDelete === post._id ? (
                      <ConfirmDeleteContainer>
                        <ConfirmText>Are you sure?</ConfirmText>
                        <ConfirmButton onClick={() => confirmDeletePost(post._id)}>
                          Yes
                        </ConfirmButton>
                        <CancelButton onClick={cancelDelete}>
                          No
                        </CancelButton>
                      </ConfirmDeleteContainer>
                    ) : (
                      <DeleteButton
                        onClick={() => handleDeleteClick(post._id)}
                        title="Delete post"
                      >
                        <FaTrash />
                      </DeleteButton>
                    )}
                  </ActionButtons>
                </TableCell>
              </TableRow>
            ))}
          </tbody>
        </PostsTable>
      )}

      {/* Import Articles Modal */}
      {showImportModal && (
        <Modal onClick={closeImportModal} style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <h2><FaFileImport /> Import Articles</h2>
              <CloseButton onClick={closeImportModal}>
                <FaTimes />
              </CloseButton>
            </ModalHeader>

            {!importResults ? (
              <>
                <UploadMethodSelector>
                  <MethodButton 
                    active={uploadMethod === 'file'} 
                    onClick={() => setUploadMethod('file')}
                  >
                    <FaFileImport /> Upload File
                  </MethodButton>
                  <MethodButton 
                    active={uploadMethod === 'paste'} 
                    onClick={() => setUploadMethod('paste')}
                  >
                    <FaEdit /> Paste Content
                  </MethodButton>
                </UploadMethodSelector>

                {uploadMethod === 'file' ? (
                  <>
                    <ModalDescription>
                      Upload a .md or .txt file containing your articles. Each article should start with <code># Title</code>.
                    </ModalDescription>
                    
                    <FileUploadArea>
                      <input
                        type="file"
                        accept=".md,.txt"
                        onChange={handleFileUpload}
                        style={{ display: 'none' }}
                        id="file-upload"
                        disabled={importing}
                      />
                      <label htmlFor="file-upload">
                        <UploadButton disabled={importing}>
                          {importing ? (
                            <>
                              <FaSpinner style={{ animation: 'spin 1s linear infinite' }} />
                              Processing...
                            </>
                          ) : (
                            <>
                              <FaFileImport />
                              Choose File (.md or .txt)
                            </>
                          )}
                        </UploadButton>
                      </label>
                      <UploadHint>
                        Maximum file size: 5MB
                      </UploadHint>
                    </FileUploadArea>
                  </>
                ) : (
                  <>
                    <ModalDescription>
                      Paste your markdown content below. Each article should start with <code># Title</code> and include the specific sections as shown in the example.
                    </ModalDescription>
                    
                    <ExampleFormat>
                      <strong>Example format:</strong>
                      <pre>{`# How to Focus Better: A Science-Backed Path to Flow State

Keyword: how to focus better
Word Count: 1928

**SEO Title:**
How to Focus Better: The Science of Entering a Flow State

**Meta Description:**
Tired of distraction? This guide shows you how to focus better.

**Focus Keywords:**
how to focus better, increase concentration, flow state

**Tags:**
focus, productivity, performance, flow state

**Full Article:**
Your article content here...

# Second Article Title
...`}</pre>
                    </ExampleFormat>

                    <ImportTextarea
                      value={importContent}
                      onChange={(e) => setImportContent(e.target.value)}
                      placeholder="Paste your markdown content here..."
                      rows="15"
                    />

                    <ModalActions>
                      <CancelButton onClick={closeImportModal}>
                        Cancel
                      </CancelButton>
                      <ImportSubmitButton 
                        onClick={() => handleImportArticles()} 
                        disabled={importing || !importContent.trim()}
                      >
                        {importing ? (
                          <>
                            <FaSpinner style={{ animation: 'spin 1s linear infinite' }} />
                            Importing...
                          </>
                        ) : (
                          <>
                            <FaFileImport />
                            Import Articles
                          </>
                        )}
                      </ImportSubmitButton>
                    </ModalActions>
                  </>
                )}

                {uploadMethod === 'file' && (
                  <ModalActions>
                    <CancelButton onClick={closeImportModal}>
                      Cancel
                    </CancelButton>
                  </ModalActions>
                )}
              </>
            ) : (
              <>
                <ImportResults>
                  <h3>Import Results</h3>
                  <ResultsSummary>
                    <div className="success">
                      <FaCheck /> {importResults.successCount} articles imported successfully
                    </div>
                    {importResults.errorCount > 0 && (
                      <div className="error">
                        <FaTimes /> {importResults.errorCount} articles failed to import
                      </div>
                    )}
                  </ResultsSummary>

                  {importResults.imported && importResults.imported.length > 0 && (
                    <div>
                      <h4>Successfully Imported Articles:</h4>
                      <ArticlesList>
                        {importResults.imported.map((article, index) => (
                          <ArticleItem key={index}>
                            <div className="article-info">
                              <strong>{article.title}</strong>
                              <div className="article-meta">
                                Category: {article.category} | Published: {article.published ? 'Yes' : 'No'}
                              </div>
                              <div className="article-url">
                                <a href={`https://ai-pomo.com${article.url}`} target="_blank" rel="noopener noreferrer">
                                  https://ai-pomo.com{article.url}
                                </a>
                              </div>
                            </div>
                          </ArticleItem>
                        ))}
                      </ArticlesList>
                    </div>
                  )}

                  {importResults.errors && importResults.errors.length > 0 && (
                    <div>
                      <h4>Import Errors:</h4>
                      <ErrorsList>
                        {importResults.errors.map((error, index) => (
                          <ErrorItem key={index}>
                            <strong>{error.title}</strong>: {error.error}
                          </ErrorItem>
                        ))}
                      </ErrorsList>
                    </div>
                  )}
                </ImportResults>

                <ModalActions>
                  <ImportSubmitButton onClick={closeImportModal}>
                    Close
                  </ImportSubmitButton>
                </ModalActions>
              </>
            )}
          </ModalContent>
        </Modal>
      )}
    </ListContainer>
  );
};

// Styled components
const ListContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2.5rem 1.5rem;
`;

const ListHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid ${props => props.theme['--border-color']};

  h1 {
    font-size: 1.85rem;
    color: ${props => props.theme['--text-color']};
    margin: 0;
    font-weight: 700;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
`;

const CreateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.6rem;
  background-color: ${props => props.theme['--primary-color']};
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.7rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(217, 85, 80, 0.2);

  &:hover {
    background-color: ${props => props.theme['--primary-color-dark']};
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(217, 85, 80, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(217, 85, 80, 0.2);
  }
`;

const ImportButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.6rem;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.7rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(39, 174, 96, 0.2);

  &:hover {
    background-color: #229954;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);
  }
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 3rem 0;
  font-size: 1.1rem;
  color: ${props => props.theme['--text-secondary']};
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 2rem;
  background-color: #fff0f0;
  border-radius: 4px;
  color: #d32f2f;
  margin-bottom: 2rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 0;

  p {
    font-size: 1.1rem;
    color: ${props => props.theme['--text-secondary']};
    margin-bottom: 1.5rem;
  }
`;

const PostsTable = styled.table`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: ${props => props.theme['--card-bg']};
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
`;

const TableHeader = styled.th`
  text-align: left;
  padding: 1.2rem 1rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: ${props => props.theme['--text-secondary']};
  background-color: ${props => props.theme['--table-header-bg'] || '#f8f9fa'};
  border-bottom: 1px solid ${props => props.theme['--border-color']};

  &:first-child {
    padding-left: 1.5rem;
  }

  &:last-child {
    padding-right: 1.5rem;
    text-align: center;
  }
`;

const TableRow = styled.tr`
  &:not(:last-child) {
    border-bottom: 1px solid ${props => props.theme['--border-color']};
  }

  &:hover {
    background-color: ${props => props.theme['--hover-bg']};
  }

  &:last-child td:first-child {
    border-bottom-left-radius: 12px;
  }

  &:last-child td:last-child {
    border-bottom-right-radius: 12px;
  }
`;

const TableCell = styled.td`
  padding: 1rem;
  vertical-align: middle;

  &:first-child {
    padding-left: 1.5rem;
  }

  &:last-child {
    padding-right: 1.5rem;
  }
`;

const PostTitle = styled.div`
  font-weight: 600;
  color: ${props => props.theme['--text-color']};
`;

const CategoryBadge = styled.span`
  display: inline-block;
  background-color: ${props => props.theme['--tag-bg'] || '#f0f0f0'};
  color: ${props => props.theme['--text-secondary']};
  font-size: 0.8rem;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
`;

const DateInfo = styled.span`
  font-size: 0.9rem;
  color: ${props => props.theme['--text-tertiary']};
`;

const StatusToggle = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: ${props => props.$isPublished ? '#e6f7e6' : '#f7e6e6'};
  color: ${props => props.$isPublished ? '#2e7d32' : '#c62828'};
  border: none;
  border-radius: 4px;
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.$isPublished ? '#d4f0d4' : '#f0d4d4'};
  }
`;

const FeaturedToggle = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: transparent;
  color: ${props => props.$isFeatured ? '#f9a825' : props.theme['--text-tertiary']};
  border: 1px solid ${props => props.$isFeatured ? '#f9a825' : props.theme['--border-color']};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme['--hover-bg']};
    border-color: ${props => props.$isFeatured ? '#f9a825' : props.theme['--text-tertiary']};
  }
`;

const ActionButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ActionButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: transparent;
  color: ${props => props.theme['--text-tertiary']};
  border: 1px solid ${props => props.theme['--border-color']};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme['--hover-bg']};
    border-color: ${props => props.theme['--text-tertiary']};
    color: ${props => props.theme['--text-color']};
  }
`;

const DeleteButton = styled(ActionButton)`
  &:hover {
    background-color: #fff0f0;
    border-color: #d32f2f;
    color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.2);
  }
`;

const ConfirmDeleteContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.6rem;
  background-color: #fff8f8;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(211, 47, 47, 0.2);
`;

const ConfirmText = styled.span`
  font-size: 0.85rem;
  font-weight: 500;
  color: #d32f2f;
`;

const ConfirmButton = styled.button`
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.35rem 0.7rem;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(211, 47, 47, 0.2);

  &:hover {
    background-color: #b71c1c;
    transform: translateY(-1px);
    box-shadow: 0 3px 5px rgba(211, 47, 47, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
`;

const CancelButton = styled.button`
  background-color: white;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.35rem 0.7rem;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    color: #333;
    border-color: #ccc;
  }
`;

// Import Modal Styles
const Modal = styled.div`
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 999999 !important;
  padding: 20px !important;
  overflow-y: auto !important;
`;

const ModalContent = styled.div`
  background: white !important;
  border-radius: 12px !important;
  padding: 0 !important;
  max-width: 800px !important;
  width: 90% !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  position: relative !important;
  margin: 0 !important;
  
  /* Ensure content doesn't get cut off */
  @media (max-height: 600px) {
    max-height: 95vh !important;
  }
  
  @media (max-width: 900px) {
    width: 95% !important;
    max-width: none !important;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #eee;
  
  h2 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f5f5f5;
    color: #333;
  }
`;

const ModalDescription = styled.p`
  margin: 20px 25px;
  color: #666;
  line-height: 1.5;
  
  code {
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
  }
`;

const ExampleFormat = styled.div`
  margin: 15px 25px 20px;
  
  pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    font-size: 0.85rem;
    color: #495057;
    overflow-x: auto;
    margin: 10px 0 0 0;
  }
`;

const ImportTextarea = styled.textarea`
  width: calc(100% - 50px);
  margin: 0 25px 20px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: vertical;
  min-height: 300px;
  
  &:focus {
    outline: none;
    border-color: #27ae60;
    box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
  }
`;

const ModalActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px 25px;
  border-top: 1px solid #eee;
`;

const ImportSubmitButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background-color: #229954;
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const ImportResults = styled.div`
  padding: 25px;
  
  h3, h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
  }
  
  h4 {
    margin-top: 25px;
    font-size: 1.1rem;
  }
`;

const ResultsSummary = styled.div`
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  
  .success, .error {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border-radius: 6px;
    font-weight: 500;
  }
  
  .success {
    background: #e8f5e8;
    color: #2e7d32;
  }
  
  .error {
    background: #ffebee;
    color: #c62828;
  }
`;

const ArticlesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
`;

const ArticleItem = styled.div`
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  
  .article-info strong {
    color: #2c3e50;
    font-size: 1.05rem;
  }
  
  .article-meta {
    color: #666;
    font-size: 0.9rem;
    margin: 5px 0;
  }
  
  .article-url {
    margin-top: 8px;
    
    a {
      color: #27ae60;
      text-decoration: none;
      font-size: 0.9rem;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
`;

const ErrorsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const ErrorItem = styled.div`
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  padding: 12px;
  color: #c53030;
  font-size: 0.9rem;
`;

// New Upload Method Styles
const UploadMethodSelector = styled.div`
  display: flex;
  gap: 10px;
  margin: 20px 25px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
`;

const MethodButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: 2px solid ${props => props.active ? '#27ae60' : '#e9ecef'};
  background: ${props => props.active ? '#27ae60' : 'white'};
  color: ${props => props.active ? 'white' : '#666'};
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #27ae60;
    color: ${props => props.active ? 'white' : '#27ae60'};
  }
`;

const FileUploadArea = styled.div`
  margin: 20px 25px;
  text-align: center;
  padding: 40px 20px;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
  
  label {
    cursor: pointer;
  }
`;

const UploadButton = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: #27ae60;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  opacity: ${props => props.disabled ? 0.6 : 1};
  
  &:hover {
    background: ${props => props.disabled ? '#27ae60' : '#229954'};
    transform: ${props => props.disabled ? 'none' : 'translateY(-1px)'};
  }
`;

const UploadHint = styled.p`
  margin: 15px 0 0 0;
  color: #666;
  font-size: 0.9rem;
`;

export default BlogPostList;
