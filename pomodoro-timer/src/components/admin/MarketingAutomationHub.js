import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaRobot, FaPlay, FaPause, FaStop, FaPlus, FaEdit, FaTrash,
  FaEnvelope, FaFacebook, FaTwitter, FaLinkedin, FaReddit,
  FaMedium, FaWordpress, FaLink, FaUsers, FaCalendarAlt,
  FaChartLine, FaCheckCircle, FaExclamationTriangle, FaSpinner,
  FaCog, FaBullhorn, FaGlobe, FaRocket, FaBullseye, FaBrain,
  FaFilter, FaSearch, FaEye, FaDownload, FaUpload, FaClock
} from 'react-icons/fa';

const Container = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
`;

const TabNavigation = styled.div`
  display: flex;
  margin-bottom: 30px;
  border-bottom: 2px solid #ecf0f1;
`;

const Tab = styled.button`
  padding: 12px 24px;
  background: none;
  border: none;
  cursor: pointer;
  color: ${props => props.active ? '#3498db' : '#7f8c8d'};
  border-bottom: 2px solid ${props => props.active ? '#3498db' : 'transparent'};
  font-weight: ${props => props.active ? '600' : '400'};
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    color: #3498db;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: ${props => props.gradient || 'linear-gradient(135deg, #3498db, #2980b9)'};
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  .icon {
    font-size: 24px;
    margin-bottom: 10px;
    opacity: 0.9;
  }
  
  .value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .label {
    font-size: 12px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const Card = styled.div`
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 25px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: ${props => 
    props.primary ? '#3498db' : 
    props.danger ? '#e74c3c' : 
    props.success ? '#27ae60' : 
    props.warning ? '#f39c12' : '#fff'
  };
  color: ${props => (props.primary || props.danger || props.success || props.warning) ? '#fff' : '#2c3e50'};
  border: 2px solid ${props => 
    props.primary ? '#3498db' : 
    props.danger ? '#e74c3c' : 
    props.success ? '#27ae60' : 
    props.warning ? '#f39c12' : '#ddd'
  };
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 14px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const WorkflowGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const WorkflowCard = styled.div`
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    
    .title {
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
  }
  
  .description {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 15px;
  }
  
  .channels {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
    
    .channel {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      
      &.email { background: #ea4335; }
      &.facebook { background: #1877f2; }
      &.twitter { background: #1da1f2; }
      &.linkedin { background: #0a66c2; }
      &.reddit { background: #ff4500; }
      &.medium { background: #00ab6c; }
      &.wordpress { background: #21759b; }
      &.backlinks { background: #9b59b6; }
    }
  }
  
  .stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #7f8c8d;
    margin-bottom: 15px;
    
    span {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
  
  .actions {
    display: flex;
    gap: 8px;
  }
`;

const StatusBadge = styled.span`
  background: ${props => {
    switch (props.status) {
      case 'active': return '#e8f5e8';
      case 'paused': return '#fff3e0';
      case 'completed': return '#e3f2fd';
      case 'draft': return '#f5f5f5';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'active': return '#388e3c';
      case 'paused': return '#f57c00';
      case 'completed': return '#1976d2';
      case 'draft': return '#616161';
      default: return '#616161';
    }
  }};
`;

const FlowBuilder = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 25px;
  
  .builder-header {
    text-align: center;
    margin-bottom: 30px;
    
    h3 {
      color: #2c3e50;
      margin: 0 0 10px 0;
    }
    
    p {
      color: #7f8c8d;
      margin: 0;
    }
  }
  
  .flow-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
  }
  
  .flow-step {
    background: white;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    min-width: 150px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #3498db;
    }
    
    .step-icon {
      font-size: 32px;
      color: #3498db;
      margin-bottom: 10px;
    }
    
    .step-title {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 5px;
    }
    
    .step-desc {
      font-size: 12px;
      color: #7f8c8d;
    }
  }
  
  .flow-arrow {
    font-size: 20px;
    color: #bdc3c7;
  }
`;

const TemplateGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const TemplateCard = styled.div`
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .template-icon {
    font-size: 48px;
    color: #3498db;
    margin-bottom: 15px;
  }
  
  .template-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
  }
  
  .template-desc {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
  }
`;

const MarketingAutomationHub = () => {
  const [activeTab, setActiveTab] = useState('workflows');
  const [loading, setLoading] = useState(false);
  
  const [stats, setStats] = useState({
    activeWorkflows: 0,
    totalLeads: 0,
    conversionRate: 0,
    channelsActive: 0,
    campaignsRunning: 0,
    automationSavings: 0
  });
  
  const [workflows, setWorkflows] = useState([]);

  // 模拟数据
  useEffect(() => {
    setStats({
      activeWorkflows: 5,
      totalLeads: 1247,
      conversionRate: 8.5,
      channelsActive: 7,
      campaignsRunning: 12,
      automationSavings: 85
    });
    
    setWorkflows([
      {
        id: 1,
        name: 'New User Onboarding Workflow',
        description: 'Automated onboarding and nurturing process for new user registrations',
        status: 'active',
        channels: ['email', 'facebook', 'twitter'],
        triggers: 156,
        conversions: 23,
        lastRun: new Date(Date.now() - 3600000)
      },
      {
        id: 2,
        name: 'Blog Post Promotion Workflow',
        description: 'Automatically promote newly published blog posts across multiple platforms',
        status: 'active',
        channels: ['twitter', 'linkedin', 'reddit', 'medium'],
        triggers: 89,
        conversions: 12,
        lastRun: new Date(Date.now() - 7200000)
      },
      {
        id: 3,
        name: 'Backlink Building Automation',
        description: 'Automatically discover, contact, and follow up on backlink building opportunities',
        status: 'paused',
        channels: ['backlinks', 'email'],
        triggers: 45,
        conversions: 8,
        lastRun: new Date(Date.now() - 86400000)
      },
      {
        id: 4,
        name: 'User Churn Recovery Workflow',
        description: 'Automatically identify and recover users who are about to churn',
        status: 'active',
        channels: ['email', 'facebook'],
        triggers: 67,
        conversions: 15,
        lastRun: new Date(Date.now() - 1800000)
      },
      {
        id: 5,
        name: 'Product Launch Marketing Automation',
        description: 'Omnichannel marketing promotion when new features are launched',
        status: 'draft',
        channels: ['email', 'twitter', 'linkedin', 'wordpress'],
        triggers: 0,
        conversions: 0,
        lastRun: null
      }
    ]);
  }, []);

  const getChannelIcon = (channel) => {
    const icons = {
      email: <FaEnvelope />,
      facebook: <FaFacebook />,
      twitter: <FaTwitter />,
      linkedin: <FaLinkedin />,
      reddit: <FaReddit />,
      medium: <FaMedium />,
      wordpress: <FaWordpress />,
      backlinks: <FaLink />
    };
    return icons[channel] || <FaGlobe />;
  };

  const renderWorkflowsTab = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>🤖 Marketing Automation Workflows</h2>
        <ActionButton primary>
          <FaPlus /> Create Workflow
        </ActionButton>
      </div>

      <WorkflowGrid>
        {workflows.map(workflow => (
          <WorkflowCard key={workflow.id}>
            <div className="header">
              <div className="title">
                <FaRobot />
                {workflow.name}
              </div>
              <StatusBadge status={workflow.status} className="status">
                {workflow.status}
              </StatusBadge>
            </div>
            
            <div className="description">
              {workflow.description}
            </div>
            
            <div className="channels">
              {workflow.channels.map(channel => (
                <div key={channel} className={`channel ${channel}`}>
                  {getChannelIcon(channel)}
                </div>
              ))}
            </div>
            
            <div className="stats">
              <span><FaRocket /> {workflow.triggers} triggers</span>
              <span><FaBullseye /> {workflow.conversions} conversions</span>
              <span><FaClock /> {workflow.lastRun ? `${Math.floor((Date.now() - workflow.lastRun) / 3600000)}h ago` : 'Not run'}</span>
            </div>
            
            <div className="actions">
              <ActionButton success disabled={workflow.status === 'active'}>
                <FaPlay />
              </ActionButton>
              <ActionButton warning disabled={workflow.status !== 'active'}>
                <FaPause />
              </ActionButton>
              <ActionButton>
                <FaEdit />
              </ActionButton>
              <ActionButton>
                <FaEye />
              </ActionButton>
              <ActionButton danger>
                <FaTrash />
              </ActionButton>
            </div>
          </WorkflowCard>
        ))}
      </WorkflowGrid>
    </div>
  );

  const renderBuilderTab = () => (
    <div>
      <h2>🔧 Workflow Builder</h2>
      
      <FlowBuilder>
        <div className="builder-header">
          <h3>Drag & Drop Workflow Designer</h3>
          <p>Build complex marketing automation workflows through simple drag and drop operations</p>
        </div>
        
        <div className="flow-steps">
          <div className="flow-step">
            <div className="step-icon"><FaUsers /></div>
            <div className="step-title">Trigger</div>
            <div className="step-desc">User behavior trigger</div>
          </div>
          
          <div className="flow-arrow">→</div>
          
          <div className="flow-step">
            <div className="step-icon"><FaBrain /></div>
            <div className="step-title">Condition</div>
            <div className="step-desc">Smart branching logic</div>
          </div>
          
          <div className="flow-arrow">→</div>
          
          <div className="flow-step">
            <div className="step-icon"><FaEnvelope /></div>
            <div className="step-title">Action</div>
            <div className="step-desc">Send email/Publish content</div>
          </div>
          
          <div className="flow-arrow">→</div>
          
          <div className="flow-step">
            <div className="step-icon"><FaClock /></div>
            <div className="step-title">Delay</div>
            <div className="step-desc">Time interval control</div>
          </div>
          
          <div className="flow-arrow">→</div>
          
          <div className="flow-step">
            <div className="step-icon"><FaChartLine /></div>
            <div className="step-title">Tracking</div>
            <div className="step-desc">Conversion analysis</div>
          </div>
        </div>
        
        <div style={{ textAlign: 'center', marginTop: '30px' }}>
          <ActionButton primary>
            <FaPlus /> Start Building Workflow
          </ActionButton>
        </div>
      </FlowBuilder>
    </div>
  );

  const renderTemplatesTab = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>📋 Workflow Template Library</h2>
        <div style={{ display: 'flex', gap: '10px' }}>
          <ActionButton>
            <FaDownload /> Import Template
          </ActionButton>
          <ActionButton>
            <FaUpload /> Export Workflow
          </ActionButton>
        </div>
      </div>

      <TemplateGrid>
        <TemplateCard>
          <div className="template-icon"><FaUsers /></div>
          <div className="template-title">New User Welcome Series</div>
          <div className="template-desc">
            Automatically send welcome email series to newly registered users, introducing product features and usage tips to increase user engagement.
          </div>
          <ActionButton primary>Use Template</ActionButton>
        </TemplateCard>

        <TemplateCard>
          <div className="template-icon"><FaBullhorn /></div>
          <div className="template-title">Content Promotion Automation</div>
          <div className="template-desc">
            After new blog posts are published, automatically promote them on social media platforms, including title optimization and optimal posting times.
          </div>
          <ActionButton primary>Use Template</ActionButton>
        </TemplateCard>

        <TemplateCard>
          <div className="template-icon"><FaLink /></div>
          <div className="template-title">Backlink Building Workflow</div>
          <div className="template-desc">
            Automatically discover backlink opportunities, send personalized outreach emails, follow up on replies and manage the entire backlink building process.
          </div>
          <ActionButton primary>Use Template</ActionButton>
        </TemplateCard>

        <TemplateCard>
          <div className="template-icon"><FaRocket /></div>
          <div className="template-title">Product Launch Marketing</div>
          <div className="template-desc">
            Complete marketing process when new features or products are launched, including pre-launch, launch and follow-up promotional activities.
          </div>
          <ActionButton primary>Use Template</ActionButton>
        </TemplateCard>

        <TemplateCard>
          <div className="template-icon"><FaChartLine /></div>
          <div className="template-title">User Churn Recovery</div>
          <div className="template-desc">
            Identify inactive users, send personalized recovery emails, provide special offers and product update information.
          </div>
          <ActionButton primary>Use Template</ActionButton>
        </TemplateCard>

        <TemplateCard>
          <div className="template-icon"><FaBullseye /></div>
          <div className="template-title">Precision Remarketing</div>
          <div className="template-desc">
            Based on user behavior data, conduct precise remarketing campaigns to improve conversion rates and user lifetime value.
          </div>
          <ActionButton primary>Use Template</ActionButton>
        </TemplateCard>
      </TemplateGrid>
    </div>
  );

  const renderAnalyticsTab = () => (
    <div style={{ textAlign: 'center', padding: '40px', color: '#7f8c8d' }}>
      <FaChartLine style={{ fontSize: '48px', marginBottom: '16px' }} />
      <h3>Automation Performance Analysis</h3>
      <p>Detailed workflow performance analysis, ROI calculation and optimization recommendations coming soon</p>
      <div style={{ marginTop: '20px' }}>
        <ActionButton primary>
          <FaRocket /> View Demo
        </ActionButton>
      </div>
    </div>
  );

  return (
    <Container>
      <Header>
        <h1>
          <FaRobot />
          Marketing Automation Hub
        </h1>
        <p>Intelligent marketing workflows that let your marketing campaigns run automatically, improving efficiency and conversion rates</p>
      </Header>

      <StatsGrid>
        <StatCard gradient="linear-gradient(135deg, #3498db, #2980b9)">
          <div className="icon"><FaRobot /></div>
          <div className="value">{stats.activeWorkflows}</div>
          <div className="label">Active Workflows</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #27ae60, #229954)">
          <div className="icon"><FaUsers /></div>
          <div className="value">{stats.totalLeads}</div>
          <div className="label">Total Leads</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #f39c12, #e67e22)">
          <div className="icon"><FaBullseye /></div>
          <div className="value">{stats.conversionRate}%</div>
          <div className="label">Conversion Rate</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #9b59b6, #8e44ad)">
          <div className="icon"><FaGlobe /></div>
          <div className="value">{stats.channelsActive}</div>
          <div className="label">Active Channels</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #e74c3c, #c0392b)">
          <div className="icon"><FaBullhorn /></div>
          <div className="value">{stats.campaignsRunning}</div>
          <div className="label">Running Campaigns</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #1abc9c, #16a085)">
          <div className="icon"><FaClock /></div>
          <div className="value">{stats.automationSavings}%</div>
          <div className="label">Time Saved</div>
        </StatCard>
      </StatsGrid>

      <Card>
        <TabNavigation>
          <Tab 
            active={activeTab === 'workflows'} 
            onClick={() => setActiveTab('workflows')}
          >
            <FaRobot /> Workflow Management
          </Tab>
          <Tab 
            active={activeTab === 'builder'} 
            onClick={() => setActiveTab('builder')}
          >
            <FaCog /> Workflow Builder
          </Tab>
          <Tab 
            active={activeTab === 'templates'} 
            onClick={() => setActiveTab('templates')}
          >
            <FaBullhorn /> Template Library
          </Tab>
          <Tab 
            active={activeTab === 'analytics'} 
            onClick={() => setActiveTab('analytics')}
          >
            <FaChartLine /> Performance Analysis
          </Tab>
        </TabNavigation>

        {activeTab === 'workflows' && renderWorkflowsTab()}
        {activeTab === 'builder' && renderBuilderTab()}
        {activeTab === 'templates' && renderTemplatesTab()}
        {activeTab === 'analytics' && renderAnalyticsTab()}
      </Card>

      {/* 快速启动卡片 */}
      <Card>
        <h3>🚀 Quick Start</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
          <div style={{ padding: '15px', background: '#e8f5e8', borderRadius: '8px' }}>
            <h4>📧 Email Marketing Automation</h4>
            <p>Set up email sequences based on user behavior</p>
            <ActionButton success><FaPlay /> Start Setup</ActionButton>
          </div>
          
          <div style={{ padding: '15px', background: '#e3f2fd', borderRadius: '8px' }}>
            <h4>📱 Social Media Auto-posting</h4>
            <p>Schedule content publishing to multiple social platforms</p>
            <ActionButton primary><FaPlay /> Start Setup</ActionButton>
          </div>
          
          <div style={{ padding: '15px', background: '#f3e5f5', borderRadius: '8px' }}>
            <h4>🔗 Backlink Building Automation</h4>
            <p>Automate backlink discovery and outreach processes</p>
            <ActionButton warning><FaPlay /> Start Setup</ActionButton>
          </div>
          
          <div style={{ padding: '15px', background: '#fff3e0', borderRadius: '8px' }}>
            <h4>📊 Data Sync Automation</h4>
            <p>Automatically sync data and reports across platforms</p>
            <ActionButton><FaPlay /> Start Setup</ActionButton>
          </div>
        </div>
      </Card>
    </Container>
  );
};

export default MarketingAutomationHub;