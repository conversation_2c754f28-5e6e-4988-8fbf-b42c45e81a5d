import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaEnvelope, FaUsers, FaChartLine, FaPlay, FaPause, FaDownload,
  FaEye, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaSpinner,
  FaExclamation<PERSON>riangle, FaBell, FaFilter, FaSearch
} from 'react-icons/fa';
import { adminApi } from '../../services/apiService';

const API_URL = process.env.REACT_APP_API_URL || 'https://ai-pomo-production.up.railway.app';

const Container = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: ${props => props.gradient || 'linear-gradient(135deg, #3498db, #2980b9)'};
  color: white;
  padding: 25px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  .icon {
    font-size: 32px;
    margin-bottom: 15px;
    opacity: 0.9;
  }
  
  .value {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .label {
    font-size: 14px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .change {
    font-size: 12px;
    margin-top: 5px;
    opacity: 0.8;
  }
`;

const Card = styled.div`
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 25px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: ${props => 
    props.primary ? '#3498db' : 
    props.danger ? '#e74c3c' : 
    props.success ? '#27ae60' : 
    props.warning ? '#f39c12' : '#fff'
  };
  color: ${props => (props.primary || props.danger || props.success || props.warning) ? '#fff' : '#2c3e50'};
  border: 2px solid ${props => 
    props.primary ? '#3498db' : 
    props.danger ? '#e74c3c' : 
    props.success ? '#27ae60' : 
    props.warning ? '#f39c12' : '#ddd'
  };
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const UserTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  
  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
  }
  
  th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
  }
  
  tr:hover {
    background: #f8f9fa;
  }
  
  .checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
  }
`;

const FilterBar = styled.div`
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  
  select, input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: ${props => {
    switch (props.status) {
      case 'active': return '#d4edda';
      case 'inactive': return '#f8d7da';
      case 'pending': return '#fff3cd';
      default: return '#e9ecef';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'active': return '#155724';
      case 'inactive': return '#721c24';
      case 'pending': return '#856404';
      default: return '#495057';
    }
  }};
`;

const LoadingSpinner = styled.div`
  text-align: center;
  padding: 40px;
  
  .spinner {
    font-size: 24px;
    color: #3498db;
    margin-bottom: 16px;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const EmailPreview = styled.div`
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  
  .subject {
    font-weight: bold;
    margin-bottom: 15px;
    color: #2c3e50;
  }
  
  .content {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    max-height: 300px;
    overflow-y: auto;
  }
`;

const UserReactivationPage = () => {
  const [loading, setLoading] = useState(false);
  const [emailSending, setEmailSending] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  
  const [stats, setStats] = useState({
    totalInactiveUsers: 0,
    totalUsers: 0,
    inactivePercentage: 0,
    emailsSentLast7Days: 0,
    usersReturned: 0,
    returnRate: '0%'
  });
  
  const [users, setUsers] = useState([]);
  const [filters, setFilters] = useState({
    daysInactive: 0,
    limit: 100,
    search: ''
  });

  useEffect(() => {
    fetchInactiveUsers();
    fetchEmailStats();
  }, [filters.daysInactive, filters.limit]);

  const fetchInactiveUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_URL}/api/admin/inactive-users?daysInactive=${filters.daysInactive}&limit=${filters.limit}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
        setStats(prev => ({ ...prev, ...data.stats }));
      }
    } catch (error) {
      console.error('Failed to fetch inactive users:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEmailStats = async () => {
    try {
      const response = await fetch(`${API_URL}/api/admin/email-stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(prev => ({ ...prev, ...data.stats }));
      }
    } catch (error) {
      console.error('Failed to fetch email statistics:', error);
    }
  };

  const handleSelectUser = (userId, checked) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedUsers(users.map(user => user._id));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSendEmails = async (isTest = false) => {
    console.log('handleSendEmails called:', { isTest, selectedUsers, selectedUsersLength: selectedUsers.length });
    
    if (!isTest && selectedUsers.length === 0) {
      alert('Please select users to send emails to');
      return;
    }
    
    if (isTest && !testEmail) {
      alert('Please enter test email address');
      return;
    }

    const confirmMessage = isTest 
      ? `Are you sure you want to send a test email to ${testEmail}?`
      : `Are you sure you want to send activation emails to ${selectedUsers.length} users?`;
    
    if (!window.confirm(confirmMessage)) {
      return;
    }

    setEmailSending(true);
    
    const requestData = {
      userIds: isTest ? [] : selectedUsers,
      testEmail: isTest ? testEmail : null
    };
    
    const token = localStorage.getItem('token');
    console.log('Sending email request:', {
      url: `${API_URL}/api/admin/send-reactivation-email`,
      method: 'POST',
      isTest,
      selectedUsers: selectedUsers.length,
      testEmail,
      requestData,
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      tokenPrefix: token ? token.substring(0, 20) + '...' : 'NO TOKEN'
    });
    
    try {
      const response = await fetch(`${API_URL}/api/admin/send-reactivation-email`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      alert(result.message || 'Email sent successfully');
      if (!isTest) {
        setSelectedUsers([]);
        setSelectAll(false);
        await fetchEmailStats();
      }
    } catch (error) {
      console.error('Failed to send emails:', error);
      let errorMessage = 'Send failed, please check network connection';
      if (error.message) {
        errorMessage = `Send failed: ${error.message}`;
      }
      alert(errorMessage);
    } finally {
      setEmailSending(false);
    }
  };

  const filteredUsers = users.filter(user => 
    !filters.search || 
    user.username.toLowerCase().includes(filters.search.toLowerCase()) ||
    user.email.toLowerCase().includes(filters.search.toLowerCase())
  );

  const emailPreviewContent = `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; line-height: 1.6; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); overflow: hidden;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px; text-align: center;">
        <h1 style="margin: 0; font-size: 28px; font-weight: 600;">🍅 AI-Pomo Team</h1>
        <p style="margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">Personal feedback request</p>
      </div>
      <div style="padding: 40px 30px; color: #2c3e50;">
        <div style="background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 100%); padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #667eea;">
          <p style="margin: 0 0 12px 0;"><strong>Hello [Username],</strong></p>
          <p style="margin: 0 0 12px 0;">I hope this message finds you well. As a member of the AI-Pomo team, I wanted to reach out personally regarding your experience with our productivity tool.</p>
          <p style="margin: 0;">We noticed you signed up for AI-Pomo but haven't been active recently. Rather than sending you generic marketing emails, I wanted to have an honest conversation about your experience.</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin: 25px 0;">
          <h3 style="margin-top: 0; color: #2c3e50; font-size: 18px;">🤔 What AI-Pomo Actually Does</h3>
          <p style="margin: 15px 0;">We built AI-Pomo to solve real productivity struggles:</p>
          <ul style="margin: 15px 0; padding-left: 20px;">
            <li style="margin: 8px 0; color: #495057;"><strong>🎯 When you have big projects</strong> - AI helps break them into manageable 25-minute tasks</li>
            <li style="margin: 8px 0; color: #495057;"><strong>⏰ When you lose track of time</strong> - Pomodoro timer keeps you focused and prevents burnout</li>
            <li style="margin: 8px 0; color: #495057;"><strong>📊 When you wonder "where did my day go?"</strong> - Visual timeline shows exactly how you spent your time</li>
            <li style="margin: 8px 0; color: #495057;"><strong>🎮 When work feels overwhelming</strong> - XP points and achievements make progress feel rewarding</li>
          </ul>
        </div>

        <div style="background: linear-gradient(135deg, #e8f5e8 0%, #d4f4dd 100%); border: 1px solid #c3e6cb; border-radius: 8px; padding: 25px; margin: 25px 0;">
          <h3 style="margin-top: 0; color: #155724; font-size: 18px;">💡 Real Example</h3>
          <p style="margin: 10px 0; color: #155724;"><em>"I need to write a report"</em> becomes:</p>
          <p style="margin: 10px 0; color: #155724;"><strong>✅ Research topic (25 min) → ✅ Create outline (25 min) → ✅ Write introduction (25 min)</strong></p>
          <p style="margin: 10px 0; color: #155724;">Instead of staring at a blank page for 2 hours, you get 3 clear wins and actual progress.</p>
        </div>

        <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin: 25px 0;">
          <h3 style="margin-top: 0; color: #2c3e50;">🚀 What We're Working On</h3>
          <p style="margin: 15px 0;">Since you last visited, we've been busy improving:</p>
          <ul style="margin: 15px 0; padding-left: 20px;">
            <li style="margin: 8px 0; color: #495057;">Better AI project suggestions (less generic, more helpful)</li>
            <li style="margin: 8px 0; color: #495057;">Smoother timer experience</li>
            <li style="margin: 8px 0; color: #495057;">More intuitive interface</li>
            <li style="margin: 8px 0; color: #495057;">Enhanced project tracking and analytics</li>
            <li style="margin: 8px 0; color: #495057;">Better mobile experience</li>
          </ul>
        </div>

        <div style="background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%); padding: 25px; border: 1px solid #ffeaa7; border-radius: 8px; margin: 25px 0;">
          <h3 style="margin-top: 0; color: #856404; font-size: 18px;">💰 Your Feedback Has Real Value</h3>
          <p style="color: #856404; margin: 12px 0;">As a startup, we genuinely value user insights. To show our appreciation, we're offering compensation for detailed feedback:</p>
          <ul style="color: #856404; margin: 15px 0; padding-left: 20px;">
            <li style="margin: 8px 0;">✅ Use AI-Pomo for at least 3 work sessions</li>
            <li style="margin: 8px 0;">✅ Share honest feedback about your experience</li>
            <li style="margin: 8px 0;">✅ Suggest specific improvements or features</li>
          </ul>
          <p style="color: #856404; margin: 12px 0;"><strong>💵 Compensation: Up to 10 USDT</strong> for comprehensive feedback that helps us improve.</p>
          <div style="text-align: center;">
            <a href="mailto:<EMAIL>?subject=User Feedback - [Username]" style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; box-shadow: 0 3px 10px rgba(102,126,234,0.3);">Share Your Experience</a>
          </div>
        </div>

        <div style="background: linear-gradient(135deg, #f0f4ff 0%, #e6f2ff 100%); padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #667eea;">
          <h3 style="margin-top: 0; color: #2c3e50;">🤝 Our Promise</h3>
          <p>We're not another faceless tech company. We're a small team genuinely trying to build something that helps people get things done without burning out. Your feedback directly shapes what we build next.</p>
          <p>If AI-Pomo isn't for you, no worries - just hit unsubscribe below. But if you're willing to give a new product a chance to earn your trust, we'd love to have you back.</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="https://ai-pomo.com/login" style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: 600; box-shadow: 0 3px 10px rgba(102,126,234,0.3);">Give AI-Pomo Another Try</a>
        </div>

        <p style="margin-top: 40px;">
          Thanks for giving us a shot,<br>
          <strong>The AI-Pomo Team</strong>
        </p>
      </div>
    </div>
  `;

  return (
    <Container>
      <Header>
        <h1>
          <FaEnvelope />
          User Reactivation Email Management
        </h1>
        <p>Reactivate inactive users and gather valuable user feedback</p>
      </Header>

      <StatsGrid>
        <StatCard gradient="linear-gradient(135deg, #e74c3c, #c0392b)">
          <div className="icon"><FaUsers /></div>
          <div className="value">{stats.totalInactiveUsers}</div>
          <div className="label">Inactive Users</div>
          <div className="change">{stats.inactivePercentage}% of total users</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #3498db, #2980b9)">
          <div className="icon"><FaEnvelope /></div>
          <div className="value">{stats.emailsSentLast7Days}</div>
          <div className="label">Emails Sent (7 days)</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #27ae60, #229954)">
          <div className="icon"><FaCheckCircle /></div>
          <div className="value">{stats.usersReturned}</div>
          <div className="label">User Return</div>
          <div className="change">Return Rate {stats.returnRate}</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #f39c12, #e67e22)">
          <div className="icon"><FaChartLine /></div>
          <div className="value">{stats.totalUsers}</div>
          <div className="label">Total Registered Users</div>
        </StatCard>
      </StatsGrid>

      {/* Email Preview and Test Area */}
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h2>📧 Email Content Preview</h2>
          <div style={{ display: 'flex', gap: '10px' }}>
            <ActionButton 
              onClick={() => setShowPreview(!showPreview)}
            >
              <FaEye /> {showPreview ? 'Hide Preview' : 'Show Preview'}
            </ActionButton>
          </div>
        </div>

        {showPreview && (
          <EmailPreview>
            <div className="subject">
              📧 Email Subject: [Username], we need your honest feedback about AI-Pomo
            </div>
            <div className="content" dangerouslySetInnerHTML={{ __html: emailPreviewContent }} />
          </EmailPreview>
        )}

        <div style={{ marginTop: '20px', padding: '15px', background: '#e8f5e8', borderRadius: '8px' }}>
          <h4>🧪 Test Email Send</h4>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <input
              type="email"
              placeholder="Enter test email address"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              style={{ flex: 1, padding: '8px 12px', border: '1px solid #ddd', borderRadius: '6px' }}
            />
            <ActionButton 
              success
              onClick={() => handleSendEmails(true)}
              disabled={emailSending || !testEmail}
            >
              {emailSending ? <FaSpinner className="fa-spin" /> : <FaPlay />}
              Send Test Email
            </ActionButton>
          </div>
        </div>
      </Card>

      {/* User Filtering and Operations Area */}
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h2>👥 User List</h2>
          <div style={{ display: 'flex', gap: '10px' }}>
            <ActionButton
              primary
              onClick={() => handleSendEmails(false)}
              disabled={emailSending || selectedUsers.length === 0}
            >
              {emailSending ? <FaSpinner className="fa-spin" /> : <FaEnvelope />}
              Send Emails ({selectedUsers.length})
            </ActionButton>
            <ActionButton onClick={fetchInactiveUsers}>
              <FaDownload /> Refresh Data
            </ActionButton>
          </div>
        </div>

        <FilterBar>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <FaFilter />
            <label>Inactive Days:</label>
            <select 
              value={filters.daysInactive} 
              onChange={(e) => setFilters(prev => ({ ...prev, daysInactive: e.target.value }))}
            >
              <option value={0}>All Users</option>
              <option value={7}>7 Days</option>
              <option value={14}>14 Days</option>
              <option value={30}>30 Days</option>
              <option value={60}>60 Days</option>
              <option value={90}>90 Days</option>
            </select>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <label>Display Count:</label>
            <select 
              value={filters.limit} 
              onChange={(e) => setFilters(prev => ({ ...prev, limit: e.target.value }))}
            >
              <option value={50}>50</option>
              <option value={100}>100</option>
              <option value={200}>200</option>
              <option value={500}>500</option>
            </select>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <FaSearch />
            <input
              type="text"
              placeholder="Search username or email..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              style={{ width: '200px' }}
            />
          </div>
        </FilterBar>

        {loading ? (
          <LoadingSpinner>
            <FaSpinner className="spinner" />
            <p>Loading user data...</p>
          </LoadingSpinner>
        ) : (
          <UserTable>
            <thead>
              <tr>
                <th>
                  <input
                    type="checkbox"
                    className="checkbox"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                </th>
                <th>Username</th>
                <th>Email</th>
                <th>Registration Date</th>
                <th>Last Active</th>
                <th>Inactive Days</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map(user => {
                // 处理没有 lastActiveAt 字段的用户
                const lastActiveDate = user.lastActiveAt ? new Date(user.lastActiveAt) : new Date(user.createdAt);
                const inactiveDays = Math.floor((new Date() - lastActiveDate) / (1000 * 60 * 60 * 24));
                const lastActiveDisplay = user.lastActiveAt ? 
                  new Date(user.lastActiveAt).toLocaleDateString() : 
                  'Never Active';
                
                return (
                  <tr key={user._id}>
                    <td>
                      <input
                        type="checkbox"
                        className="checkbox"
                        checked={selectedUsers.includes(user._id)}
                        onChange={(e) => handleSelectUser(user._id, e.target.checked)}
                      />
                    </td>
                    <td>{user.username}</td>
                    <td>{user.email}</td>
                    <td>{new Date(user.createdAt).toLocaleDateString()}</td>
                    <td>{lastActiveDisplay}</td>
                    <td>{inactiveDays} days</td>
                    <td>
                      <StatusBadge status={inactiveDays > 60 ? 'inactive' : 'pending'}>
                        {inactiveDays > 60 ? 'Long-term Inactive' : 
                         user.lastActiveAt ? 'Short-term Inactive' : 'Never Active'}
                      </StatusBadge>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </UserTable>
        )}

        {!loading && filteredUsers.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#7f8c8d' }}>
            <FaBell style={{ fontSize: '48px', marginBottom: '16px' }} />
            <h3>No Users Found</h3>
            <p>All users are active, or you can adjust the filter criteria</p>
          </div>
        )}
      </Card>

      {/* Operation Instructions */}
      <Card>
        <h3>📋 Usage Instructions</h3>
        <ul style={{ color: '#7f8c8d', lineHeight: '1.6' }}>
          <li>🎯 <strong>Filter Users:</strong> Filter target users based on inactive days and search criteria</li>
          <li>🧪 <strong>Test Send:</strong> Send test email to your mailbox first to check the effect</li>
          <li>📧 <strong>Batch Send:</strong> Select users and send activation emails in bulk</li>
          <li>📊 <strong>Track Results:</strong> Monitor email sending effectiveness and user return rate</li>
          <li>⚠️ <strong>Note:</strong> Avoid frequent sending, recommend at least 1 week interval</li>
        </ul>
      </Card>
    </Container>
  );
};

export default UserReactivationPage;