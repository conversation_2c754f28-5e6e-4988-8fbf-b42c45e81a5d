import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaLink, FaSearch, FaPlus, FaEnvelope, FaEye, FaEdit, FaTrash,
  FaUsers, FaChartLine, FaCheckCircle, FaTimesCircle, FaSpinner,
  FaDownload, FaFilter, FaBullhorn, FaGlobe, FaRocket, FaBullseye,
  FaLightbulb, FaCog, FaPlay, FaPause, FaCalendarAlt
} from 'react-icons/fa';

const Container = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
`;

const TabNavigation = styled.div`
  display: flex;
  margin-bottom: 30px;
  border-bottom: 2px solid #ecf0f1;
`;

const Tab = styled.button`
  padding: 12px 24px;
  background: none;
  border: none;
  cursor: pointer;
  color: ${props => props.active ? '#3498db' : '#7f8c8d'};
  border-bottom: 2px solid ${props => props.active ? '#3498db' : 'transparent'};
  font-weight: ${props => props.active ? '600' : '400'};
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    color: #3498db;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: ${props => props.gradient || 'linear-gradient(135deg, #3498db, #2980b9)'};
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  .icon {
    font-size: 24px;
    margin-bottom: 10px;
    opacity: 0.9;
  }
  
  .value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .label {
    font-size: 12px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const Card = styled.div`
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 25px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: ${props => 
    props.primary ? '#3498db' : 
    props.danger ? '#e74c3c' : 
    props.success ? '#27ae60' : 
    props.warning ? '#f39c12' : '#fff'
  };
  color: ${props => (props.primary || props.danger || props.success || props.warning) ? '#fff' : '#2c3e50'};
  border: 2px solid ${props => 
    props.primary ? '#3498db' : 
    props.danger ? '#e74c3c' : 
    props.success ? '#27ae60' : 
    props.warning ? '#f39c12' : '#ddd'
  };
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 14px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const FilterBar = styled.div`
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  
  select, input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  
  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
  }
  
  th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
  }
  
  tr:hover {
    background: #f8f9fa;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: ${props => {
    switch (props.status) {
      case 'discovered': return '#e3f2fd';
      case 'researched': return '#fff3e0';
      case 'contacted': return '#f3e5f5';
      case 'replied': return '#e8f5e8';
      case 'published': return '#e0f2f1';
      case 'rejected': return '#ffebee';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'discovered': return '#1976d2';
      case 'researched': return '#f57c00';
      case 'contacted': return '#7b1fa2';
      case 'replied': return '#388e3c';
      case 'published': return '#00796b';
      case 'rejected': return '#d32f2f';
      default: return '#616161';
    }
  }};
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  
  h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    
    &:focus {
      outline: none;
      border-color: #3498db;
    }
  }
  
  textarea {
    height: 100px;
    resize: vertical;
  }
`;

const BacklinkOutreachPage = () => {
  const [activeTab, setActiveTab] = useState('prospects');
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  
  const [stats, setStats] = useState({
    totalProspects: 0,
    contacted: 0,
    replied: 0,
    linksReceived: 0,
    totalCampaigns: 0,
    activeCampaigns: 0,
    emailsSent: 0,
    liveBacklinks: 0,
    replyRate: 0,
    successRate: 0
  });
  
  const [prospects, setProspects] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [selectedProspects, setSelectedProspects] = useState([]);
  
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    category: '',
    minDA: '',
    maxDA: '',
    search: ''
  });
  
  const [newProspect, setNewProspect] = useState({
    url: '',
    domain: '',
    title: '',
    contactEmail: '',
    contactName: '',
    categories: [],
    priority: 'medium',
    notes: ''
  });
  
  const [newCampaign, setNewCampaign] = useState({
    name: '',
    description: '',
    targetKeywords: '',
    linkTarget: {
      url: 'https://ai-pomo.com',
      anchorText: 'AI-Pomo'
    },
    emailTemplate: {
      subject: '合作机会：AI-Pomo 生产力工具',
      body: ''
    }
  });

  useEffect(() => {
    fetchStats();
    if (activeTab === 'prospects') {
      fetchProspects();
    } else if (activeTab === 'campaigns') {
      fetchCampaigns();
    }
  }, [activeTab, filters]);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/outreach-stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  const fetchProspects = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
      
      const response = await fetch(`/api/admin/prospects?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setProspects(data.prospects);
      }
    } catch (error) {
      console.error('获取外链机会失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCampaigns = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/campaigns', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setCampaigns(data.campaigns);
      }
    } catch (error) {
      console.error('获取活动失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProspect = async () => {
    try {
      const response = await fetch('/api/admin/prospects', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...newProspect,
          categories: newProspect.categories.split(',').map(c => c.trim())
        })
      });
      
      if (response.ok) {
        setShowModal(false);
        setNewProspect({
          url: '',
          domain: '',
          title: '',
          contactEmail: '',
          contactName: '',
          categories: [],
          priority: 'medium',
          notes: ''
        });
        fetchProspects();
        fetchStats();
        alert('外链机会添加成功！');
      }
    } catch (error) {
      console.error('创建外链机会失败:', error);
      alert('创建失败，请重试');
    }
  };

  const handleDiscoverProspects = async (method, source) => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/discover-prospects', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          method,
          source,
          keywords: ['productivity', 'time management', 'pomodoro']
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        alert(`发现了 ${data.discovered} 个机会，成功保存 ${data.saved} 个`);
        fetchProspects();
        fetchStats();
      }
    } catch (error) {
      console.error('发现外链机会失败:', error);
      alert('发现失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCampaign = async () => {
    try {
      const response = await fetch('/api/admin/campaigns', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...newCampaign,
          targetKeywords: newCampaign.targetKeywords.split(',').map(k => k.trim()),
          prospects: selectedProspects.map(id => ({ prospect: id }))
        })
      });
      
      if (response.ok) {
        setShowModal(false);
        setNewCampaign({
          name: '',
          description: '',
          targetKeywords: '',
          linkTarget: {
            url: 'https://ai-pomo.com',
            anchorText: 'AI-Pomo'
          },
          emailTemplate: {
            subject: '合作机会：AI-Pomo 生产力工具',
            body: ''
          }
        });
        setSelectedProspects([]);
        fetchCampaigns();
        fetchStats();
        alert('外联活动创建成功！');
      }
    } catch (error) {
      console.error('创建活动失败:', error);
      alert('创建失败，请重试');
    }
  };

  const renderProspectsTab = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>🎯 Backlink Opportunity Management</h2>
        <div style={{ display: 'flex', gap: '10px' }}>
          <ActionButton
            primary
            onClick={() => {
              setModalType('addProspect');
              setShowModal(true);
            }}
          >
            <FaPlus /> Add Opportunity
          </ActionButton>
          <ActionButton
            success
            onClick={() => handleDiscoverProspects('competitor_analysis', 'toggl.com')}
            disabled={loading}
          >
            <FaSearch /> Competitor Analysis
          </ActionButton>
          <ActionButton
            warning
            onClick={() => handleDiscoverProspects('keyword_research', 'productivity tools')}
            disabled={loading}
          >
            <FaLightbulb /> Keyword Research
          </ActionButton>
        </div>
      </div>

      <FilterBar>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FaFilter />
          <select 
            value={filters.status} 
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
          >
            <option value="">All Status</option>
            <option value="discovered">Discovered</option>
            <option value="researched">Researched</option>
            <option value="contacted">Contacted</option>
            <option value="replied">Replied</option>
            <option value="published">Published</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <label>Priority:</label>
          <select 
            value={filters.priority} 
            onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
          >
            <option value="">All</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FaSearch />
          <input
            type="text"
            placeholder="Search domain or title..."
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            style={{ width: '200px' }}
          />
        </div>
      </FilterBar>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <FaSpinner style={{ fontSize: '24px', color: '#3498db', animation: 'spin 1s linear infinite' }} />
          <p>Loading...</p>
        </div>
      ) : (
        <Table>
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedProspects(prospects.map(p => p._id));
                    } else {
                      setSelectedProspects([]);
                    }
                  }}
                />
              </th>
              <th>Domain</th>
              <th>Title</th>
              <th>Contact Email</th>
              <th>DA</th>
              <th>Priority</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {prospects.map(prospect => (
              <tr key={prospect._id}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedProspects.includes(prospect._id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedProspects(prev => [...prev, prospect._id]);
                      } else {
                        setSelectedProspects(prev => prev.filter(id => id !== prospect._id));
                      }
                    }}
                  />
                </td>
                <td>{prospect.domain}</td>
                <td>{prospect.title}</td>
                <td>{prospect.contactEmail || 'Unknown'}</td>
                <td>{prospect.metrics?.domainAuthority || 'N/A'}</td>
                <td>{prospect.priority}</td>
                <td>
                  <StatusBadge status={prospect.status}>
                    {prospect.status}
                  </StatusBadge>
                </td>
                <td>
                  <div style={{ display: 'flex', gap: '5px' }}>
                    <ActionButton onClick={() => window.open(prospect.url, '_blank')}>
                      <FaEye />
                    </ActionButton>
                    <ActionButton>
                      <FaEdit />
                    </ActionButton>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}

      {selectedProspects.length > 0 && (
        <div style={{ marginTop: '20px', padding: '15px', background: '#e8f5e8', borderRadius: '8px' }}>
          <p>Selected {selectedProspects.length} backlink opportunities</p>
          <ActionButton
            primary
            onClick={() => {
              setModalType('createCampaign');
              setShowModal(true);
            }}
          >
            <FaBullhorn /> Create Outreach Campaign
          </ActionButton>
        </div>
      )}
    </div>
  );

  const renderCampaignsTab = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>📬 Outreach Campaign Management</h2>
        <ActionButton
          primary
          onClick={() => {
            setModalType('createCampaign');
            setShowModal(true);
          }}
        >
          <FaPlus /> Create Campaign
        </ActionButton>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <FaSpinner style={{ fontSize: '24px', color: '#3498db', animation: 'spin 1s linear infinite' }} />
          <p>Loading...</p>
        </div>
      ) : (
        <Table>
          <thead>
            <tr>
              <th>Campaign Name</th>
              <th>Target Sites</th>
              <th>Sent</th>
              <th>Reply Rate</th>
              <th>Status</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {campaigns.map(campaign => (
              <tr key={campaign._id}>
                <td>{campaign.name}</td>
                <td>{campaign.prospects?.length || 0}</td>
                <td>{campaign.stats?.totalSent || 0}</td>
                <td>
                  {campaign.stats?.totalSent > 0 
                    ? `${((campaign.stats.replied / campaign.stats.totalSent) * 100).toFixed(1)}%`
                    : '0%'
                  }
                </td>
                <td>
                  <StatusBadge status={campaign.status}>
                    {campaign.status}
                  </StatusBadge>
                </td>
                <td>{new Date(campaign.createdAt).toLocaleDateString()}</td>
                <td>
                  <div style={{ display: 'flex', gap: '5px' }}>
                    <ActionButton>
                      <FaEye />
                    </ActionButton>
                    <ActionButton success>
                      <FaPlay />
                    </ActionButton>
                    <ActionButton warning>
                      <FaPause />
                    </ActionButton>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </div>
  );

  return (
    <Container>
      <Header>
        <h1>
          <FaLink />
          Backlink Building & Outreach Management
        </h1>
        <p>Systematically manage backlink discovery, contact, and tracking to improve website authority and traffic</p>
      </Header>

      <StatsGrid>
        <StatCard gradient="linear-gradient(135deg, #3498db, #2980b9)">
          <div className="icon"><FaBullseye /></div>
          <div className="value">{stats.totalProspects}</div>
          <div className="label">Backlink Opportunities</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #f39c12, #e67e22)">
          <div className="icon"><FaEnvelope /></div>
          <div className="value">{stats.contacted}</div>
          <div className="label">Contacted</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #27ae60, #229954)">
          <div className="icon"><FaCheckCircle /></div>
          <div className="value">{stats.replied}</div>
          <div className="label">Replied</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #9b59b6, #8e44ad)">
          <div className="icon"><FaLink /></div>
          <div className="value">{stats.liveBacklinks}</div>
          <div className="label">Active Backlinks</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #e74c3c, #c0392b)">
          <div className="icon"><FaChartLine /></div>
          <div className="value">{stats.replyRate}%</div>
          <div className="label">Reply Rate</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #1abc9c, #16a085)">
          <div className="icon"><FaRocket /></div>
          <div className="value">{stats.successRate}%</div>
          <div className="label">Success Rate</div>
        </StatCard>
      </StatsGrid>

      <Card>
        <TabNavigation>
          <Tab 
            active={activeTab === 'prospects'} 
            onClick={() => setActiveTab('prospects')}
          >
            <FaBullseye /> Backlink Opportunities
          </Tab>
          <Tab 
            active={activeTab === 'campaigns'} 
            onClick={() => setActiveTab('campaigns')}
          >
            <FaBullhorn /> Outreach Campaigns
          </Tab>
          <Tab 
            active={activeTab === 'analytics'} 
            onClick={() => setActiveTab('analytics')}
          >
            <FaChartLine /> Analytics
          </Tab>
        </TabNavigation>

        {activeTab === 'prospects' && renderProspectsTab()}
        {activeTab === 'campaigns' && renderCampaignsTab()}
        {activeTab === 'analytics' && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#7f8c8d' }}>
            <FaChartLine style={{ fontSize: '48px', marginBottom: '16px' }} />
            <h3>Analytics Features</h3>
            <p>Detailed backlink building performance analysis and ROI calculation coming soon</p>
          </div>
        )}
      </Card>

      {/* 模态框 */}
      {showModal && (
        <Modal onClick={() => setShowModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            {modalType === 'addProspect' && (
              <>
                <h3><FaPlus /> Add Backlink Opportunity</h3>
                <FormGroup>
                  <label>Website URL</label>
                  <input
                    type="url"
                    value={newProspect.url}
                    onChange={(e) => setNewProspect(prev => ({ ...prev, url: e.target.value }))}
                    placeholder="https://example.com"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Domain</label>
                  <input
                    type="text"
                    value={newProspect.domain}
                    onChange={(e) => setNewProspect(prev => ({ ...prev, domain: e.target.value }))}
                    placeholder="example.com"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Website Title</label>
                  <input
                    type="text"
                    value={newProspect.title}
                    onChange={(e) => setNewProspect(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Website or page title"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Contact Email</label>
                  <input
                    type="email"
                    value={newProspect.contactEmail}
                    onChange={(e) => setNewProspect(prev => ({ ...prev, contactEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Contact Name</label>
                  <input
                    type="text"
                    value={newProspect.contactName}
                    onChange={(e) => setNewProspect(prev => ({ ...prev, contactName: e.target.value }))}
                    placeholder="Contact person name"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Category Tags (comma separated)</label>
                  <input
                    type="text"
                    value={newProspect.categories}
                    onChange={(e) => setNewProspect(prev => ({ ...prev, categories: e.target.value }))}
                    placeholder="productivity, tech, blog"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Priority</label>
                  <select
                    value={newProspect.priority}
                    onChange={(e) => setNewProspect(prev => ({ ...prev, priority: e.target.value }))}
                  >
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </FormGroup>
                <FormGroup>
                  <label>Notes</label>
                  <textarea
                    value={newProspect.notes}
                    onChange={(e) => setNewProspect(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Notes information..."
                  />
                </FormGroup>
                <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                  <ActionButton onClick={() => setShowModal(false)}>
                    Cancel
                  </ActionButton>
                  <ActionButton primary onClick={handleCreateProspect}>
                    <FaPlus /> Add
                  </ActionButton>
                </div>
              </>
            )}

            {modalType === 'createCampaign' && (
              <>
                <h3><FaBullhorn /> Create Outreach Campaign</h3>
                <FormGroup>
                  <label>Campaign Name</label>
                  <input
                    type="text"
                    value={newCampaign.name}
                    onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="AI-Pomo Backlink Building Campaign"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Campaign Description</label>
                  <textarea
                    value={newCampaign.description}
                    onChange={(e) => setNewCampaign(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Campaign goals and strategy description..."
                  />
                </FormGroup>
                <FormGroup>
                  <label>Target Keywords (comma separated)</label>
                  <input
                    type="text"
                    value={newCampaign.targetKeywords}
                    onChange={(e) => setNewCampaign(prev => ({ ...prev, targetKeywords: e.target.value }))}
                    placeholder="productivity, time management, pomodoro"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Target Link</label>
                  <input
                    type="url"
                    value={newCampaign.linkTarget.url}
                    onChange={(e) => setNewCampaign(prev => ({ 
                      ...prev, 
                      linkTarget: { ...prev.linkTarget, url: e.target.value }
                    }))}
                    placeholder="https://ai-pomo.com"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Anchor Text</label>
                  <input
                    type="text"
                    value={newCampaign.linkTarget.anchorText}
                    onChange={(e) => setNewCampaign(prev => ({ 
                      ...prev, 
                      linkTarget: { ...prev.linkTarget, anchorText: e.target.value }
                    }))}
                    placeholder="AI-Pomo"
                  />
                </FormGroup>
                <FormGroup>
                  <label>Email Subject</label>
                  <input
                    type="text"
                    value={newCampaign.emailTemplate.subject}
                    onChange={(e) => setNewCampaign(prev => ({ 
                      ...prev, 
                      emailTemplate: { ...prev.emailTemplate, subject: e.target.value }
                    }))}
                    placeholder="Partnership Opportunity: AI-Pomo Productivity Tool"
                  />
                </FormGroup>
                <p style={{ color: '#7f8c8d', fontSize: '14px' }}>
                  Will include {selectedProspects.length} backlink opportunities
                </p>
                <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                  <ActionButton onClick={() => setShowModal(false)}>
                    Cancel
                  </ActionButton>
                  <ActionButton primary onClick={handleCreateCampaign}>
                    <FaBullhorn /> Create Campaign
                  </ActionButton>
                </div>
              </>
            )}
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

export default BacklinkOutreachPage;