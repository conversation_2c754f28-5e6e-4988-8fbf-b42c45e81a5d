import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaEnvelope, FaDownload, FaPlus, FaSearch, Fa<PERSON>ilter, 
  FaEye, FaTrash, FaExternalLinkAlt, Fa<PERSON><PERSON>ner, FaCheck,
  FaUserPlus, FaMailBulk, FaCopy, FaChartBar, FaUsers
} from 'react-icons/fa';

const Container = styled.div`
  padding: 20px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: ${props => props.primary ? '#3498db' : '#fff'};
  color: ${props => props.primary ? '#fff' : '#2c3e50'};
  border: 2px solid #3498db;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    background: ${props => props.primary ? '#2980b9' : '#3498db'};
    color: #fff;
    transform: translateY(-2px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const StatsCards = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  
  .icon {
    font-size: 24px;
    color: #3498db;
    margin-bottom: 12px;
  }
  
  .value {
    font-size: 32px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
  }
  
  .label {
    color: #7f8c8d;
    font-size: 14px;
  }
`;

const ToolsSection = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const ToolsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const Tool = styled.div`
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 20px;
  
  h3 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  p {
    color: #7f8c8d;
    margin-bottom: 16px;
    font-size: 14px;
  }
`;

const FilterBar = styled.div`
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  display: flex;
  gap: 16px;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #3498db;
  }
`;

const Select = styled.select`
  padding: 10px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  
  &:focus {
    outline: none;
    border-color: #3498db;
  }
`;

const LeadsTable = styled.div`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 100px;
  gap: 16px;
  padding: 16px 20px;
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
`;

const TableRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 100px;
  gap: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid #ecf0f1;
  align-items: center;
  
  &:hover {
    background: #f8f9fa;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: ${props => {
    switch (props.status) {
      case 'verified': return '#d4edda';
      case 'subscribed': return '#d1ecf1';
      case 'unsubscribed': return '#f8d7da';
      default: return '#fff3cd';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'verified': return '#155724';
      case 'subscribed': return '#0c5460';
      case 'unsubscribed': return '#721c24';
      default: return '#856404';
    }
  }};
`;

const ActionMenu = styled.div`
  display: flex;
  gap: 8px;
`;

const IconButton = styled.button`
  padding: 6px;
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    color: #333;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: #3498db;
    }
  }
  
  textarea {
    height: 100px;
    resize: vertical;
  }
`;

const EmailLeadsManager = () => {
  const [leads, setLeads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sourceFilter, setSourceFilter] = useState('all');
  const [selectedLeads, setSelectedLeads] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAnalyzerModal, setShowAnalyzerModal] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    verified: 0,
    subscribed: 0,
    conversionRate: 0
  });

  useEffect(() => {
    fetchLeads();
    fetchStats();
  }, []);

  const fetchLeads = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/marketing/leads/analytics', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setLeads(data.leads || []);
      } else {
        // 模拟数据
        const mockLeads = [
          {
            _id: '1',
            email: '<EMAIL>',
            source: { type: 'landing_page' },
            status: 'subscribed',
            engagement: { score: 85 },
            createdAt: new Date().toISOString(),
            profile: { occupation: 'student' }
          },
          {
            _id: '2',
            email: '<EMAIL>',
            source: { type: 'exit_intent' },
            status: 'verified',
            engagement: { score: 45 },
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            profile: { occupation: 'employee' }
          }
        ];
        setLeads(mockLeads);
      }
    } catch (error) {
      console.error('Error fetching leads:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    // 模拟统计数据
    setStats({
      total: 247,
      verified: 195,
      subscribed: 156,
      conversionRate: 12.8
    });
  };

  const handleCreateLeadMagnet = async (formData) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/marketing/leads/magnet', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (response.ok) {
        alert('线索磁铁创建成功！');
        setShowCreateModal(false);
      } else {
        alert('创建失败，请重试');
      }
    } catch (error) {
      console.error('Error creating lead magnet:', error);
      alert('创建失败，请检查网络连接');
    }
  };

  const handleAnalyzeEmails = async () => {
    try {
      // 实际的邮件域名分析逻辑
      const domains = leads.reduce((acc, lead) => {
        const domain = lead.email.split('@')[1];
        acc[domain] = (acc[domain] || 0) + 1;
        return acc;
      }, {});

      const analysis = Object.entries(domains)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([domain, count]) => ({
          domain,
          count,
          percentage: ((count / leads.length) * 100).toFixed(1)
        }));

      alert(`邮箱域名分析结果:\n${analysis.map(a => `${a.domain}: ${a.count} (${a.percentage}%)`).join('\n')}`);
    } catch (error) {
      console.error('Error analyzing emails:', error);
    }
  };

  const handleExportLeads = () => {
    const csv = [
      ['Email', 'Source', 'Status', 'Engagement Score', 'Created Date'],
      ...leads.map(lead => [
        lead.email,
        lead.source?.type || '',
        lead.status,
        lead.engagement?.score || 0,
        new Date(lead.createdAt).toLocaleDateString()
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `email-leads-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || lead.status === statusFilter;
    const matchesSource = sourceFilter === 'all' || lead.source?.type === sourceFilter;
    return matchesSearch && matchesStatus && matchesSource;
  });

  if (loading) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <FaSpinner className="fa-spin" style={{ fontSize: '24px', color: '#3498db' }} />
          <p>加载线索数据中...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <h1>
          <FaEnvelope />
          邮件线索管理
        </h1>
        <ActionButtons>
          <ActionButton onClick={handleExportLeads}>
            <FaDownload />
            导出数据
          </ActionButton>
          <ActionButton onClick={() => setShowAnalyzerModal(true)}>
            <FaChartBar />
            分析工具
          </ActionButton>
          <ActionButton primary onClick={() => setShowCreateModal(true)}>
            <FaPlus />
            创建线索磁铁
          </ActionButton>
        </ActionButtons>
      </Header>

      <StatsCards>
        <StatCard>
          <div className="icon"><FaUsers /></div>
          <div className="value">{stats.total}</div>
          <div className="label">总线索数</div>
        </StatCard>
        <StatCard>
          <div className="icon"><FaCheck /></div>
          <div className="value">{stats.verified}</div>
          <div className="label">已验证</div>
        </StatCard>
        <StatCard>
          <div className="icon"><FaMailBulk /></div>
          <div className="value">{stats.subscribed}</div>
          <div className="label">已订阅</div>
        </StatCard>
        <StatCard>
          <div className="icon"><FaChartBar /></div>
          <div className="value">{stats.conversionRate}%</div>
          <div className="label">转化率</div>
        </StatCard>
      </StatsCards>

      <ToolsSection>
        <h2>线索收集工具</h2>
        <ToolsGrid>
          <Tool>
            <h3><FaExternalLinkAlt />着陆页弹窗</h3>
            <p>在重要页面添加邮件收集弹窗，支持退出意图、滚动触发等</p>
            <ActionButton onClick={() => {
              navigator.clipboard.writeText(`
<LeadCaptureModal 
  isOpen={showModal} 
  onClose={closeModal}
  title="获取免费生产力指南"
  leadMagnet="productivity_guide"
/>
              `);
              alert('代码已复制到剪贴板！');
            }}>
              <FaCopy />
              复制代码
            </ActionButton>
          </Tool>
          
          <Tool>
            <h3><FaUserPlus />内容升级</h3>
            <p>为博客文章创建相关的免费资源，提高转化率</p>
            <ActionButton onClick={() => setShowCreateModal(true)}>
              <FaPlus />
              创建资源
            </ActionButton>
          </Tool>
          
          <Tool>
            <h3><FaChartBar />域名分析</h3>
            <p>分析用户邮箱域名，识别企业客户和学生群体</p>
            <ActionButton onClick={handleAnalyzeEmails}>
              <FaEye />
              开始分析
            </ActionButton>
          </Tool>
        </ToolsGrid>
      </ToolsSection>

      <FilterBar>
        <FaSearch style={{ color: '#666' }} />
        <SearchInput
          placeholder="搜索邮箱地址..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <Select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
          <option value="all">所有状态</option>
          <option value="pending">待验证</option>
          <option value="verified">已验证</option>
          <option value="subscribed">已订阅</option>
          <option value="unsubscribed">已退订</option>
        </Select>
        <Select value={sourceFilter} onChange={(e) => setSourceFilter(e.target.value)}>
          <option value="all">所有来源</option>
          <option value="landing_page">着陆页</option>
          <option value="exit_intent">退出意图</option>
          <option value="content_upgrade">内容升级</option>
          <option value="blog_subscription">博客订阅</option>
        </Select>
      </FilterBar>

      <LeadsTable>
        <TableHeader>
          <div>邮箱地址</div>
          <div>来源</div>
          <div>状态</div>
          <div>参与度</div>
          <div>注册时间</div>
          <div>操作</div>
        </TableHeader>
        {filteredLeads.map(lead => (
          <TableRow key={lead._id}>
            <div>{lead.email}</div>
            <div>{lead.source?.type || '-'}</div>
            <div><StatusBadge status={lead.status}>{lead.status}</StatusBadge></div>
            <div>{lead.engagement?.score || 0}</div>
            <div>{new Date(lead.createdAt).toLocaleDateString()}</div>
            <ActionMenu>
              <IconButton title="查看详情">
                <FaEye />
              </IconButton>
              <IconButton title="发送邮件">
                <FaEnvelope />
              </IconButton>
            </ActionMenu>
          </TableRow>
        ))}
      </LeadsTable>

      {/* 创建线索磁铁模态框 */}
      {showCreateModal && (
        <Modal onClick={() => setShowCreateModal(false)}>
          <ModalContent onClick={e => e.stopPropagation()}>
            <h2>创建线索磁铁</h2>
            <CreateLeadMagnetForm 
              onSubmit={handleCreateLeadMagnet}
              onCancel={() => setShowCreateModal(false)}
            />
          </ModalContent>
        </Modal>
      )}

      {/* 分析工具模态框 */}
      {showAnalyzerModal && (
        <Modal onClick={() => setShowAnalyzerModal(false)}>
          <ModalContent onClick={e => e.stopPropagation()}>
            <h2>邮件分析工具</h2>
            <EmailAnalyzer 
              leads={leads}
              onClose={() => setShowAnalyzerModal(false)}
            />
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

// 创建线索磁铁表单组件
const CreateLeadMagnetForm = ({ onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    title: '',
    type: 'ebook',
    description: '',
    targetAudience: 'student',
    downloadUrl: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <FormGroup>
        <label>标题</label>
        <input
          value={formData.title}
          onChange={(e) => setFormData({...formData, title: e.target.value})}
          placeholder="例如：AI生产力终极指南"
          required
        />
      </FormGroup>
      
      <FormGroup>
        <label>类型</label>
        <select
          value={formData.type}
          onChange={(e) => setFormData({...formData, type: e.target.value})}
        >
          <option value="ebook">电子书</option>
          <option value="template">模板</option>
          <option value="checklist">检查清单</option>
          <option value="course">迷你课程</option>
          <option value="tool_access">工具访问</option>
        </select>
      </FormGroup>
      
      <FormGroup>
        <label>描述</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
          placeholder="描述这个资源的价值..."
        />
      </FormGroup>
      
      <FormGroup>
        <label>目标受众</label>
        <select
          value={formData.targetAudience}
          onChange={(e) => setFormData({...formData, targetAudience: e.target.value})}
        >
          <option value="student">学生</option>
          <option value="employee">上班族</option>
          <option value="freelancer">自由职业者</option>
          <option value="entrepreneur">创业者</option>
        </select>
      </FormGroup>
      
      <FormGroup>
        <label>下载链接</label>
        <input
          value={formData.downloadUrl}
          onChange={(e) => setFormData({...formData, downloadUrl: e.target.value})}
          placeholder="https://..."
          type="url"
        />
      </FormGroup>
      
      <ActionButtons>
        <ActionButton type="button" onClick={onCancel}>取消</ActionButton>
        <ActionButton type="submit" primary>创建</ActionButton>
      </ActionButtons>
    </form>
  );
};

// 邮件分析器组件
const EmailAnalyzer = ({ leads, onClose }) => {
  const [analysis, setAnalysis] = useState(null);

  useEffect(() => {
    analyzeLeads();
  }, [leads]);

  const analyzeLeads = () => {
    // 域名分析
    const domains = leads.reduce((acc, lead) => {
      const domain = lead.email.split('@')[1];
      acc[domain] = (acc[domain] || 0) + 1;
      return acc;
    }, {});

    // 来源分析
    const sources = leads.reduce((acc, lead) => {
      const source = lead.source?.type || 'unknown';
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    }, {});

    // 参与度分析
    const avgEngagement = leads.reduce((sum, lead) => sum + (lead.engagement?.score || 0), 0) / leads.length;

    setAnalysis({
      topDomains: Object.entries(domains).sort(([,a], [,b]) => b - a).slice(0, 5),
      sources: Object.entries(sources),
      avgEngagement: avgEngagement.toFixed(1),
      totalLeads: leads.length
    });
  };

  if (!analysis) return <div>分析中...</div>;

  return (
    <div>
      <h3>域名分析</h3>
      {analysis.topDomains.map(([domain, count]) => (
        <div key={domain} style={{ marginBottom: '8px' }}>
          <strong>{domain}</strong>: {count} ({((count / analysis.totalLeads) * 100).toFixed(1)}%)
        </div>
      ))}
      
      <h3 style={{ marginTop: '20px' }}>来源分析</h3>
      {analysis.sources.map(([source, count]) => (
        <div key={source} style={{ marginBottom: '8px' }}>
          <strong>{source}</strong>: {count} ({((count / analysis.totalLeads) * 100).toFixed(1)}%)
        </div>
      ))}
      
      <h3 style={{ marginTop: '20px' }}>参与度</h3>
      <div>平均参与度分数: <strong>{analysis.avgEngagement}</strong></div>
      
      <ActionButtons style={{ marginTop: '20px' }}>
        <ActionButton onClick={onClose}>关闭</ActionButton>
      </ActionButtons>
    </div>
  );
};

export default EmailLeadsManager;