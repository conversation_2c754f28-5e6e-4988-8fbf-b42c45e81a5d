import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FaExternalLinkAlt, FaPlus, FaSearch, FaEnvelope, FaCheck, 
  FaTimes, FaEye, FaCopy, FaSpinner, FaGlobe, FaUserTie,
  FaChartLine, FaCalendarAlt, FaBookmark, FaStar
} from 'react-icons/fa';

const Container = styled.div`
  padding: 20px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: ${props => props.primary ? '#3498db' : '#fff'};
  color: ${props => props.primary ? '#fff' : '#2c3e50'};
  border: 2px solid #3498db;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    background: ${props => props.primary ? '#2980b9' : '#3498db'};
    color: #fff;
    transform: translateY(-2px);
  }
`;

const ToolsSection = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const ToolsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const Tool = styled.div`
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 20px;
  
  h3 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  p {
    color: #7f8c8d;
    margin-bottom: 16px;
    font-size: 14px;
  }
`;

const SearchForm = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
  }
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: #3498db;
    }
  }
  
  textarea {
    height: 80px;
    resize: vertical;
  }
`;

const SearchButton = styled.button`
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const ProspectsSection = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const ProspectsList = styled.div`
  display: grid;
  gap: 16px;
`;

const ProspectCard = styled.div`
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const ProspectHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  
  .website {
    font-weight: 600;
    color: #2c3e50;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
`;

const ProspectMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
`;

const Metric = styled.div`
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  
  .value {
    font-weight: bold;
    color: #2c3e50;
    font-size: 16px;
  }
  
  .label {
    font-size: 12px;
    color: #7f8c8d;
    margin-top: 2px;
  }
`;

const ProspectContact = styled.div`
  margin-bottom: 16px;
  
  .contact-info {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #7f8c8d;
  }
  
  .contact-item {
    display: flex;
    align-items: center;
    gap: 4px;
  }
`;

const ProspectActions = styled.div`
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
`;

const ProspectButton = styled.button`
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #666;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  
  &:hover {
    background: #f8f9fa;
    color: #333;
    border-color: #bbb;
  }
  
  &.primary {
    background: #3498db;
    color: white;
    border-color: #3498db;
    
    &:hover {
      background: #2980b9;
      border-color: #2980b9;
    }
  }
`;

const StatusBadge = styled.span`
  background: ${props => {
    switch (props.status) {
      case 'identified': return '#fff3cd';
      case 'contacted': return '#d1ecf1';
      case 'negotiating': return '#f8d7da';
      case 'accepted': return '#d4edda';
      default: return '#e2e3e5';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'identified': return '#856404';
      case 'contacted': return '#0c5460';
      case 'negotiating': return '#721c24';
      case 'accepted': return '#155724';
      default: return '#383d41';
    }
  }};
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
`;

const BacklinkManager = () => {
  const [searchForm, setSearchForm] = useState({
    keywords: 'productivity tools, pomodoro timer',
    niche: 'productivity',
    minDA: '20',
    targetType: 'guest_post'
  });
  const [prospects, setProspects] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [selectedProspect, setSelectedProspect] = useState(null);

  const handleSearch = async () => {
    setLoading(true);
    
    try {
      // 模拟搜索延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟外链机会数据
      const mockProspects = [
        {
          id: '1',
          website: 'productivityblog.com',
          domain: 'productivityblog.com',
          title: 'The Ultimate Productivity Blog',
          description: 'A popular blog about productivity tools and techniques',
          metrics: {
            domainAuthority: 45,
            pageAuthority: 38,
            organicTraffic: 15000,
            backlinks: 1250
          },
          contact: {
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            position: 'Editor-in-Chief'
          },
          status: 'identified',
          category: 'productivity',
          language: 'en',
          opportunity: 'guest_post',
          notes: 'Accepts guest posts about productivity tools. 1500+ words required.'
        },
        {
          id: '2',
          website: 'studytips.edu',
          domain: 'studytips.edu',
          title: 'Student Study Tips & Resources',
          description: 'Educational resource site for study techniques and time management',
          metrics: {
            domainAuthority: 38,
            pageAuthority: 42,
            organicTraffic: 8500,
            backlinks: 890
          },
          contact: {
            name: 'Dr. Michael Chen',
            email: '<EMAIL>',
            position: 'Site Administrator'
          },
          status: 'identified',
          category: 'education',
          language: 'en',
          opportunity: 'resource_page',
          notes: 'Has a tools directory page. Looking for educational productivity tools.'
        },
        {
          id: '3',
          website: 'timemanagement.org',
          domain: 'timemanagement.org',
          title: 'Time Management Institute',
          description: 'Professional resource for time management techniques and tools',
          metrics: {
            domainAuthority: 52,
            pageAuthority: 48,
            organicTraffic: 22000,
            backlinks: 2100
          },
          contact: {
            name: 'Jennifer Williams',
            email: '<EMAIL>',
            position: 'Content Manager'
          },
          status: 'contacted',
          category: 'business',
          language: 'en',
          opportunity: 'mention',
          notes: 'Already contacted. Waiting for response about tool review.'
        }
      ];
      
      setProspects(mockProspects);
    } catch (error) {
      console.error('Error searching prospects:', error);
      alert('搜索失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setSearchForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContact = (prospect) => {
    setSelectedProspect(prospect);
    setShowEmailModal(true);
  };

  const handleSendEmail = (emailData) => {
    // 模拟发送邮件
    alert(`邮件已发送给 ${selectedProspect.contact.name}！`);
    setShowEmailModal(false);
    
    // 更新状态
    setProspects(prev => prev.map(p => 
      p.id === selectedProspect.id 
        ? { ...p, status: 'contacted' }
        : p
    ));
  };

  const copyEmail = (email) => {
    navigator.clipboard.writeText(email);
    alert('邮箱地址已复制！');
  };

  return (
    <Container>
      <Header>
        <h1>
          <FaExternalLinkAlt />
          外链机会管理
        </h1>
        <ActionButtons>
          <ActionButton>
            <FaBookmark />
            保存的机会
          </ActionButton>
          <ActionButton primary>
            <FaPlus />
            手动添加
          </ActionButton>
        </ActionButtons>
      </Header>

      <ToolsSection>
        <h2>外链建设工具</h2>
        <ToolsGrid>
          <Tool>
            <h3><FaSearch />机会发现</h3>
            <p>基于关键词和利基市场寻找高质量的外链机会</p>
            <ActionButton primary onClick={() => document.getElementById('search-form').scrollIntoView()}>
              <FaSearch />
              开始搜索
            </ActionButton>
          </Tool>
          
          <Tool>
            <h3><FaEnvelope />邮件模板</h3>
            <p>预设的外展邮件模板，提高回复率</p>
            <ActionButton>
              <FaEye />
              查看模板
            </ActionButton>
          </Tool>
          
          <Tool>
            <h3><FaChartLine />进度追踪</h3>
            <p>跟踪外展活动的状态和成功率</p>
            <ActionButton>
              <FaChartLine />
              查看统计
            </ActionButton>
          </Tool>
        </ToolsGrid>
      </ToolsSection>

      <SearchForm id="search-form">
        <h3>搜索外链机会</h3>
        <FormGrid>
          <FormGroup>
            <label>目标关键词</label>
            <input
              name="keywords"
              value={searchForm.keywords}
              onChange={handleFormChange}
              placeholder="productivity tools, pomodoro timer"
            />
          </FormGroup>
          
          <FormGroup>
            <label>利基市场</label>
            <select
              name="niche"
              value={searchForm.niche}
              onChange={handleFormChange}
            >
              <option value="productivity">生产力工具</option>
              <option value="education">教育/学习</option>
              <option value="business">商业/企业</option>
              <option value="tech">科技/软件</option>
              <option value="lifestyle">生活方式</option>
            </select>
          </FormGroup>
          
          <FormGroup>
            <label>最低域名权重 (DA)</label>
            <select
              name="minDA"
              value={searchForm.minDA}
              onChange={handleFormChange}
            >
              <option value="10">10+</option>
              <option value="20">20+</option>
              <option value="30">30+</option>
              <option value="40">40+</option>
              <option value="50">50+</option>
            </select>
          </FormGroup>
          
          <FormGroup>
            <label>外链类型</label>
            <select
              name="targetType"
              value={searchForm.targetType}
              onChange={handleFormChange}
            >
              <option value="guest_post">客座文章</option>
              <option value="resource_page">资源页面</option>
              <option value="broken_link">断链建设</option>
              <option value="mention">品牌提及</option>
              <option value="review">工具评测</option>
            </select>
          </FormGroup>
        </FormGrid>
        
        <SearchButton onClick={handleSearch} disabled={loading}>
          {loading ? (
            <>
              <FaSpinner className="fa-spin" />
              搜索中...
            </>
          ) : (
            <>
              <FaSearch />
              搜索机会
            </>
          )}
        </SearchButton>
      </SearchForm>

      {prospects.length > 0 && (
        <ProspectsSection>
          <h3>发现的外链机会 ({prospects.length})</h3>
          <ProspectsList>
            {prospects.map(prospect => (
              <ProspectCard key={prospect.id}>
                <ProspectHeader>
                  <div className="website">
                    <FaGlobe />
                    {prospect.website}
                  </div>
                  <StatusBadge status={prospect.status} className="status">
                    {prospect.status}
                  </StatusBadge>
                </ProspectHeader>
                
                <p style={{ color: '#7f8c8d', margin: '0 0 12px 0' }}>
                  {prospect.description}
                </p>
                
                <ProspectMetrics>
                  <Metric>
                    <div className="value">{prospect.metrics.domainAuthority}</div>
                    <div className="label">Domain Authority</div>
                  </Metric>
                  <Metric>
                    <div className="value">{prospect.metrics.organicTraffic.toLocaleString()}</div>
                    <div className="label">Monthly Traffic</div>
                  </Metric>
                  <Metric>
                    <div className="value">{prospect.metrics.backlinks.toLocaleString()}</div>
                    <div className="label">Backlinks</div>
                  </Metric>
                  <Metric>
                    <div className="value">{prospect.opportunity}</div>
                    <div className="label">Opportunity</div>
                  </Metric>
                </ProspectMetrics>
                
                <ProspectContact>
                  <div className="contact-info">
                    <div className="contact-item">
                      <FaUserTie />
                      {prospect.contact.name} - {prospect.contact.position}
                    </div>
                    <div className="contact-item">
                      <FaEnvelope />
                      {prospect.contact.email}
                    </div>
                  </div>
                  {prospect.notes && (
                    <div style={{ fontSize: '14px', color: '#7f8c8d' }}>
                      📝 {prospect.notes}
                    </div>
                  )}
                </ProspectContact>
                
                <ProspectActions>
                  <ProspectButton 
                    className="primary"
                    onClick={() => handleContact(prospect)}
                  >
                    <FaEnvelope />
                    联系
                  </ProspectButton>
                  <ProspectButton onClick={() => copyEmail(prospect.contact.email)}>
                    <FaCopy />
                    复制邮箱
                  </ProspectButton>
                  <ProspectButton onClick={() => window.open(`https://${prospect.domain}`, '_blank')}>
                    <FaExternalLinkAlt />
                    访问网站
                  </ProspectButton>
                  <ProspectButton>
                    <FaBookmark />
                    保存
                  </ProspectButton>
                </ProspectActions>
              </ProspectCard>
            ))}
          </ProspectsList>
        </ProspectsSection>
      )}

      {/* 邮件发送模态框 */}
      {showEmailModal && selectedProspect && (
        <Modal onClick={() => setShowEmailModal(false)}>
          <ModalContent onClick={e => e.stopPropagation()}>
            <EmailOutreachForm 
              prospect={selectedProspect}
              onSend={handleSendEmail}
              onCancel={() => setShowEmailModal(false)}
            />
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

// 邮件外展表单组件
const EmailOutreachForm = ({ prospect, onSend, onCancel }) => {
  const [emailData, setEmailData] = useState({
    subject: `Guest Post Proposal for ${prospect.domain}`,
    message: `Hi ${prospect.contact.name},

I came across ${prospect.domain} and was impressed by your content on ${prospect.category}.

I'd love to contribute a guest post about AI-powered productivity techniques that would be valuable for your audience. 

The article would cover:
- How AI can optimize Pomodoro technique usage
- Practical tools for better time management
- Real case studies and results

Would you be interested in this topic? I can send you a detailed outline.

Best regards,
[Your name]`
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSend(emailData);
  };

  return (
    <div>
      <h2>发送外展邮件</h2>
      <p>联系人: <strong>{prospect.contact.name}</strong> ({prospect.contact.email})</p>
      
      <form onSubmit={handleSubmit}>
        <FormGroup>
          <label>邮件主题</label>
          <input
            value={emailData.subject}
            onChange={(e) => setEmailData({...emailData, subject: e.target.value})}
            required
          />
        </FormGroup>
        
        <FormGroup>
          <label>邮件内容</label>
          <textarea
            style={{ height: '200px' }}
            value={emailData.message}
            onChange={(e) => setEmailData({...emailData, message: e.target.value})}
            required
          />
        </FormGroup>
        
        <ActionButtons>
          <ActionButton type="button" onClick={onCancel}>取消</ActionButton>
          <ActionButton type="submit" primary>发送邮件</ActionButton>
        </ActionButtons>
      </form>
    </div>
  );
};

export default BacklinkManager;