import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaEnvelope, FaRedditAlien, FaExternalLinkAlt, FaChartLine,
  FaUsers, FaDownload, FaSpinner, FaSearch, FaPlus,
  FaEye, FaArrowUp, FaComments
} from 'react-icons/fa';

const Container = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0 0 8px 0;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
`;

const TabNavigation = styled.div`
  display: flex;
  border-bottom: 2px solid #ecf0f1;
  margin-bottom: 30px;
`;

const Tab = styled.button`
  padding: 12px 24px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: ${props => props.active ? '#3498db' : '#7f8c8d'};
  border-bottom: 2px solid ${props => props.active ? '#3498db' : 'transparent'};
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    color: #3498db;
  }
`;

const TabContent = styled.div`
  display: ${props => props.active ? 'block' : 'none'};
`;

// Lead Management Components
const LeadSection = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  
  .icon {
    font-size: 24px;
    color: #3498db;
    margin-bottom: 12px;
  }
  
  .value {
    font-size: 32px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
  }
  
  .label {
    color: #7f8c8d;
    font-size: 14px;
  }
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: ${props => props.primary ? '#3498db' : '#fff'};
  color: ${props => props.primary ? '#fff' : '#2c3e50'};
  border: 2px solid #3498db;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    background: ${props => props.primary ? '#2980b9' : '#3498db'};
    color: #fff;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: #3498db;
    }
  }
  
  textarea {
    height: 100px;
    resize: vertical;
  }
`;

const LeadsTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  
  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
  }
  
  th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
  }
  
  tr:hover {
    background: #f8f9fa;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: ${props => {
    switch (props.status) {
      case 'verified': return '#d4edda';
      case 'subscribed': return '#d1ecf1';
      case 'unsubscribed': return '#f8d7da';
      default: return '#fff3cd';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'verified': return '#155724';
      case 'subscribed': return '#0c5460';
      case 'unsubscribed': return '#721c24';
      default: return '#856404';
    }
  }};
`;

// Reddit Analysis Components
const AnalysisForm = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ResultCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  
  h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

const MetricsRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
`;

const Metric = styled.div`
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  
  .value {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 4px;
  }
  
  .label {
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
  }
`;

const PostList = styled.div`
  display: grid;
  gap: 12px;
`;

const PostItem = styled.div`
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 16px;
  
  .title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
  }
  
  .stats {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #7f8c8d;
  }
  
  .stat {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .url {
    font-size: 12px;
    color: #3498db;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const LoadingSpinner = styled.div`
  text-align: center;
  padding: 50px;
  
  .spinner {
    font-size: 24px;
    color: #3498db;
    margin-bottom: 16px;
  }
`;

const ProfessionalMarketingDashboard = () => {
  const [activeTab, setActiveTab] = useState('leads');
  const [loading, setLoading] = useState(false);
  
  // Lead management state
  const [leadStats, setLeadStats] = useState({
    totalLeads: 0,
    activeLeads: 0,
    conversionRate: 0
  });
  const [leads, setLeads] = useState([]);
  
  // Reddit analysis state
  const [redditForm, setRedditForm] = useState({
    subreddit: 'productivity',
    keywords: 'pomodoro, time management, productivity'
  });
  const [redditAnalysis, setRedditAnalysis] = useState(null);

  useEffect(() => {
    if (activeTab === 'leads') {
      fetchLeadData();
    }
  }, [activeTab]);

  const fetchLeadData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/marketing/leads/analytics', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setLeadStats({
          totalLeads: data.summary?.totalLeads || 0,
          activeLeads: data.leads?.filter(l => l.status !== 'unsubscribed').length || 0,
          conversionRate: data.summary?.avgEngagement || 0
        });
        setLeads(data.leads || []);
      }
    } catch (error) {
      console.error('Error fetching lead data:', error);
    }
  };

  const handleExportLeads = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/marketing/leads/analytics?exportFormat=csv', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `leads-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Export error:', error);
      alert('导出失败');
    }
  };

  const handleRedditAnalysis = async () => {
    if (!redditForm.subreddit.trim()) {
      alert('请输入subreddit名称');
      return;
    }

    setLoading(true);
    setRedditAnalysis(null);
    
    try {
      const token = localStorage.getItem('token');
      const keywords = redditForm.keywords.split(',').map(k => k.trim()).filter(k => k);
      
      const response = await fetch('/api/marketing/forum/analyze-competitors', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform: 'reddit',
          subreddit: redditForm.subreddit,
          keywords: keywords,
          limit: 50
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setRedditAnalysis(data.analysis);
      } else {
        alert(data.error || '分析失败');
      }
    } catch (error) {
      console.error('Reddit analysis error:', error);
      alert('分析失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container>
      <Header>
        <h1>Professional Marketing Management System</h1>
        <p>Real data-driven marketing toolkit</p>
      </Header>

      <TabNavigation>
        <Tab active={activeTab === 'leads'} onClick={() => setActiveTab('leads')}>
          <FaEnvelope />
          Email Lead Management
        </Tab>
        <Tab active={activeTab === 'reddit'} onClick={() => setActiveTab('reddit')}>
          <FaRedditAlien />
          Reddit Analysis
        </Tab>
      </TabNavigation>

      <TabContent active={activeTab === 'leads'}>
        <StatsGrid>
          <StatCard>
            <div className="icon"><FaUsers /></div>
            <div className="value">{leadStats.totalLeads}</div>
            <div className="label">总线索数</div>
          </StatCard>
          <StatCard>
            <div className="icon"><FaChartLine /></div>
            <div className="value">{leadStats.activeLeads}</div>
            <div className="label">活跃线索</div>
          </StatCard>
          <StatCard>
            <div className="icon"><FaArrowUp /></div>
            <div className="value">{leadStats.conversionRate.toFixed(1)}</div>
            <div className="label">平均参与度</div>
          </StatCard>
        </StatsGrid>

        <LeadSection>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h3>邮件线索列表</h3>
            <ActionButton onClick={handleExportLeads}>
              <FaDownload />
              导出CSV
            </ActionButton>
          </div>
          
          <LeadsTable>
            <thead>
              <tr>
                <th>邮箱</th>
                <th>来源</th>
                <th>状态</th>
                <th>职业</th>
                <th>注册时间</th>
              </tr>
            </thead>
            <tbody>
              {leads.map((lead, index) => (
                <tr key={index}>
                  <td>{lead.email}</td>
                  <td>{lead.source?.type || '-'}</td>
                  <td><StatusBadge status={lead.status}>{lead.status}</StatusBadge></td>
                  <td>{lead.profile?.occupation || '-'}</td>
                  <td>{new Date(lead.createdAt).toLocaleDateString()}</td>
                </tr>
              ))}
            </tbody>
          </LeadsTable>
        </LeadSection>
      </TabContent>

      <TabContent active={activeTab === 'reddit'}>
        <AnalysisForm>
          <h3>Reddit Community Analysis</h3>
          <FormRow>
            <FormGroup>
              <label>目标Subreddit</label>
              <input
                value={redditForm.subreddit}
                onChange={(e) => setRedditForm({...redditForm, subreddit: e.target.value})}
                placeholder="例如: productivity"
              />
            </FormGroup>
            <FormGroup>
              <label>关键词 (用逗号分隔)</label>
              <input
                value={redditForm.keywords}
                onChange={(e) => setRedditForm({...redditForm, keywords: e.target.value})}
                placeholder="pomodoro, timer, productivity"
              />
            </FormGroup>
          </FormRow>
          <ActionButton primary onClick={handleRedditAnalysis} disabled={loading}>
            {loading ? (
              <>
                <FaSpinner className="fa-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <FaSearch />
                Start Analysis
              </>
            )}
          </ActionButton>
        </AnalysisForm>

        {loading && (
          <LoadingSpinner>
            <FaSpinner className="fa-spin spinner" />
            <p>Analyzing r/{redditForm.subreddit}...</p>
          </LoadingSpinner>
        )}

        {redditAnalysis && (
          <>
            <ResultCard>
              <h3>
                <FaChartLine />
                r/{redditAnalysis.subreddit} Analysis Results
              </h3>
              <MetricsRow>
                <Metric>
                  <div className="value">{redditAnalysis.totalPosts}</div>
                  <div className="label">相关帖子</div>
                </Metric>
                <Metric>
                  <div className="value">{redditAnalysis.metrics?.avgUpvotes || 0}</div>
                  <div className="label">平均点赞</div>
                </Metric>
                <Metric>
                  <div className="value">{redditAnalysis.metrics?.avgComments || 0}</div>
                  <div className="label">平均评论</div>
                </Metric>
                <Metric>
                  <div className="value">{redditAnalysis.metrics?.avgRatio || 0}</div>
                  <div className="label">好评率</div>
                </Metric>
              </MetricsRow>
            </ResultCard>

            {redditAnalysis.topPerformers && redditAnalysis.topPerformers.length > 0 && (
              <ResultCard>
                <h3>
                  <FaArrowUp />
                  高表现帖子
                </h3>
                <PostList>
                  {redditAnalysis.topPerformers.slice(0, 5).map((post, index) => (
                    <PostItem key={index}>
                      <div className="title">{post.title}</div>
                      <div className="stats">
                        <div className="stat">
                          <FaArrowUp />
                          {post.upvotes} 点赞
                        </div>
                        <div className="stat">
                          <FaComments />
                          {post.comments} 评论
                        </div>
                      </div>
                      <a href={post.url} target="_blank" rel="noopener noreferrer" className="url">
                        View Original →
                      </a>
                    </PostItem>
                  ))}
                </PostList>
              </ResultCard>
            )}

            {redditAnalysis.opportunities && redditAnalysis.opportunities.length > 0 && (
              <ResultCard>
                <h3>
                  <FaEye />
                  营销机会
                </h3>
                <PostList>
                  {redditAnalysis.opportunities.slice(0, 5).map((opp, index) => (
                    <PostItem key={index}>
                      <div className="title">{opp.title}</div>
                      <div className="stats">
                        <div className="stat">
                          <FaArrowUp />
                          {opp.upvotes} 点赞
                        </div>
                        <div className="stat">
                          <FaComments />
                          {opp.comments} 评论
                        </div>
                      </div>
                      <p style={{ fontSize: '14px', color: '#7f8c8d', margin: '8px 0' }}>
                        {opp.opportunity}
                      </p>
                      <a href={opp.url} target="_blank" rel="noopener noreferrer" className="url">
                        参与讨论 →
                      </a>
                    </PostItem>
                  ))}
                </PostList>
              </ResultCard>
            )}

            {redditAnalysis.recommendations && redditAnalysis.recommendations.length > 0 && (
              <ResultCard>
                <h3>Content Strategy Recommendations</h3>
                {redditAnalysis.recommendations.map((rec, index) => (
                  <div key={index} style={{ 
                    background: '#f8f9fa', 
                    padding: '16px', 
                    borderRadius: '8px', 
                    marginBottom: '12px',
                    borderLeft: '4px solid #3498db'
                  }}>
                    <div style={{ fontWeight: '600', marginBottom: '8px' }}>
                      {rec.suggestion}
                    </div>
                    <div style={{ fontSize: '14px', color: '#7f8c8d' }}>
                      {rec.evidence}
                    </div>
                    {rec.action && (
                      <div style={{ fontSize: '14px', color: '#2c3e50', marginTop: '4px' }}>
                        💡 {rec.action}
                      </div>
                    )}
                  </div>
                ))}
              </ResultCard>
            )}
          </>
        )}
      </TabContent>
    </Container>
  );
};

export default ProfessionalMarketingDashboard;