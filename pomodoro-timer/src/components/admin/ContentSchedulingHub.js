import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FaCalendarAlt, FaPlus, FaPlay, FaPause, FaEdit, FaTrash,
  FaClock, FaCheckCircle, FaExclamationTriangle, FaSpinner,
  FaFacebook, FaTwitter, FaLinkedin, FaReddit, FaMedium,
  FaWordpress, FaEnvelope, FaChartLine, FaFilter, FaSearch,
  FaRocket, FaBullhorn, FaGlobe, FaUsers, FaEye, FaCog
} from 'react-icons/fa';

const Container = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
`;

const TabNavigation = styled.div`
  display: flex;
  margin-bottom: 30px;
  border-bottom: 2px solid #ecf0f1;
`;

const Tab = styled.button`
  padding: 12px 24px;
  background: none;
  border: none;
  cursor: pointer;
  color: ${props => props.active ? '#3498db' : '#7f8c8d'};
  border-bottom: 2px solid ${props => props.active ? '#3498db' : 'transparent'};
  font-weight: ${props => props.active ? '600' : '400'};
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    color: #3498db;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: ${props => props.gradient || 'linear-gradient(135deg, #3498db, #2980b9)'};
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  .icon {
    font-size: 24px;
    margin-bottom: 10px;
    opacity: 0.9;
  }
  
  .value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .label {
    font-size: 12px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const Card = styled.div`
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 25px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: ${props => 
    props.primary ? '#3498db' : 
    props.danger ? '#e74c3c' : 
    props.success ? '#27ae60' : 
    props.warning ? '#f39c12' : '#fff'
  };
  color: ${props => (props.primary || props.danger || props.success || props.warning) ? '#fff' : '#2c3e50'};
  border: 2px solid ${props => 
    props.primary ? '#3498db' : 
    props.danger ? '#e74c3c' : 
    props.success ? '#27ae60' : 
    props.warning ? '#f39c12' : '#ddd'
  };
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 14px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const CalendarView = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #ddd;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
`;

const CalendarDay = styled.div`
  background: white;
  padding: 15px 10px;
  min-height: 120px;
  position: relative;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
  
  .day-number {
    font-weight: bold;
    color: ${props => props.isToday ? '#3498db' : '#2c3e50'};
    margin-bottom: 8px;
  }
  
  .content-item {
    background: #e8f5e8;
    padding: 4px 6px;
    border-radius: 4px;
    font-size: 11px;
    margin-bottom: 4px;
    cursor: pointer;
    
    &.social { background: #e3f2fd; }
    &.email { background: #fff3e0; }
    &.blog { background: #f3e5f5; }
    &.outreach { background: #ffebee; }
  }
`;

const ContentList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
`;

const ContentItem = styled.div`
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .content-info {
    flex: 1;
    
    .title {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 5px;
    }
    
    .details {
      font-size: 14px;
      color: #7f8c8d;
      display: flex;
      align-items: center;
      gap: 15px;
    }
  }
  
  .platforms {
    display: flex;
    gap: 8px;
    margin: 0 20px;
    
    .platform-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
      
      &.facebook { background: #1877f2; }
      &.twitter { background: #1da1f2; }
      &.linkedin { background: #0a66c2; }
      &.reddit { background: #ff4500; }
      &.medium { background: #00ab6c; }
      &.wordpress { background: #21759b; }
      &.email { background: #ea4335; }
    }
  }
  
  .actions {
    display: flex;
    gap: 8px;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: ${props => {
    switch (props.status) {
      case 'scheduled': return '#e3f2fd';
      case 'published': return '#e8f5e8';
      case 'failed': return '#ffebee';
      case 'draft': return '#f5f5f5';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'scheduled': return '#1976d2';
      case 'published': return '#388e3c';
      case 'failed': return '#d32f2f';
      case 'draft': return '#616161';
      default: return '#616161';
    }
  }};
`;

const FilterBar = styled.div`
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  
  select, input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 700px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  
  h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    
    &:focus {
      outline: none;
      border-color: #3498db;
    }
  }
  
  textarea {
    height: 100px;
    resize: vertical;
  }
`;

const PlatformSelector = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 10px;
`;

const PlatformOption = styled.label`
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 2px solid ${props => props.checked ? '#3498db' : '#ddd'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #3498db;
  }
  
  input {
    margin: 0;
    width: auto;
  }
`;

const ContentSchedulingHub = () => {
  const [activeTab, setActiveTab] = useState('calendar');
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  
  const [stats, setStats] = useState({
    scheduledContent: 0,
    publishedToday: 0,
    totalPlatforms: 0,
    successRate: 0,
    pendingApproval: 0,
    draftContent: 0
  });
  
  const [scheduledContent, setScheduledContent] = useState([]);
  const [filters, setFilters] = useState({
    platform: '',
    status: '',
    dateRange: 'week',
    search: ''
  });
  
  const [newContent, setNewContent] = useState({
    title: '',
    content: '',
    platforms: [],
    scheduledDate: '',
    scheduledTime: '',
    contentType: 'social',
    hashtags: '',
    images: []
  });

  // 模拟数据
  useEffect(() => {
    setStats({
      scheduledContent: 25,
      publishedToday: 8,
      totalPlatforms: 7,
      successRate: 94.5,
      pendingApproval: 3,
      draftContent: 12
    });
    
    setScheduledContent([
      {
        id: 1,
        title: 'AI-Pomo Productivity Tips Sharing',
        content: 'Share how to use the Pomodoro Technique to improve focus...',
        platforms: ['facebook', 'twitter', 'linkedin'],
        scheduledDate: new Date(Date.now() + 86400000),
        status: 'scheduled',
        contentType: 'social'
      },
      {
        id: 2,
        title: 'Deep Work Guide Blog Post',
        content: 'Complete deep work guide with practical tips and tool recommendations',
        platforms: ['medium', 'wordpress'],
        scheduledDate: new Date(Date.now() + 172800000),
        status: 'draft',
        contentType: 'blog'
      },
      {
        id: 3,
        title: 'User Success Story Outreach Email',
        content: 'Share user success stories with AI-Pomo',
        platforms: ['email'],
        scheduledDate: new Date(Date.now() + 259200000),
        status: 'scheduled',
        contentType: 'email'
      }
    ]);
  }, []);

  const getPlatformIcon = (platform) => {
    const icons = {
      facebook: <FaFacebook />,
      twitter: <FaTwitter />,
      linkedin: <FaLinkedin />,
      reddit: <FaReddit />,
      medium: <FaMedium />,
      wordpress: <FaWordpress />,
      email: <FaEnvelope />
    };
    return icons[platform] || <FaGlobe />;
  };

  const generateCalendar = () => {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    const startDate = new Date(startOfMonth);
    startDate.setDate(startDate.getDate() - startDate.getDay());
    
    const calendar = [];
    const current = new Date(startDate);
    
    for (let week = 0; week < 6; week++) {
      for (let day = 0; day < 7; day++) {
        const dateContent = scheduledContent.filter(item => {
          const itemDate = new Date(item.scheduledDate);
          return itemDate.toDateString() === current.toDateString();
        });
        
        calendar.push({
          date: new Date(current),
          dayNumber: current.getDate(),
          isToday: current.toDateString() === today.toDateString(),
          isCurrentMonth: current.getMonth() === today.getMonth(),
          content: dateContent
        });
        
        current.setDate(current.getDate() + 1);
      }
    }
    
    return calendar;
  };

  const handleCreateContent = () => {
    const content = {
      ...newContent,
      id: Date.now(),
      scheduledDate: new Date(`${newContent.scheduledDate} ${newContent.scheduledTime}`),
      status: 'scheduled'
    };
    
    setScheduledContent(prev => [...prev, content]);
    setShowModal(false);
    setNewContent({
      title: '',
      content: '',
      platforms: [],
      scheduledDate: '',
      scheduledTime: '',
      contentType: 'social',
      hashtags: '',
      images: []
    });
    
    // 更新统计数据
    setStats(prev => ({
      ...prev,
      scheduledContent: prev.scheduledContent + 1
    }));
  };

  const renderCalendarTab = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>📅 Content Publishing Calendar</h2>
        <ActionButton
          primary
          onClick={() => setShowModal(true)}
        >
          <FaPlus /> Schedule New Content
        </ActionButton>
      </div>

      <CalendarView>
        {generateCalendar().map((day, index) => (
          <CalendarDay key={index} isToday={day.isToday}>
            <div className="day-number">{day.dayNumber}</div>
            {day.content.map((item, i) => (
              <div 
                key={i} 
                className={`content-item ${item.contentType}`}
                title={item.title}
              >
                {item.title.substring(0, 20)}...
              </div>
            ))}
          </CalendarDay>
        ))}
      </CalendarView>
    </div>
  );

  const renderContentListTab = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>📋 Content Management List</h2>
        <ActionButton
          primary
          onClick={() => setShowModal(true)}
        >
          <FaPlus /> Create Content
        </ActionButton>
      </div>

      <FilterBar>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FaFilter />
          <select 
            value={filters.platform} 
            onChange={(e) => setFilters(prev => ({ ...prev, platform: e.target.value }))}
          >
            <option value="">All Platforms</option>
            <option value="facebook">Facebook</option>
            <option value="twitter">Twitter</option>
            <option value="linkedin">LinkedIn</option>
            <option value="reddit">Reddit</option>
            <option value="medium">Medium</option>
            <option value="wordpress">WordPress</option>
            <option value="email">Email</option>
          </select>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <label>Status:</label>
          <select 
            value={filters.status} 
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
          >
            <option value="">All</option>
            <option value="draft">Draft</option>
            <option value="scheduled">Scheduled</option>
            <option value="published">Published</option>
            <option value="failed">Failed</option>
          </select>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FaSearch />
          <input
            type="text"
            placeholder="Search content title..."
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            style={{ width: '200px' }}
          />
        </div>
      </FilterBar>

      <ContentList>
        {scheduledContent.map(item => (
          <ContentItem key={item.id}>
            <div className="content-info">
              <div className="title">{item.title}</div>
              <div className="details">
                <span><FaClock /> {new Date(item.scheduledDate).toLocaleString()}</span>
                <StatusBadge status={item.status}>{item.status}</StatusBadge>
                <span>{item.contentType}</span>
              </div>
            </div>
            
            <div className="platforms">
              {item.platforms.map(platform => (
                <div key={platform} className={`platform-icon ${platform}`}>
                  {getPlatformIcon(platform)}
                </div>
              ))}
            </div>
            
            <div className="actions">
              <ActionButton><FaEye /></ActionButton>
              <ActionButton><FaEdit /></ActionButton>
              <ActionButton success><FaPlay /></ActionButton>
              <ActionButton danger><FaTrash /></ActionButton>
            </div>
          </ContentItem>
        ))}
      </ContentList>
    </div>
  );

  const renderAnalyticsTab = () => (
    <div style={{ textAlign: 'center', padding: '40px', color: '#7f8c8d' }}>
      <FaChartLine style={{ fontSize: '48px', marginBottom: '16px' }} />
      <h3>Content Analytics Features</h3>
      <p>Detailed content performance analysis, engagement statistics and optimization recommendations coming soon</p>
    </div>
  );

  return (
    <Container>
      <Header>
        <h1>
          <FaCalendarAlt />
          Content Management and Publishing Schedule Hub
        </h1>
        <p>Unified management and scheduling of all marketing content, enabling multi-platform automated publishing</p>
      </Header>

      <StatsGrid>
        <StatCard gradient="linear-gradient(135deg, #3498db, #2980b9)">
          <div className="icon"><FaClock /></div>
          <div className="value">{stats.scheduledContent}</div>
          <div className="label">Scheduled Content</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #27ae60, #229954)">
          <div className="icon"><FaRocket /></div>
          <div className="value">{stats.publishedToday}</div>
          <div className="label">Published Today</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #f39c12, #e67e22)">
          <div className="icon"><FaGlobe /></div>
          <div className="value">{stats.totalPlatforms}</div>
          <div className="label">Connected Platforms</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #9b59b6, #8e44ad)">
          <div className="icon"><FaCheckCircle /></div>
          <div className="value">{stats.successRate}%</div>
          <div className="label">Success Rate</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #e74c3c, #c0392b)">
          <div className="icon"><FaExclamationTriangle /></div>
          <div className="value">{stats.pendingApproval}</div>
          <div className="label">Pending Approval</div>
        </StatCard>
        
        <StatCard gradient="linear-gradient(135deg, #95a5a6, #7f8c8d)">
          <div className="icon"><FaEdit /></div>
          <div className="value">{stats.draftContent}</div>
          <div className="label">Draft Content</div>
        </StatCard>
      </StatsGrid>

      <Card>
        <TabNavigation>
          <Tab 
            active={activeTab === 'calendar'} 
            onClick={() => setActiveTab('calendar')}
          >
            <FaCalendarAlt /> Publishing Calendar
          </Tab>
          <Tab 
            active={activeTab === 'content'} 
            onClick={() => setActiveTab('content')}
          >
            <FaBullhorn /> Content Management
          </Tab>
          <Tab 
            active={activeTab === 'analytics'} 
            onClick={() => setActiveTab('analytics')}
          >
            <FaChartLine /> Performance Analysis
          </Tab>
        </TabNavigation>

        {activeTab === 'calendar' && renderCalendarTab()}
        {activeTab === 'content' && renderContentListTab()}
        {activeTab === 'analytics' && renderAnalyticsTab()}
      </Card>

      {/* 创建内容模态框 */}
      {showModal && (
        <Modal onClick={() => setShowModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <h3><FaPlus /> Create New Content</h3>
            
            <FormGroup>
              <label>Content Title</label>
              <input
                type="text"
                value={newContent.title}
                onChange={(e) => setNewContent(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter content title..."
              />
            </FormGroup>

            <FormGroup>
              <label>Content Type</label>
              <select
                value={newContent.contentType}
                onChange={(e) => setNewContent(prev => ({ ...prev, contentType: e.target.value }))}
              >
                <option value="social">Social Media Post</option>
                <option value="blog">Blog Article</option>
                <option value="email">Email Content</option>
                <option value="outreach">Outreach Content</option>
              </select>
            </FormGroup>

            <FormGroup>
              <label>Content Body</label>
              <textarea
                value={newContent.content}
                onChange={(e) => setNewContent(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Enter content body..."
                rows="4"
              />
            </FormGroup>

            <FormGroup>
              <label>Target Platforms</label>
              <PlatformSelector>
                {[
                  { id: 'facebook', label: 'Facebook', icon: <FaFacebook /> },
                  { id: 'twitter', label: 'Twitter', icon: <FaTwitter /> },
                  { id: 'linkedin', label: 'LinkedIn', icon: <FaLinkedin /> },
                  { id: 'reddit', label: 'Reddit', icon: <FaReddit /> },
                  { id: 'medium', label: 'Medium', icon: <FaMedium /> },
                  { id: 'wordpress', label: 'WordPress', icon: <FaWordpress /> },
                  { id: 'email', label: 'Email', icon: <FaEnvelope /> }
                ].map(platform => (
                  <PlatformOption 
                    key={platform.id}
                    checked={newContent.platforms.includes(platform.id)}
                  >
                    <input
                      type="checkbox"
                      checked={newContent.platforms.includes(platform.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewContent(prev => ({
                            ...prev,
                            platforms: [...prev.platforms, platform.id]
                          }));
                        } else {
                          setNewContent(prev => ({
                            ...prev,
                            platforms: prev.platforms.filter(p => p !== platform.id)
                          }));
                        }
                      }}
                    />
                    {platform.icon}
                    {platform.label}
                  </PlatformOption>
                ))}
              </PlatformSelector>
            </FormGroup>

            <div style={{ display: 'flex', gap: '15px' }}>
              <FormGroup style={{ flex: 1 }}>
                <label>Publish Date</label>
                <input
                  type="date"
                  value={newContent.scheduledDate}
                  onChange={(e) => setNewContent(prev => ({ ...prev, scheduledDate: e.target.value }))}
                />
              </FormGroup>

              <FormGroup style={{ flex: 1 }}>
                <label>Publish Time</label>
                <input
                  type="time"
                  value={newContent.scheduledTime}
                  onChange={(e) => setNewContent(prev => ({ ...prev, scheduledTime: e.target.value }))}
                />
              </FormGroup>
            </div>

            <FormGroup>
              <label>Tags (optional)</label>
              <input
                type="text"
                value={newContent.hashtags}
                onChange={(e) => setNewContent(prev => ({ ...prev, hashtags: e.target.value }))}
                placeholder="#productivity #timemanagement #focus"
              />
            </FormGroup>

            <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end', marginTop: '20px' }}>
              <ActionButton onClick={() => setShowModal(false)}>
                Cancel
              </ActionButton>
              <ActionButton 
                primary 
                onClick={handleCreateContent}
                disabled={!newContent.title || !newContent.content || newContent.platforms.length === 0}
              >
                <FaPlus /> Create Content
              </ActionButton>
            </div>
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

export default ContentSchedulingHub;