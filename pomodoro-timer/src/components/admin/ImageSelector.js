import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaImage, FaSearch, FaTags, FaHeart, FaClock, FaDownload,
  FaEye, FaSpinner, FaCheck, FaTimes, FaFilter, FaStar, FaUser, FaExternalLinkAlt, FaChevronDown
} from 'react-icons/fa';
import { pexelsApi } from '../../services/pexelsApi';

const Modal = styled.div`
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.8) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 999999 !important;
  overflow: hidden !important;
`;

const ModalContent = styled.div`
  background: white !important;
  width: 90vw !important;
  max-width: 1200px !important;
  max-height: 85vh !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
  margin: 0 !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  
  @media (max-width: 768px) {
    width: 95vw !important;
    max-height: 90vh !important;
  }
`;

const Header = styled.div`
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Title = styled.h2`
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
`;

const Controls = styled.div`
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 300px;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const Select = styled.select`
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 150px;
`;

const Content = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
`;

const TagsBar = styled.div`
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  flex-shrink: 0;
`;

const TagButton = styled.button`
  background: ${props => props.active ? '#3498db' : 'white'};
  color: ${props => props.active ? 'white' : '#666'};
  border: 1px solid #ddd;
  padding: 5px 10px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;

  &:hover {
    background: #3498db;
    color: white;
  }
`;

const ScrollableImageArea = styled.div`
  flex: 1;
  overflow-y: auto;
  max-height: calc(85vh - 280px);
  
  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`;

const ImageGrid = styled.div`
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
`;

const ImageCard = styled.div`
  background: white;
  border: 2px solid ${props => props.selected ? '#3498db' : '#eee'};
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
  }
`;

const ImageContainer = styled.div`
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
`;

const Image = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ImageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: ${props => props.selected ? 1 : 0};
  transition: opacity 0.3s ease;

  ${ImageCard}:hover & {
    opacity: 1;
  }
`;

const CheckIcon = styled(FaCheck)`
  color: white;
  font-size: 24px;
`;

const ImageInfo = styled.div`
  padding: 10px;
`;

const ImageTitle = styled.div`
  font-size: 12px;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.3;
  height: 32px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
`;

const ImageStats = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #666;
`;

const StatItem = styled.span`
  display: flex;
  align-items: center;
  gap: 3px;
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
`;

const Footer = styled.div`
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SelectedInfo = styled.div`
  color: #666;
  font-size: 14px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 10px;
`;

const Button = styled.button`
  background: ${props => 
    props.variant === 'primary' ? '#3498db' : 
    props.variant === 'success' ? '#27ae60' : 'transparent'
  };
  color: ${props => 
    props.variant === 'primary' || props.variant === 'success' ? 'white' : '#666'
  };
  border: 1px solid ${props => 
    props.variant === 'primary' ? '#3498db' : 
    props.variant === 'success' ? '#27ae60' : '#ddd'
  };
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    background: #95a5a6;
    border-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
`;

const PageButton = styled.button`
  background: ${props => props.active ? '#3498db' : 'white'};
  color: ${props => props.active ? 'white' : '#3498db'};
  border: 2px solid #3498db;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: #3498db;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
  }

  &:disabled {
    background: #f8f9fa;
    color: #ccc;
    border-color: #ddd;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const ErrorMessage = styled.div`
  background: #ffebee;
  color: #c62828;
  padding: 15px 20px;
  margin: 0 20px;
  border-radius: 6px;
  border-left: 4px solid #c62828;
  font-size: 14px;
`;

const ImageSelector = ({ isOpen, onClose, onSelect, currentImage = null }) => {
  const [images, setImages] = useState([]);
  const [searchTerm, setSearchTerm] = useState('productivity');
  const [orientation, setOrientation] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalResults, setTotalResults] = useState(0);
  const [loading, setLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [error, setError] = useState(null);
  const [searchHistory, setSearchHistory] = useState(['productivity', 'workspace', 'technology', 'business', 'focus', 'creativity']);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      loadImages();
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && searchTerm) {
      const timeoutId = setTimeout(() => {
        setCurrentPage(1);
        loadImages();
      }, 500); // Debounce search

      return () => clearTimeout(timeoutId);
    }
  }, [searchTerm, orientation]);

  useEffect(() => {
    if (isOpen && currentPage > 1) {
      loadImages();
    }
  }, [currentPage]);

  // Find and set the selected image based on currentImage URL
  useEffect(() => {
    if (currentImage && images.length > 0) {
      if (typeof currentImage === 'string') {
        const matchingImage = images.find(img =>
          img.url === currentImage ||
          img.urlMedium === currentImage ||
          img.urlSmall === currentImage
        );
        setSelectedImage(matchingImage || null);
      } else {
        setSelectedImage(currentImage);
      }
    } else {
      setSelectedImage(null);
    }
  }, [currentImage, images]);

  const loadImages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let response;
      if (searchTerm.trim()) {
        response = await pexelsApi.searchPhotos(searchTerm, {
          page: currentPage,
          per_page: 20,
          orientation: orientation !== 'all' ? orientation : undefined
        });
      } else {
        // Load curated photos if no search term
        response = await pexelsApi.getCuratedPhotos({
          page: currentPage,
          per_page: 20
        });
      }

      const formattedData = pexelsApi.formatSearchResults(response);
      
      if (currentPage === 1) {
        setImages(formattedData.photos);
      } else {
        setImages(prev => [...prev, ...formattedData.photos]);
      }
      
      setTotalResults(formattedData.totalResults);
    } catch (error) {
      console.error('Error loading images:', error);
      setError('Failed to load images. Please check your API key and try again.');
      setImages([]);
    } finally {
      setLoading(false);
    }
  };

  const handleImageClick = (image) => {
    setSelectedImage(selectedImage?.id === image.id ? null : image);
  };

  const handleSelectImage = () => {
    if (selectedImage) {
      onSelect({
        id: selectedImage.id,
        url: selectedImage.url,
        title: selectedImage.title,
        alt: selectedImage.alt,
        photographer: selectedImage.photographer,
        photographerUrl: selectedImage.photographerUrl,
        pageUrl: selectedImage.pageUrl
      });
      onClose();
    }
  };

  const handleQuickSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
    // Add to search history if not already there
    if (!searchHistory.includes(term)) {
      setSearchHistory(prev => [term, ...prev.slice(0, 9)]);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const loadMoreImages = () => {
    setCurrentPage(prev => prev + 1);
  };

  if (!isOpen) return null;

  return (
    <Modal onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <Header>
          <Title>
            <FaImage />
            Select Image
          </Title>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
        </Header>

        <Controls>
          <SearchInput
            type="text"
            placeholder="Search images (e.g., productivity, workspace, nature)..."
            value={searchTerm}
            onChange={handleSearch}
          />
          
          <Select value={orientation} onChange={(e) => setOrientation(e.target.value)}>
            <option value="all">All Orientations</option>
            <option value="landscape">Landscape</option>
            <option value="portrait">Portrait</option>
            <option value="square">Square</option>
          </Select>
        </Controls>

        <Content>
          <TagsBar>
            {searchHistory.map(term => (
              <TagButton
                key={term}
                active={searchTerm === term}
                onClick={() => handleQuickSearch(term)}
              >
                {term}
              </TagButton>
            ))}
          </TagsBar>

          {error && (
            <ErrorMessage>
              {error}
            </ErrorMessage>
          )}

          <ScrollableImageArea>
            {loading ? (
              <LoadingContainer>
                <FaSpinner style={{ animation: 'spin 1s linear infinite', marginRight: '10px' }} />
                Loading images...
              </LoadingContainer>
            ) : (
              <>
                <ImageGrid>
                  {images.map(image => (
                    <ImageCard
                      key={image.id}
                      selected={selectedImage?.id === image.id}
                      onClick={() => handleImageClick(image)}
                    >
                      <ImageContainer>
                        <Image src={image.urlSmall} alt={image.alt} />
                        <ImageOverlay selected={selectedImage?.id === image.id}>
                          <CheckIcon />
                        </ImageOverlay>
                      </ImageContainer>
                      <ImageInfo>
                        <ImageTitle>{image.title}</ImageTitle>
                        <ImageStats>
                          <StatItem>
                            <FaUser />
                            {image.photographer}
                          </StatItem>
                          <StatItem>
                            <FaExternalLinkAlt />
                            Pexels
                          </StatItem>
                        </ImageStats>
                      </ImageInfo>
                    </ImageCard>
                  ))}
                </ImageGrid>

                {!loading && images.length > 0 && totalResults > images.length && (
                  <Pagination>
                    <PageButton onClick={loadMoreImages}>
                      <FaChevronDown />
                      Load More Images ({images.length} of {totalResults.toLocaleString()})
                    </PageButton>
                  </Pagination>
                )}
              </>
            )}
          </ScrollableImageArea>
        </Content>

        <Footer>
          <SelectedInfo>
            {selectedImage ? (
              `Selected: ${selectedImage.title} by ${selectedImage.photographer}`
            ) : (
              'No image selected'
            )}
          </SelectedInfo>
          
          <ActionButtons>
            <Button onClick={onClose}>
              Cancel
            </Button>
            <Button 
              variant="success" 
              onClick={handleSelectImage}
              disabled={!selectedImage}
            >
              <FaCheck />
              Select Image
            </Button>
          </ActionButtons>
        </Footer>
      </ModalContent>
    </Modal>
  );
};

export default ImageSelector;