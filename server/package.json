{"name": "ai-pomo-server", "version": "1.0.0", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "init-db": "node src/scripts/init-db.js", "set-admin": "node src/scripts/set-admin.js", "migrate-paddle": "node src/scripts/migrateToPaddle.js", "import-images": "node src/scripts/import-images.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "keywords": ["pomodoro", "timer", "productivity", "ai", "task-management"], "author": "AI Pomo Team", "license": "ISC", "description": "Backend server for AI Pomo - an AI-enhanced Pomodoro timer application", "dependencies": {"adm-zip": "^0.5.16", "archiver": "^7.0.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "compression": "^1.8.0", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.5.0", "express": "^4.18.2", "express-fileupload": "^1.5.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "multer": "^2.0.1", "node-cache": "^5.1.2", "node-cron": "^4.0.5", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "slugify": "^1.6.6"}, "devDependencies": {"nodemon": "^3.1.9"}}