const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const compression = require('compression');
const dotenv = require('dotenv');
const passport = require('passport');
const multer = require('multer');

// Load environment variables
dotenv.config();

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const taskRoutes = require('./routes/tasks');
const pomodoroRoutes = require('./routes/pomodoros');
const statsRoutes = require('./routes/stats');
const projectRoutes = require('./routes/projects');
const milestoneRoutes = require('./routes/milestones');
const noteRoutes = require('./routes/notes');
const reminderRoutes = require('./routes/reminders');
const countdownRoutes = require('./routes/countdowns');
const aggregateRoutes = require('./routes/aggregate');
const subscriptionRoutes = require('./routes/subscriptions');
const adminRoutes = require('./routes/admin');
const standaloneTaskRoutes = require('./routes/standaloneTasks');
const fastTaskRoutes = require('./routes/fastTasks');
const blogRoutes = require('./routes/blog');
const blogImageRoutes = require('./routes/blogImages');
const pillarPageRoutes = require('./routes/pillarPages');
const apiKeyRoutes = require('./routes/apiKeys');
const sitemapRoutes = require('./routes/sitemap');
const analyticsRoutes = require('./routes/analytics');
const contentExportRoutes = require('./routes/contentExport');
const paddleRoutes = require('./routes/paddle');

const activeTimerRoutes = require('./routes/activeTimer');
const contactRoutes = require('./routes/contactRoutes');
const emailOutreachRoutes = require('./routes/emailOutreach');
const emailListRoutes = require('./routes/emailList');
const emailCampaignRoutes = require('./routes/emailCampaign');
const imageRoutes = require('./routes/images');
const marketingRoutes = require('./routes/marketing');
const backlinkOutreachRoutes = require('./routes/backlinkOutreach');

// Initialize Express app
const app = express();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// Make upload middleware available globally
app.set('upload', upload);

// Debug middleware for file uploads
app.use((req, res, next) => {
  if (req.method === 'POST' && req.url.includes('import')) {
    console.log('=== FILE UPLOAD DEBUG ===');
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Files object:', req.files);
    console.log('File object:', req.file);
    console.log('Body object:', req.body);
    console.log('========================');
  }
  next();
});

// Other middleware (after file upload)
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Configure CORS
const corsOptions = {
  origin: function (origin, callback) {
    // Get allowed origins from environment variable or use defaults
    const clientUrls = process.env.CLIENT_URL || 'https://www.ai-pomo.com';
    const clientUrlArray = clientUrls.split(',').map(url => url.trim());
    
    const allowedOrigins = [
      ...clientUrlArray,
      'http://localhost:3000', // For local development
      'https://www.ai-pomo.com', // Main domain
      'https://www.ai-pomo.com', // WWW subdomain
      'https://ai-pomo.vercel.app' // Production Vercel domain
    ];

    // Allow requests with no origin (like mobile apps, curl, etc.)
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      console.log('Allowed origins:', allowedOrigins);
      callback(null, false);
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Origin', 'X-Requested-With', 'Accept'],
  credentials: true
};

// Apply CORS middleware
app.use(cors(corsOptions));

// Handle OPTIONS requests for all routes
app.options('*', cors(corsOptions));

// Enable gzip compression for better performance
app.use(compression({
  filter: (req, res) => {
    // Don't compress responses with this request header
    if (req.headers['x-no-compression']) {
      return false;
    }
    // Fallback to standard filter function
    return compression.filter(req, res);
  },
  level: 6, // Compression level (1-9, 6 is default)
  threshold: 1024 // Only compress responses larger than 1KB
}));

// Analytics middleware (before other middleware)
const { analyticsMiddleware } = require('./middleware/analytics');
app.use(analyticsMiddleware);

// Log requests for debugging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
});

app.use(passport.initialize());

// Configure passport
require('./config/passport')(passport);

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.error('MongoDB connection error:', err));

// Routes
app.use('/auth', authRoutes);
app.use('/users', userRoutes);
app.use('/tasks', taskRoutes);
app.use('/pomodoros', pomodoroRoutes);
app.use('/stats', statsRoutes);
app.use('/projects', projectRoutes);
// Register milestone routes in both places to handle both patterns
app.use('/projects', milestoneRoutes); // For /projects/:projectId/milestones
app.use('/milestones', milestoneRoutes); // For /milestones/:id
app.use('/projects', noteRoutes);
app.use('/reminders', reminderRoutes);
app.use('/countdowns', countdownRoutes);
app.use('/aggregate', aggregateRoutes);

app.use('/standalone-tasks', standaloneTaskRoutes);
app.use('/fast-tasks', fastTaskRoutes);
app.use('/active-timer', activeTimerRoutes);
app.use('/contact', contactRoutes);
app.use('/subscriptions', subscriptionRoutes);
app.use('/paddle', paddleRoutes);
app.use('/api/admin', adminRoutes);
app.use('/blog', blogRoutes);
app.use('/blog-images', blogImageRoutes);
app.use('/pillar-pages', pillarPageRoutes);
app.use('/api-keys', apiKeyRoutes);
app.use('/analytics', analyticsRoutes);
app.use('/content-export', contentExportRoutes);
app.use('/email-outreach', emailOutreachRoutes);
app.use('/email-lists', emailListRoutes);
app.use('/email-campaigns', emailCampaignRoutes);
app.use('/images', imageRoutes);
app.use('/api/marketing', marketingRoutes);
app.use('/api/backlink-outreach', backlinkOutreachRoutes);

// SEO routes (no prefix needed)
app.use('/', sitemapRoutes);

// Health check route for Railway
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'ai-pomo-backend',
    version: '1.0.1'
  });
});

// Root route
app.get('/', (req, res) => {
  res.send('Pomodoro Timer API is running');
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT} and listening on all interfaces`);
});
