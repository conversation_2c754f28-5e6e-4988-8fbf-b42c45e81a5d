const axios = require('axios');
const cheerio = require('cheerio');

class ForumAnalysisService {
  constructor() {
    this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
  }

  // Reddit Analysis
  async analyzeRedditSubreddit(subreddit, keywords = ['productivity', 'pomodoro', 'time management']) {
    try {
      const posts = await this.fetchRedditPosts(subreddit, keywords);
      const analysis = await this.analyzeRedditPosts(posts);
      
      return {
        subreddit,
        keywords,
        totalPosts: posts.length,
        analysis: {
          averageUpvotes: analysis.avgUpvotes,
          averageComments: analysis.avgComments,
          bestPostingTimes: analysis.bestTimes,
          topPerformingTypes: analysis.topTypes,
          engagementPatterns: analysis.engagement,
          contentRecommendations: analysis.recommendations
        },
        competitorAnalysis: analysis.competitors,
        opportunities: analysis.opportunities
      };
    } catch (error) {
      console.error(`Error analyzing Reddit r/${subreddit}:`, error);
      throw error;
    }
  }

  async fetchRedditPosts(subreddit, keywords, limit = 100) {
    try {
      // Use Reddit's JSON API
      const url = `https://www.reddit.com/r/${subreddit}/hot.json?limit=${limit}`;
      const response = await axios.get(url, {
        headers: { 'User-Agent': this.userAgent }
      });

      const posts = response.data.data.children.map(child => child.data);
      
      // Filter posts by keywords
      const relevantPosts = posts.filter(post => {
        const content = `${post.title} ${post.selftext}`.toLowerCase();
        return keywords.some(keyword => content.includes(keyword.toLowerCase()));
      });

      return relevantPosts.map(post => ({
        id: post.id,
        title: post.title,
        content: post.selftext,
        author: post.author,
        upvotes: post.ups,
        downvotes: post.downs,
        comments: post.num_comments,
        url: `https://reddit.com${post.permalink}`,
        createdAt: new Date(post.created_utc * 1000),
        flair: post.link_flair_text,
        awards: post.total_awards_received,
        gilded: post.gilded,
        ratio: post.upvote_ratio
      }));
    } catch (error) {
      console.error('Error fetching Reddit posts:', error);
      return [];
    }
  }

  analyzeRedditPosts(posts) {
    if (posts.length === 0) {
      return {
        avgUpvotes: 0,
        avgComments: 0,
        bestTimes: [],
        topTypes: [],
        engagement: {},
        recommendations: [],
        competitors: [],
        opportunities: []
      };
    }

    // Calculate averages
    const avgUpvotes = posts.reduce((sum, post) => sum + post.upvotes, 0) / posts.length;
    const avgComments = posts.reduce((sum, post) => sum + post.comments, 0) / posts.length;

    // Analyze posting times
    const timeDistribution = {};
    posts.forEach(post => {
      const hour = post.createdAt.getHours();
      timeDistribution[hour] = (timeDistribution[hour] || 0) + 1;
    });

    const bestTimes = Object.entries(timeDistribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour, count]) => ({
        hour: parseInt(hour),
        posts: count,
        timeRange: `${hour}:00-${parseInt(hour) + 1}:00`
      }));

    // Analyze content types
    const contentTypes = this.categorizeRedditPosts(posts);
    const topTypes = Object.entries(contentTypes)
      .sort(([,a], [,b]) => b.avgEngagement - a.avgEngagement)
      .slice(0, 5);

    // Find high-performing posts for recommendations
    const topPosts = posts
      .sort((a, b) => (b.upvotes + b.comments) - (a.upvotes + a.comments))
      .slice(0, 10);

    const recommendations = this.generateContentRecommendations(topPosts);

    // Identify competitors
    const competitors = this.identifyCompetitors(posts);

    // Find opportunities
    const opportunities = this.findOpportunities(posts);

    return {
      avgUpvotes: Math.round(avgUpvotes),
      avgComments: Math.round(avgComments),
      bestTimes,
      topTypes,
      engagement: {
        highPerformers: topPosts.slice(0, 5),
        engagementRate: posts.filter(p => p.ratio > 0.8).length / posts.length
      },
      recommendations,
      competitors,
      opportunities
    };
  }

  categorizeRedditPosts(posts) {
    const categories = {
      question: { posts: [], avgEngagement: 0 },
      tip: { posts: [], avgEngagement: 0 },
      discussion: { posts: [], avgEngagement: 0 },
      tool: { posts: [], avgEngagement: 0 },
      story: { posts: [], avgEngagement: 0 },
      other: { posts: [], avgEngagement: 0 }
    };

    posts.forEach(post => {
      const title = post.title.toLowerCase();
      const content = post.content.toLowerCase();
      let category = 'other';

      if (title.includes('?') || title.startsWith('how') || title.startsWith('what') || title.startsWith('why')) {
        category = 'question';
      } else if (title.includes('tip') || title.includes('advice') || content.includes('try this')) {
        category = 'tip';
      } else if (title.includes('app') || title.includes('tool') || title.includes('software')) {
        category = 'tool';
      } else if (content.length > 200 || title.includes('story') || title.includes('experience')) {
        category = 'story';
      } else if (title.includes('discuss') || title.includes('thoughts') || title.includes('opinion')) {
        category = 'discussion';
      }

      const engagement = post.upvotes + post.comments;
      categories[category].posts.push(post);
      categories[category].avgEngagement = 
        (categories[category].avgEngagement * (categories[category].posts.length - 1) + engagement) / 
        categories[category].posts.length;
    });

    return categories;
  }

  generateContentRecommendations(topPosts) {
    const recommendations = [];
    const patterns = {};

    topPosts.forEach(post => {
      // Analyze title patterns
      const words = post.title.toLowerCase().split(' ');
      words.forEach(word => {
        if (word.length > 3) {
          patterns[word] = (patterns[word] || 0) + 1;
        }
      });
    });

    const popularWords = Object.entries(patterns)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);

    recommendations.push({
      type: 'title_keywords',
      suggestion: `Use these popular keywords in titles: ${popularWords.join(', ')}`,
      evidence: `Found in ${topPosts.length} high-performing posts`
    });

    // Analyze optimal title length
    const avgTitleLength = topPosts.reduce((sum, post) => sum + post.title.length, 0) / topPosts.length;
    recommendations.push({
      type: 'title_length',
      suggestion: `Optimal title length: ${Math.round(avgTitleLength)} characters`,
      evidence: `Average of top performing posts`
    });

    // Analyze content format
    const questionPosts = topPosts.filter(post => post.title.includes('?'));
    if (questionPosts.length > topPosts.length * 0.3) {
      recommendations.push({
        type: 'content_format',
        suggestion: 'Question-based titles perform well in this community',
        evidence: `${questionPosts.length}/${topPosts.length} top posts are questions`
      });
    }

    return recommendations;
  }

  identifyCompetitors(posts) {
    const competitors = {};
    
    posts.forEach(post => {
      if (post.content.includes('app') || post.content.includes('tool') || post.content.includes('software')) {
        // Extract potential app/tool mentions
        const mentions = this.extractToolMentions(post.content);
        mentions.forEach(mention => {
          if (!competitors[mention]) {
            competitors[mention] = {
              name: mention,
              mentions: 0,
              totalEngagement: 0,
              posts: []
            };
          }
          competitors[mention].mentions++;
          competitors[mention].totalEngagement += post.upvotes + post.comments;
          competitors[mention].posts.push({
            title: post.title,
            url: post.url,
            engagement: post.upvotes + post.comments
          });
        });
      }
    });

    return Object.values(competitors)
      .sort((a, b) => b.totalEngagement - a.totalEngagement)
      .slice(0, 10);
  }

  extractToolMentions(content) {
    const toolPatterns = [
      /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+app\b/gi,
      /\busing\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b/gi,
      /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+software\b/gi,
      /\btry\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b/gi
    ];

    const mentions = [];
    toolPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const tool = match.replace(/(app|software|using|try)/gi, '').trim();
          if (tool.length > 2 && tool.length < 30) {
            mentions.push(tool);
          }
        });
      }
    });

    return [...new Set(mentions)]; // Remove duplicates
  }

  findOpportunities(posts) {
    const opportunities = [];

    // Find unanswered questions
    const unansweredQuestions = posts
      .filter(post => post.title.includes('?') && post.comments < 5)
      .sort((a, b) => b.upvotes - a.upvotes)
      .slice(0, 5);

    if (unansweredQuestions.length > 0) {
      opportunities.push({
        type: 'unanswered_questions',
        description: 'Questions with low response rates',
        posts: unansweredQuestions.map(post => ({
          title: post.title,
          url: post.url,
          upvotes: post.upvotes,
          comments: post.comments
        })),
        action: 'Provide helpful answers to build authority'
      });
    }

    // Find trending topics
    const recentPosts = posts.filter(post => {
      const daysDiff = (new Date() - post.createdAt) / (1000 * 60 * 60 * 24);
      return daysDiff <= 7;
    });

    if (recentPosts.length > 0) {
      const trendingKeywords = this.extractTrendingKeywords(recentPosts);
      opportunities.push({
        type: 'trending_topics',
        description: 'Currently trending keywords',
        keywords: trendingKeywords,
        action: 'Create content around these trending topics'
      });
    }

    return opportunities;
  }

  extractTrendingKeywords(posts) {
    const keywords = {};
    const stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'];

    posts.forEach(post => {
      const words = `${post.title} ${post.content}`.toLowerCase()
        .replace(/[^a-z\s]/g, '')
        .split(/\s+/)
        .filter(word => word.length > 3 && !stopWords.includes(word));

      words.forEach(word => {
        keywords[word] = (keywords[word] || 0) + 1;
      });
    });

    return Object.entries(keywords)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ word, mentions: count }));
  }

  // Quora Analysis
  async analyzeQuoraTopics(topics, keywords) {
    try {
      const results = [];
      
      for (const topic of topics) {
        const questions = await this.fetchQuoraQuestions(topic, keywords);
        const analysis = this.analyzeQuoraQuestions(questions);
        
        results.push({
          topic,
          totalQuestions: questions.length,
          analysis
        });
      }

      return {
        topics: results,
        overallInsights: this.generateQuoraInsights(results)
      };
    } catch (error) {
      console.error('Error analyzing Quora topics:', error);
      throw error;
    }
  }

  async fetchQuoraQuestions(topic, keywords, limit = 50) {
    // Note: Quora doesn't have a public API, so this would require web scraping
    // This is a simplified placeholder - in production, you'd use a service like Apify or Scrapy
    console.log(`Would fetch Quora questions for topic: ${topic}, keywords: ${keywords.join(', ')}`);
    
    // Return mock data for now
    return [
      {
        id: '1',
        question: 'What are the best productivity apps for students?',
        views: 5000,
        answers: 15,
        followers: 100,
        url: 'https://quora.com/example',
        createdAt: new Date(),
        topic: topic
      }
    ];
  }

  analyzeQuoraQuestions(questions) {
    const avgViews = questions.reduce((sum, q) => sum + q.views, 0) / questions.length;
    const avgAnswers = questions.reduce((sum, q) => sum + q.answers, 0) / questions.length;
    
    const unansweredQuestions = questions.filter(q => q.answers === 0);
    const lowCompetitionQuestions = questions.filter(q => q.answers < 3 && q.views > 1000);

    return {
      avgViews: Math.round(avgViews),
      avgAnswers: Math.round(avgAnswers),
      unansweredCount: unansweredQuestions.length,
      lowCompetitionOpportunities: lowCompetitionQuestions.slice(0, 10),
      topViewedQuestions: questions.sort((a, b) => b.views - a.views).slice(0, 5)
    };
  }

  generateQuoraInsights(results) {
    const totalQuestions = results.reduce((sum, result) => sum + result.totalQuestions, 0);
    const avgViews = results.reduce((sum, result) => sum + result.analysis.avgViews, 0) / results.length;

    return {
      totalQuestions,
      avgViews: Math.round(avgViews),
      bestTopics: results.sort((a, b) => b.analysis.avgViews - a.analysis.avgViews).slice(0, 3),
      opportunities: results.reduce((acc, result) => {
        return acc.concat(result.analysis.lowCompetitionOpportunities);
      }, []).slice(0, 20)
    };
  }

  // Content suggestion based on analysis
  generateContentSuggestions(platformAnalysis) {
    const suggestions = [];

    if (platformAnalysis.reddit) {
      const reddit = platformAnalysis.reddit;
      
      suggestions.push({
        platform: 'reddit',
        type: 'post_timing',
        suggestion: `Post during peak hours: ${reddit.analysis.bestPostingTimes.map(t => t.timeRange).join(', ')}`,
        priority: 'high'
      });

      suggestions.push({
        platform: 'reddit',
        type: 'content_type',
        suggestion: `Focus on ${reddit.analysis.topPerformingTypes[0]?.[0]} posts - they get ${reddit.analysis.topPerformingTypes[0]?.[1].avgEngagement} avg engagement`,
        priority: 'high'
      });

      reddit.opportunities.forEach(opp => {
        suggestions.push({
          platform: 'reddit',
          type: 'opportunity',
          suggestion: opp.action,
          priority: 'medium',
          details: opp.description
        });
      });
    }

    return suggestions;
  }
}

module.exports = new ForumAnalysisService();