const { PublishingPlatform, ArticleTemplate, ContentCampaign, PublishedArticle } = require('../models/ContentPublishing');
const axios = require('axios');
const cron = require('node-cron');

// WordPress API 客户端
class WordPressClient {
  constructor(platform) {
    this.baseUrl = platform.apiConfig.baseUrl;
    this.credentials = platform.credentials;
    this.auth = Buffer.from(`${this.credentials.username}:${this.credentials.password}`).toString('base64');
  }

  async createPost(article) {
    try {
      const postData = {
        title: article.content.title,
        content: article.content.body,
        excerpt: article.content.excerpt || '',
        status: article.publishing.platformStatus || 'publish',
        categories: article.content.category ? [article.content.category] : [],
        tags: article.content.tags || [],
        meta: {
          description: article.seo.metaDescription || '',
          keywords: article.seo.keywords ? article.seo.keywords.join(', ') : ''
        }
      };

      if (article.content.featuredImage?.url) {
        postData.featured_media = await this.uploadImage(article.content.featuredImage.url);
      }

      const response = await axios.post(`${this.baseUrl}/wp-json/wp/v2/posts`, postData, {
        headers: {
          'Authorization': `Basic ${this.auth}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        postId: response.data.id.toString(),
        url: response.data.link,
        status: response.data.status
      };
    } catch (error) {
      console.error('WordPress API error:', error.response?.data);
      throw new Error(error.response?.data?.message || 'WordPress publishing failed');
    }
  }

  async uploadImage(imageUrl) {
    try {
      // 下载图片
      const imageResponse = await axios.get(imageUrl, { responseType: 'stream' });
      
      // 上传到 WordPress
      const uploadResponse = await axios.post(`${this.baseUrl}/wp-json/wp/v2/media`, imageResponse.data, {
        headers: {
          'Authorization': `Basic ${this.auth}`,
          'Content-Type': imageResponse.headers['content-type'],
          'Content-Disposition': 'attachment; filename="image.jpg"'
        }
      });

      return uploadResponse.data.id;
    } catch (error) {
      console.error('WordPress image upload failed:', error);
      return null;
    }
  }

  async getPostStats(postId) {
    try {
      const response = await axios.get(`${this.baseUrl}/wp-json/wp/v2/posts/${postId}`, {
        headers: {
          'Authorization': `Basic ${this.auth}`
        }
      });

      // WordPress 基础统计（需要插件支持更详细统计）
      return {
        views: response.data.meta?.views || 0,
        comments: response.data.comment_status === 'open' ? await this.getCommentCount(postId) : 0,
        shares: 0, // 需要社交分享插件
        status: response.data.status
      };
    } catch (error) {
      console.error('WordPress stats fetch failed:', error);
      return null;
    }
  }

  async getCommentCount(postId) {
    try {
      const response = await axios.get(`${this.baseUrl}/wp-json/wp/v2/comments?post=${postId}`, {
        headers: {
          'Authorization': `Basic ${this.auth}`
        }
      });
      return response.data.length;
    } catch (error) {
      return 0;
    }
  }
}

// Medium API 客户端
class MediumClient {
  constructor(platform) {
    this.accessToken = platform.apiConfig.accessToken;
    this.userId = platform.credentials.userId;
    this.baseUrl = 'https://api.medium.com/v1';
  }

  async createPost(article) {
    try {
      const postData = {
        title: article.content.title,
        contentFormat: 'html',
        content: article.content.body,
        tags: article.content.tags || [],
        publishStatus: article.publishing.platformStatus || 'public',
        notifyFollowers: true
      };

      const response = await axios.post(`${this.baseUrl}/users/${this.userId}/posts`, postData, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        postId: response.data.data.id,
        url: response.data.data.url,
        status: response.data.data.publishStatus
      };
    } catch (error) {
      console.error('Medium API error:', error.response?.data);
      throw new Error('Medium publishing failed');
    }
  }

  async getPostStats(postId) {
    // Medium API 限制，无法获取详细统计
    return {
      views: 0,
      claps: 0,
      responses: 0
    };
  }
}

// Dev.to API 客户端
class DevToClient {
  constructor(platform) {
    this.apiKey = platform.apiConfig.apiKey;
    this.baseUrl = 'https://dev.to/api';
  }

  async createPost(article) {
    try {
      const postData = {
        article: {
          title: article.content.title,
          body_markdown: this.convertToMarkdown(article.content.body),
          published: article.publishing.platformStatus === 'published',
          tags: article.content.tags || [],
          series: article.content.category || undefined,
          main_image: article.content.featuredImage?.url || undefined,
          description: article.content.excerpt || undefined
        }
      };

      const response = await axios.post(`${this.baseUrl}/articles`, postData, {
        headers: {
          'api-key': this.apiKey,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        postId: response.data.id.toString(),
        url: response.data.url,
        status: response.data.published ? 'published' : 'draft'
      };
    } catch (error) {
      console.error('Dev.to API error:', error.response?.data);
      throw new Error('Dev.to publishing failed');
    }
  }

  convertToMarkdown(html) {
    // 简单的 HTML 到 Markdown 转换
    // 实际项目中应使用专门的转换库
    return html
      .replace(/<h1>(.*?)<\/h1>/g, '# $1')
      .replace(/<h2>(.*?)<\/h2>/g, '## $1')
      .replace(/<h3>(.*?)<\/h3>/g, '### $1')
      .replace(/<p>(.*?)<\/p>/g, '$1\n\n')
      .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      .replace(/<em>(.*?)<\/em>/g, '*$1*')
      .replace(/<br\s*\/?>/g, '\n')
      .replace(/<[^>]*>/g, ''); // 移除其他HTML标签
  }

  async getPostStats(postId) {
    try {
      const response = await axios.get(`${this.baseUrl}/articles/${postId}`, {
        headers: {
          'api-key': this.apiKey
        }
      });

      return {
        views: response.data.page_views_count || 0,
        reactions: response.data.positive_reactions_count || 0,
        comments: response.data.comments_count || 0,
        reads: response.data.reading_time_minutes || 0
      };
    } catch (error) {
      console.error('Dev.to stats fetch failed:', error);
      return null;
    }
  }
}

// === 发布平台管理 ===

// 添加发布平台
exports.createPublishingPlatform = async (req, res) => {
  try {
    const { name, type, platform, apiConfig, credentials, capabilities, publishingSettings, rateLimits } = req.body;
    
    // 验证平台配置
    const validation = await validatePlatformConfig(platform, apiConfig, credentials);
    if (!validation.valid) {
      return res.status(400).json({ error: validation.error });
    }
    
    const publishingPlatform = new PublishingPlatform({
      name,
      type,
      platform,
      apiConfig,
      credentials: encryptCredentials(credentials),
      capabilities: capabilities || {},
      publishingSettings: publishingSettings || {},
      rateLimits: rateLimits || {}
    });
    
    await publishingPlatform.save();
    
    // 返回安全的平台信息
    const safePlatform = publishingPlatform.toObject();
    delete safePlatform.credentials;
    delete safePlatform.apiConfig;
    
    res.json({ success: true, platform: safePlatform });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 验证平台配置
async function validatePlatformConfig(platform, apiConfig, credentials) {
  try {
    switch (platform) {
      case 'wordpress':
        if (!apiConfig.baseUrl || !credentials.username || !credentials.password) {
          return { valid: false, error: 'WordPress 需要网站URL、用户名和密码' };
        }
        // 测试连接
        try {
          const auth = Buffer.from(`${credentials.username}:${credentials.password}`).toString('base64');
          await axios.get(`${apiConfig.baseUrl}/wp-json/wp/v2/users/me`, {
            headers: { 'Authorization': `Basic ${auth}` }
          });
          return { valid: true };
        } catch (error) {
          return { valid: false, error: 'WordPress 认证失败' };
        }
      
      case 'medium':
        if (!apiConfig.accessToken) {
          return { valid: false, error: 'Medium 需要访问令牌' };
        }
        return { valid: true };
      
      case 'devto':
        if (!apiConfig.apiKey) {
          return { valid: false, error: 'Dev.to 需要API密钥' };
        }
        return { valid: true };
      
      default:
        return { valid: true };
    }
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

// 简单凭据加密
function encryptCredentials(credentials) {
  // 实际项目中应使用强加密
  return credentials;
}

// 获取发布平台列表
exports.getPublishingPlatforms = async (req, res) => {
  try {
    const { type, platform, status } = req.query;
    const filter = {};
    
    if (type) filter.type = type;
    if (platform) filter.platform = platform;
    if (status) filter.status = status;
    
    const platforms = await PublishingPlatform.find(filter)
      .select('-credentials -apiConfig')
      .sort({ createdAt: -1 });
    
    res.json({ platforms });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 测试平台连接
exports.testPlatformConnection = async (req, res) => {
  try {
    const platform = await PublishingPlatform.findById(req.params.id);
    if (!platform) {
      return res.status(404).json({ error: '平台未找到' });
    }
    
    const testResult = await testPlatformAPI(platform);
    
    // 更新测试结果
    platform.lastTestDate = new Date();
    platform.status = testResult.success ? 'active' : 'error';
    platform.lastError = testResult.success ? null : testResult.error;
    
    await platform.save();
    
    res.json(testResult);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 测试平台 API
async function testPlatformAPI(platform) {
  try {
    switch (platform.platform) {
      case 'wordpress':
        const wp = new WordPressClient(platform);
        await axios.get(`${platform.apiConfig.baseUrl}/wp-json/wp/v2/users/me`, {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${platform.credentials.username}:${platform.credentials.password}`).toString('base64')}`
          }
        });
        return { success: true, message: 'WordPress 连接成功' };
      
      case 'medium':
        await axios.get('https://api.medium.com/v1/me', {
          headers: {
            'Authorization': `Bearer ${platform.apiConfig.accessToken}`
          }
        });
        return { success: true, message: 'Medium 连接成功' };
      
      case 'devto':
        await axios.get('https://dev.to/api/users/me', {
          headers: {
            'api-key': platform.apiConfig.apiKey
          }
        });
        return { success: true, message: 'Dev.to 连接成功' };
      
      default:
        return { success: false, error: '不支持的平台' };
    }
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.message || error.message || '连接测试失败' 
    };
  }
}

// === 文章模板管理 ===

// 创建文章模板
exports.createArticleTemplate = async (req, res) => {
  try {
    const { name, description, category, structure, seo, variables, publishingConfig } = req.body;
    
    const template = new ArticleTemplate({
      name,
      description,
      category,
      structure,
      seo: seo || {},
      variables: variables || [],
      publishingConfig: publishingConfig || {}
    });
    
    await template.save();
    res.json({ success: true, template });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 获取文章模板
exports.getArticleTemplates = async (req, res) => {
  try {
    const { category, active } = req.query;
    const filter = {};
    
    if (category) filter.category = category;
    if (active !== undefined) filter.active = active === 'true';
    
    const templates = await ArticleTemplate.find(filter)
      .populate('publishingConfig.platforms', 'name platform')
      .sort({ createdAt: -1 });
    
    res.json({ templates });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === 内容发布活动管理 ===

// 创建内容发布活动
exports.createContentCampaign = async (req, res) => {
  try {
    const {
      name,
      description,
      type,
      contentConfig,
      platformConfig,
      scheduling,
      contentAdaptation,
      automation,
      moderation
    } = req.body;
    
    const campaign = new ContentCampaign({
      name,
      description,
      type,
      contentConfig,
      platformConfig,
      scheduling,
      contentAdaptation: contentAdaptation || {},
      automation: automation || {},
      moderation: moderation || {},
      createdBy: req.user._id
    });
    
    // 计算下次发布时间
    if (scheduling.frequency !== 'once') {
      campaign.scheduling.nextPublishTime = calculateNextPublishTime(scheduling);
    }
    
    await campaign.save();
    res.json({ success: true, campaign });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 计算下次发布时间
function calculateNextPublishTime(scheduling) {
  const now = new Date();
  
  if (scheduling.publishImmediately) {
    return now;
  }
  
  switch (scheduling.frequency) {
    case 'daily':
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      if (scheduling.customSchedule && scheduling.customSchedule.length > 0) {
        const schedule = scheduling.customSchedule[0];
        const [hour, minute] = schedule.time.split(':').map(Number);
        tomorrow.setHours(hour, minute, 0, 0);
      }
      return tomorrow;
    
    case 'weekly':
      const nextWeek = new Date(now);
      nextWeek.setDate(nextWeek.getDate() + 7);
      return nextWeek;
    
    case 'bi_weekly':
      const nextBiWeek = new Date(now);
      nextBiWeek.setDate(nextBiWeek.getDate() + 14);
      return nextBiWeek;
    
    case 'monthly':
      const nextMonth = new Date(now);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      return nextMonth;
  }
  
  return new Date(now.getTime() + 24 * 60 * 60 * 1000);
}

// 获取内容发布活动
exports.getContentCampaigns = async (req, res) => {
  try {
    const { status, type } = req.query;
    const filter = {};
    
    if (status) filter.status = status;
    if (type) filter.type = type;
    
    const campaigns = await ContentCampaign.find(filter)
      .populate('contentConfig.templates', 'name category')
      .populate('platformConfig.platforms', 'name platform')
      .sort({ createdAt: -1 });
    
    res.json({ campaigns });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 启动内容发布活动
exports.startContentCampaign = async (req, res) => {
  try {
    const campaign = await ContentCampaign.findById(req.params.id);
    if (!campaign) {
      return res.status(404).json({ error: '活动未找到' });
    }
    
    // 验证活动配置
    const validation = validateContentCampaignConfig(campaign);
    if (!validation.valid) {
      return res.status(400).json({ error: validation.error });
    }
    
    campaign.status = 'active';
    if (!campaign.scheduling.nextPublishTime) {
      campaign.scheduling.nextPublishTime = calculateNextPublishTime(campaign.scheduling);
    }
    
    await campaign.save();
    
    // 立即创建第一篇待发布的文章
    await scheduleNextArticle(campaign);
    
    res.json({ success: true, message: '内容发布活动已启动' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 验证活动配置
function validateContentCampaignConfig(campaign) {
  if (!campaign.contentConfig.templates || campaign.contentConfig.templates.length === 0) {
    if (!campaign.contentConfig.customArticles || campaign.contentConfig.customArticles.length === 0) {
      return { valid: false, error: '未设置内容模板或自定义文章' };
    }
  }
  
  if (!campaign.platformConfig.platforms || campaign.platformConfig.platforms.length === 0) {
    return { valid: false, error: '未设置发布平台' };
  }
  
  return { valid: true };
}

// === 自动发布执行 ===

// 调度下一篇文章
async function scheduleNextArticle(campaign) {
  try {
    // 选择内容
    const content = await selectArticleContent(campaign);
    if (!content) {
      console.error(`Campaign ${campaign.name}: No content available`);
      return;
    }
    
    // 选择平台
    const platforms = await selectPublishingPlatforms(campaign);
    if (!platforms || platforms.length === 0) {
      console.error(`Campaign ${campaign.name}: No platforms available`);
      return;
    }
    
    let publishTime = campaign.scheduling.nextPublishTime;
    
    // 为每个平台创建发布任务
    for (const platform of platforms) {
      const adaptedContent = await adaptContentForPlatform(content, platform, campaign);
      
      const article = new PublishedArticle({
        campaignId: campaign._id,
        templateId: content.templateId,
        platformId: platform._id,
        content: adaptedContent.content,
        seo: adaptedContent.seo,
        publishing: {
          status: 'queued',
          scheduledFor: publishTime,
          platformStatus: platform.publishingSettings.defaultStatus || 'draft'
        }
      });
      
      await article.save();
      
      // 如果是顺序发布，为下个平台添加延迟
      if (campaign.platformConfig.publishingStrategy === 'sequential' && platforms.length > 1) {
        publishTime = new Date(publishTime.getTime() + campaign.platformConfig.platformDelay);
      }
    }
    
    // 更新下次发布时间
    campaign.scheduling.nextPublishTime = calculateNextPublishTime(campaign.scheduling);
    await campaign.save();
    
    console.log(`Scheduled article for campaign ${campaign.name} on ${platforms.length} platforms`);
  } catch (error) {
    console.error('Error scheduling article:', error);
  }
}

// 选择文章内容
async function selectArticleContent(campaign) {
  // 优先使用自定义文章
  if (campaign.contentConfig.customArticles && campaign.contentConfig.customArticles.length > 0) {
    const unused = campaign.contentConfig.customArticles.filter(article => !article.used);
    if (unused.length > 0) {
      return unused[0];
    }
  }
  
  // 使用模板
  if (campaign.contentConfig.templates && campaign.contentConfig.templates.length > 0) {
    const templates = await ArticleTemplate.find({
      _id: { $in: campaign.contentConfig.templates },
      active: true
    });
    
    if (templates.length === 0) return null;
    
    const template = templates.sort((a, b) => a.performance.used - b.performance.used)[0];
    return {
      templateId: template._id,
      title: template.structure.title,
      content: generateContentFromTemplate(template),
      seo: template.seo,
      tags: template.publishingConfig.defaultTags || [],
      category: template.publishingConfig.defaultCategory
    };
  }
  
  return null;
}

// 从模板生成内容
function generateContentFromTemplate(template) {
  let content = '';
  
  if (template.structure.introduction) {
    content += `<p>${template.structure.introduction}</p>\n\n`;
  }
  
  if (template.structure.sections) {
    template.structure.sections
      .sort((a, b) => (a.order || 0) - (b.order || 0))
      .forEach(section => {
        if (section.heading) {
          content += `<h2>${section.heading}</h2>\n`;
        }
        if (section.content) {
          switch (section.type) {
            case 'list':
              const items = section.content.split('\n').filter(item => item.trim());
              content += '<ul>\n';
              items.forEach(item => {
                content += `<li>${item.trim()}</li>\n`;
              });
              content += '</ul>\n\n';
              break;
            case 'quote':
              content += `<blockquote>${section.content}</blockquote>\n\n`;
              break;
            case 'code':
              content += `<pre><code>${section.content}</code></pre>\n\n`;
              break;
            default:
              content += `<p>${section.content}</p>\n\n`;
          }
        }
      });
  }
  
  if (template.structure.conclusion) {
    content += `<p>${template.structure.conclusion}</p>\n\n`;
  }
  
  if (template.structure.callToAction) {
    content += `<p><strong>${template.structure.callToAction}</strong></p>\n`;
  }
  
  return content;
}

// 选择发布平台
async function selectPublishingPlatforms(campaign) {
  const platforms = await PublishingPlatform.find({
    _id: { $in: campaign.platformConfig.platforms },
    status: 'active'
  });
  
  const availablePlatforms = platforms.filter(platform => platform.canPublish().canPublish);
  
  if (availablePlatforms.length === 0) return [];
  
  // 根据优先级排序
  if (campaign.platformConfig.platformPriority && campaign.platformConfig.platformPriority.length > 0) {
    const priorityMap = new Map();
    campaign.platformConfig.platformPriority.forEach(item => {
      priorityMap.set(item.platformId.toString(), item.priority);
    });
    
    availablePlatforms.sort((a, b) => {
      const aPriority = priorityMap.get(a._id.toString()) || 999;
      const bPriority = priorityMap.get(b._id.toString()) || 999;
      return aPriority - bPriority;
    });
  }
  
  return availablePlatforms;
}

// 为平台适配内容
async function adaptContentForPlatform(content, platform, campaign) {
  let adaptedContent = { ...content };
  
  // 应用平台特定适配
  if (campaign.contentAdaptation.enabled && campaign.contentAdaptation.platformSpecific) {
    const platformAdaptation = campaign.contentAdaptation.platformSpecific.find(
      adaptation => adaptation.platformId.toString() === platform._id.toString()
    );
    
    if (platformAdaptation) {
      // 标题适配
      if (platformAdaptation.titlePrefix) {
        adaptedContent.title = platformAdaptation.titlePrefix + adaptedContent.title;
      }
      if (platformAdaptation.titleSuffix) {
        adaptedContent.title += platformAdaptation.titleSuffix;
      }
      
      // 内容适配
      if (platformAdaptation.contentPrefix) {
        adaptedContent.content = platformAdaptation.contentPrefix + '\n\n' + adaptedContent.content;
      }
      if (platformAdaptation.contentSuffix) {
        adaptedContent.content += '\n\n' + platformAdaptation.contentSuffix;
      }
      
      // 标签映射
      if (platformAdaptation.tagsMapping && adaptedContent.tags) {
        adaptedContent.tags = adaptedContent.tags.map(tag => {
          const mapping = platformAdaptation.tagsMapping.find(map => map.originalTag === tag);
          return mapping ? mapping.platformTag : tag;
        });
      }
    }
  }
  
  // 平台限制检查
  if (platform.capabilities.maxTitleLength && adaptedContent.title.length > platform.capabilities.maxTitleLength) {
    adaptedContent.title = adaptedContent.title.substring(0, platform.capabilities.maxTitleLength - 3) + '...';
  }
  
  if (platform.capabilities.maxContentLength && adaptedContent.content.length > platform.capabilities.maxContentLength) {
    adaptedContent.content = adaptedContent.content.substring(0, platform.capabilities.maxContentLength - 100) + '\n\n[内容已截断]';
  }
  
  if (platform.capabilities.maxTags && adaptedContent.tags && adaptedContent.tags.length > platform.capabilities.maxTags) {
    adaptedContent.tags = adaptedContent.tags.slice(0, platform.capabilities.maxTags);
  }
  
  return {
    content: adaptedContent,
    seo: content.seo || {}
  };
}

// 执行实际发布
async function executeArticlePublishing(article) {
  try {
    article.publishing.status = 'publishing';
    await article.save();
    
    const platform = await PublishingPlatform.findById(article.platformId);
    if (!platform || !platform.canPublish().canPublish) {
      throw new Error('Platform not available for publishing');
    }
    
    let result;
    
    switch (platform.platform) {
      case 'wordpress':
        result = await publishToWordPress(article, platform);
        break;
      case 'medium':
        result = await publishToMedium(article, platform);
        break;
      case 'devto':
        result = await publishToDevTo(article, platform);
        break;
      default:
        throw new Error(`Platform ${platform.platform} not supported`);
    }
    
    // 更新发布状态
    article.publishing.status = 'published';
    article.publishing.publishedAt = new Date();
    article.publishing.platformPostId = result.postId;
    article.publishing.platformUrl = result.url;
    
    // 更新平台使用统计
    platform.usage.postsToday++;
    platform.usage.postsThisMonth++;
    platform.usage.lastPostTime = new Date();
    platform.usage.totalRequests++;
    
    await Promise.all([article.save(), platform.save()]);
    
    // 更新活动统计
    await ContentCampaign.findByIdAndUpdate(article.campaignId, {
      $inc: { 'stats.totalArticles': 1, 'stats.publishedArticles': 1 }
    });
    
    console.log(`Successfully published to ${platform.platform}: ${result.url}`);
    
  } catch (error) {
    console.error('Article publishing failed:', error);
    
    article.publishing.status = 'failed';
    article.publishing.errorMessage = error.message;
    article.publishing.retryCount++;
    
    await article.save();
    
    // 更新平台错误统计
    await PublishingPlatform.findByIdAndUpdate(article.platformId, {
      $inc: { 'usage.failedRequests': 1 }
    });
    
    // 更新活动统计
    await ContentCampaign.findByIdAndUpdate(article.campaignId, {
      $inc: { 'stats.totalArticles': 1, 'stats.failedPublications': 1 }
    });
  }
}

// 发布到 WordPress
async function publishToWordPress(article, platform) {
  const wp = new WordPressClient(platform);
  return await wp.createPost(article);
}

// 发布到 Medium
async function publishToMedium(article, platform) {
  const medium = new MediumClient(platform);
  return await medium.createPost(article);
}

// 发布到 Dev.to
async function publishToDevTo(article, platform) {
  const devto = new DevToClient(platform);
  return await devto.createPost(article);
}

// 获取已发布文章列表
exports.getPublishedArticles = async (req, res) => {
  try {
    const { campaignId, platformId, status, page = 1, limit = 20 } = req.query;
    const filter = {};
    
    if (campaignId) filter.campaignId = campaignId;
    if (platformId) filter.platformId = platformId;
    if (status) filter['publishing.status'] = status;
    
    const articles = await PublishedArticle.find(filter)
      .populate('campaignId', 'name')
      .populate('templateId', 'name category')
      .populate('platformId', 'name platform')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await PublishedArticle.countDocuments(filter);
    
    res.json({
      articles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 手动触发发布
exports.triggerContentPublish = async (req, res) => {
  try {
    const { campaignId } = req.body;
    
    const campaign = await ContentCampaign.findById(campaignId);
    if (!campaign) {
      return res.status(404).json({ error: '活动未找到' });
    }
    
    if (campaign.status !== 'active') {
      return res.status(400).json({ error: '活动未激活' });
    }
    
    await scheduleNextArticle(campaign);
    
    res.json({ success: true, message: '已创建新的发布任务' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 获取发布统计
exports.getContentPublishingStats = async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const days = parseInt(period.replace('d', ''));
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const campaignStats = await ContentCampaign.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: null,
          totalCampaigns: { $sum: 1 },
          activeCampaigns: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
          totalArticles: { $sum: '$stats.totalArticles' },
          publishedArticles: { $sum: '$stats.publishedArticles' },
          totalViews: { $sum: '$stats.totalViews' },
          totalShares: { $sum: '$stats.totalShares' }
        }
      }
    ]);
    
    const platformStats = await PublishedArticle.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $lookup: {
          from: 'publishingplatforms',
          localField: 'platformId',
          foreignField: '_id',
          as: 'platform'
        }
      },
      { $unwind: '$platform' },
      {
        $group: {
          _id: '$platform.platform',
          articles: { $sum: 1 },
          published: { $sum: { $cond: [{ $eq: ['$publishing.status', 'published'] }, 1, 0] } },
          avgViews: { $avg: '$metrics.views' },
          avgShares: { $avg: '$metrics.shares' },
          avgComments: { $avg: '$metrics.comments' }
        }
      }
    ]);
    
    const result = campaignStats[0] || {
      totalCampaigns: 0,
      activeCampaigns: 0,
      totalArticles: 0,
      publishedArticles: 0,
      totalViews: 0,
      totalShares: 0
    };
    
    // 计算成功率
    if (result.totalArticles > 0) {
      result.successRate = ((result.publishedArticles / result.totalArticles) * 100).toFixed(2);
    } else {
      result.successRate = 0;
    }
    
    res.json({
      overview: result,
      platformBreakdown: platformStats,
      period: `${days} days`
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 定时任务：执行待发布的文章
cron.schedule('*/10 * * * *', async () => {
  try {
    const now = new Date();
    const articlesToPublish = await PublishedArticle.find({
      'publishing.status': 'queued',
      'publishing.scheduledFor': { $lte: now }
    }).limit(5); // 限制并发数量
    
    for (const article of articlesToPublish) {
      await executeArticlePublishing(article);
      // 文章间添加延迟
      await new Promise(resolve => setTimeout(resolve, 60000)); // 1分钟延迟
    }
  } catch (error) {
    console.error('Content publishing cron job error:', error);
  }
});

// 定时任务：为活跃活动调度新文章
cron.schedule('0 */8 * * *', async () => {
  try {
    const now = new Date();
    const campaignsToSchedule = await ContentCampaign.find({
      status: 'active',
      'scheduling.nextPublishTime': { $lte: now }
    });
    
    for (const campaign of campaignsToSchedule) {
      await scheduleNextArticle(campaign);
    }
  } catch (error) {
    console.error('Content campaign scheduling error:', error);
  }
});

module.exports = exports;