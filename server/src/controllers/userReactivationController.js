const User = require('../models/User');
const { sendEmail } = require('../services/emailService');

// 获取不活跃用户列表
exports.getInactiveUsers = async (req, res) => {
  try {
    const { daysInactive = 0, limit = 100 } = req.query;
    const daysInactiveNum = parseInt(daysInactive);
    
    let userQuery = {};
    let inactiveQuery = {};
    
    if (daysInactiveNum > 0) {
      // 计算不活跃的时间节点
      const inactiveDate = new Date();
      inactiveDate.setDate(inactiveDate.getDate() - daysInactiveNum);
      
      // 查找指定天数内不活跃的用户
      userQuery = {
        $or: [
          { lastActiveAt: { $lt: inactiveDate } },
          { lastActiveAt: { $exists: false } } // 没有lastActiveAt字段的用户也算不活跃
        ]
      };
      
      inactiveQuery = {
        $or: [
          { lastActiveAt: { $lt: inactiveDate } },
          { lastActiveAt: { $exists: false } }
        ]
      };
    }
    // 如果 daysInactive = 0，显示所有用户（不添加时间筛选条件）
    
    // 查找用户（移除过于严格的邮件验证要求）
    const users = await User.find(userQuery)
      .select('username email createdAt lastActiveAt isEmailVerified')
      .sort({ createdAt: -1 }) // 按注册时间倒序排列
      .limit(parseInt(limit));
    
    // 统计信息
    let totalInactive = 0;
    if (daysInactiveNum > 0) {
      totalInactive = await User.countDocuments(inactiveQuery);
    }
    
    const totalUsers = await User.countDocuments({});
    
    res.json({
      users: users,
      stats: {
        totalInactiveUsers: totalInactive,
        totalUsers: totalUsers,
        inactivePercentage: totalUsers > 0 ? ((totalInactive / totalUsers) * 100).toFixed(1) : '0',
        daysInactive: daysInactiveNum
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: error.message });
  }
};

// 创建用户激活邮件模板
const createReactivationEmailTemplate = (user) => {
  const emailTemplate = {
    subject: `${user.username}, we need your honest feedback about AI-Pomo`,
    
    html: `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Feedback Request - AI-Pomo</title>
        <style>
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background-color: #f5f7fa; 
                line-height: 1.6; 
                color: #2c3e50;
            }
            .container { 
                max-width: 600px; 
                margin: 0 auto; 
                background-color: white; 
                border-radius: 12px; 
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                overflow: hidden;
            }
            .header { 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                color: white; 
                padding: 40px 30px; 
                text-align: center; 
            }
            .header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 600;
                letter-spacing: -0.5px;
            }
            .header p {
                margin: 8px 0 0 0;
                font-size: 16px;
                opacity: 0.9;
            }
            .content { 
                padding: 40px 30px; 
                color: #2c3e50; 
            }
            .personal { 
                background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 100%); 
                padding: 25px; 
                border-radius: 8px; 
                margin: 25px 0;
                border-left: 4px solid #667eea;
            }
            .personal p {
                margin: 0 0 12px 0;
            }
            .personal p:last-child {
                margin-bottom: 0;
            }
            .feature-list {
                background: #f8f9fa;
                padding: 25px;
                border-radius: 8px;
                margin: 25px 0;
            }
            .feature-list h3 {
                margin-top: 0;
                color: #2c3e50;
                font-size: 18px;
            }
            .feature-list ul {
                margin: 15px 0;
                padding-left: 20px;
            }
            .feature-list li {
                margin: 8px 0;
                color: #495057;
            }
            .example-box {
                background: linear-gradient(135deg, #e8f5e8 0%, #d4f4dd 100%);
                border: 1px solid #c3e6cb;
                border-radius: 8px;
                padding: 25px;
                margin: 25px 0;
            }
            .example-box h3 {
                margin-top: 0;
                color: #155724;
                font-size: 18px;
            }
            .example-box p {
                margin: 10px 0;
                color: #155724;
            }
            .highlight { 
                background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%); 
                padding: 25px; 
                border: 1px solid #ffeaa7;
                border-radius: 8px; 
                margin: 25px 0; 
            }
            .highlight h3 {
                margin-top: 0;
                color: #856404;
                font-size: 18px;
            }
            .highlight p {
                color: #856404;
                margin: 12px 0;
            }
            .highlight ul {
                color: #856404;
                margin: 15px 0;
                padding-left: 20px;
            }
            .highlight li {
                margin: 8px 0;
            }
            .button { 
                display: inline-block; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                color: white; 
                padding: 15px 30px; 
                text-decoration: none; 
                border-radius: 6px; 
                font-weight: 600; 
                margin: 20px 0;
                box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
                transition: transform 0.2s ease;
            }
            .button:hover {
                transform: translateY(-1px);
            }
            .improvements {
                background: #f8f9fa;
                padding: 25px;
                border-radius: 8px;
                margin: 25px 0;
            }
            .improvements h3 {
                margin-top: 0;
                color: #2c3e50;
            }
            .improvements ul {
                margin: 15px 0;
                padding-left: 20px;
            }
            .improvements li {
                margin: 8px 0;
                color: #495057;
            }
            .promise {
                background: linear-gradient(135deg, #f0f4ff 0%, #e6f2ff 100%);
                padding: 25px;
                border-radius: 8px;
                margin: 25px 0;
                border-left: 4px solid #667eea;
            }
            .promise h3 {
                margin-top: 0;
                color: #2c3e50;
            }
            .footer { 
                background: #f8f9fa; 
                padding: 25px; 
                text-align: center; 
                color: #6c757d; 
                font-size: 13px; 
                border-top: 1px solid #e9ecef; 
            }
            .footer a {
                color: #667eea;
                text-decoration: none;
            }
            .footer a:hover {
                text-decoration: underline;
            }
            @media (max-width: 640px) {
                body { padding: 10px; }
                .content { padding: 25px 20px; }
                .header { padding: 30px 20px; }
                .personal, .feature-list, .example-box, .highlight, .improvements, .promise { padding: 20px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🍅 AI-Pomo Team</h1>
                <p>Personal feedback request</p>
            </div>
            
            <div class="content">
                <div class="personal">
                    <p><strong>Hello ${user.username},</strong></p>
                    <p>I hope this message finds you well. As a member of the AI-Pomo team, I wanted to reach out personally regarding your experience with our productivity tool.</p>
                    <p>We noticed you signed up for AI-Pomo but haven't been active recently. Rather than sending you generic marketing emails, I wanted to have an honest conversation about your experience.</p>
                </div>
                
                <div class="feature-list">
                    <h3>🤔 What AI-Pomo Actually Does</h3>
                    <p>We built AI-Pomo to solve real productivity struggles:</p>
                    <ul>
                        <li><strong>🎯 When you have big projects</strong> - AI helps break them into manageable 25-minute tasks</li>
                        <li><strong>⏰ When you lose track of time</strong> - Pomodoro timer keeps you focused and prevents burnout</li>
                        <li><strong>📊 When you wonder "where did my day go?"</strong> - Visual timeline shows exactly how you spent your time</li>
                        <li><strong>🎮 When work feels overwhelming</strong> - XP points and achievements make progress feel rewarding</li>
                    </ul>
                </div>
                
                <div class="example-box">
                    <h3>💡 Real Example</h3>
                    <p><em>"I need to write a report"</em> becomes:</p>
                    <p><strong>✅ Research topic (25 min) → ✅ Create outline (25 min) → ✅ Write introduction (25 min)</strong></p>
                    <p>Instead of staring at a blank page for 2 hours, you get 3 clear wins and actual progress.</p>
                </div>
                
                <div class="improvements">
                    <h3>🚀 What We're Working On</h3>
                    <p>Since you last visited, we've been busy improving:</p>
                    <ul>
                        <li>Better AI project suggestions (less generic, more helpful)</li>
                        <li>Smoother timer experience</li>
                        <li>More intuitive interface</li>
                        <li>Enhanced project tracking and analytics</li>
                        <li>Better mobile experience</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="https://ai-pomo.com/login" class="button">Give Us Another Try →</a>
                </div>
                
                <div class="highlight">
                    <h3>💰 Your Feedback Has Real Value</h3>
                    <p>As a startup, we genuinely value user insights. To show our appreciation, we're offering compensation for detailed feedback:</p>
                    <ul>
                        <li>✅ Use AI-Pomo for at least 3 work sessions</li>
                        <li>✅ Share honest feedback about your experience</li>
                        <li>✅ Suggest specific improvements or features</li>
                    </ul>
                    <p><strong>💵 Compensation: Up to 10 USDT</strong> for comprehensive feedback that helps us improve.</p>
                    <div style="text-align: center;">
                        <a href="mailto:<EMAIL>?subject=User Feedback - ${user.username}" class="button">Share Your Experience</a>
                    </div>
                </div>
                
                <div class="promise">
                    <h3>🤝 Our Promise</h3>
                    <p>We're not another faceless tech company. We're a small team genuinely trying to build something that helps people get things done without burning out. Your feedback directly shapes what we build next.</p>
                    
                    <p>If AI-Pomo isn't for you, no worries - just hit unsubscribe below. But if you're willing to give a new product a chance to earn your trust, we'd love to have you back.</p>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="https://ai-pomo.com/login" class="button">Give AI-Pomo Another Try</a>
                </div>
                
                <p style="margin-top: 40px;">Thanks for giving us a shot,<br>
                <strong>The AI-Pomo Team</strong></p>
            </div>
            
            <div class="footer">
                <p>If you don't want to receive such emails, <a href="https://ai-pomo.com/unsubscribe?email=${user.email}">click here to unsubscribe</a></p>
                <p>AI-Pomo - Making Time Management Smarter</p>
            </div>
        </div>
    </body>
    </html>
    `,
    
    text: `
    Hi ${user.username},

    We noticed you tried AI-Pomo but haven't been back recently. That's totally fine - we're a new product and we know we're not perfect yet.

    What AI-Pomo Actually Does:
    • When you have big projects - AI helps break them into manageable 25-minute tasks
    • When you lose track of time - Pomodoro timer keeps you focused and prevents burnout  
    • When you wonder "where did my day go?" - Visual timeline shows exactly how you spent your time
    • When work feels overwhelming - XP points and achievements make progress feel rewarding

    Real Example:
    "I need to write a report" becomes:
    ✅ Research topic (25 min) → ✅ Create outline (25 min) → ✅ Write introduction (25 min)
    Instead of staring at a blank page for 2 hours, you get 3 clear wins and actual progress.

    We'll Pay for Your Feedback (Seriously):
    As a new product, your honest feedback is incredibly valuable to us. If you:
    • Try AI-Pomo for at least 3 Pomodoro sessions
    • Share detailed feedback about what worked/didn't work  
    • Suggest specific improvements
    
    We'll send you up to 10 USDT as a thank you. No tricks, no catches.
    
    Share feedback: <EMAIL>

    Give Us Another Try: https://ai-pomo.com/login

    We Need Your Voice! Your opinion is very important to us:
    • What made you stop using AI-Pomo?
    • Which features can we improve?
    • What new features would you like to see?

    Share Feedback: https://ai-pomo.com/feedback

    Special Offer: Returning users get 30 days free trial of Premium features!

    Looking forward to your return!

    Best regards,
    AI-Pomo Team

    ---
    If you don't want to receive such emails, please visit: https://ai-pomo.com/unsubscribe?email=${user.email}
    `
  };
  
  return emailTemplate;
};

// 发送用户激活邮件
exports.sendReactivationEmail = async (req, res) => {
  try {
    console.log('📧 sendReactivationEmail called with:', {
      body: req.body,
      userIds: req.body.userIds,
      testEmail: req.body.testEmail,
      bodyType: typeof req.body,
      userIdsType: typeof req.body.userIds,
      userIdsLength: req.body.userIds ? req.body.userIds.length : 'undefined'
    });
    
    const { userIds, testEmail } = req.body;
    
    // 如果不是测试模式，需要检查用户ID
    if (!testEmail && (!userIds || !Array.isArray(userIds) || userIds.length === 0)) {
      return res.status(400).json({ error: '请选择要发送邮件的用户' });
    }
    
    // 如果是测试模式，只发送给测试邮箱
    if (testEmail) {
      const testUser = { username: 'Test User', email: testEmail };
      const emailTemplate = createReactivationEmailTemplate(testUser);
      
      await sendEmail({
        to: testEmail,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
        text: emailTemplate.text
      });
      
      return res.json({ 
        success: true, 
        message: `Test email sent to ${testEmail}`,
        testMode: true
      });
    }
    
    // 获取用户信息 - 放宽查询条件以避免404错误
    const users = await User.find({
      _id: { $in: userIds },
      // 移除过于严格的邮件验证要求，因为很多用户可能没有验证邮箱
      // isEmailVerified: true,
      // 只排除明确取消订阅的用户
      unsubscribedFromMarketing: { $ne: true }
    }).select('username email isEmailVerified unsubscribedFromMarketing');
    
    if (users.length === 0) {
      console.log('No users found for userIds:', userIds);
      // 检查用户是否存在但不符合条件
      const allUsers = await User.find({ _id: { $in: userIds } }).select('username email isEmailVerified unsubscribedFromMarketing');
      console.log('All users found (regardless of conditions):', allUsers.length);

      if (allUsers.length === 0) {
        return res.status(404).json({ error: '没有找到指定的用户ID' });
      } else {
        const unsubscribedCount = allUsers.filter(u => u.unsubscribedFromMarketing === true).length;
        return res.status(400).json({
          error: `找到 ${allUsers.length} 个用户，但其中 ${unsubscribedCount} 个已取消订阅，无法发送邮件`,
          details: {
            totalFound: allUsers.length,
            unsubscribed: unsubscribedCount,
            eligible: allUsers.length - unsubscribedCount
          }
        });
      }
    }
    
    const results = {
      total: users.length,
      sent: 0,
      failed: 0,
      errors: []
    };
    
    console.log(`开始发送邮件给 ${users.length} 个用户`);

    // 批量发送邮件
    for (const user of users) {
      try {
        console.log(`正在发送邮件给: ${user.email}`);
        const emailTemplate = createReactivationEmailTemplate(user);

        await sendEmail({
          to: user.email,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          text: emailTemplate.text
        });

        results.sent++;
        console.log(`邮件发送成功: ${user.email}`);

        // 更新用户的最后营销邮件发送时间
        await User.findByIdAndUpdate(user._id, {
          lastMarketingEmailSent: new Date()
        });

        // 为了避免被标记为垃圾邮件，每封邮件间隔2秒
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (emailError) {
        console.error(`发送邮件给 ${user.email} 失败:`, emailError);
        results.failed++;
        results.errors.push({
          user: user.email,
          error: emailError.message
        });
      }
    }
    
    res.json({
      success: true,
      message: `邮件发送完成：成功 ${results.sent} 封，失败 ${results.failed} 封`,
      results
    });
    
  } catch (error) {
    console.error('发送激活邮件错误:', error);
    res.status(500).json({ error: error.message });
  }
};

// 获取邮件发送统计
exports.getEmailStats = async (req, res) => {
  try {
    const { days = 7 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    
    // 统计最近发送的营销邮件
    const recentEmailUsers = await User.countDocuments({
      lastMarketingEmailSent: { $gte: startDate }
    });
    
    // 统计回归用户（发送邮件后又活跃的用户）
    const returnedUsers = await User.countDocuments({
      lastMarketingEmailSent: { $gte: startDate },
      lastActiveAt: { $gte: startDate }
    });
    
    // 统计取消订阅的用户
    const unsubscribedUsers = await User.countDocuments({
      unsubscribedFromMarketing: true
    });
    
    // 计算回归率
    const returnRate = recentEmailUsers > 0 ? ((returnedUsers / recentEmailUsers) * 100).toFixed(1) : 0;
    
    res.json({
      stats: {
        emailsSentLast7Days: recentEmailUsers,
        usersReturned: returnedUsers,
        returnRate: `${returnRate}%`,
        unsubscribedUsers,
        period: `${days} 天`
      }
    });
  } catch (error) {
    console.error('获取邮件统计错误:', error);
    res.status(500).json({ error: error.message });
  }
};

// 创建用户取消订阅功能
exports.unsubscribeUser = async (req, res) => {
  try {
    const { email } = req.query;
    
    if (!email) {
      return res.status(400).json({ error: '邮箱地址不能为空' });
    }
    
    const user = await User.findOneAndUpdate(
      { email: email },
      { 
        unsubscribedFromMarketing: true,
        unsubscribedAt: new Date()
      },
      { new: true }
    );
    
    if (!user) {
      return res.status(404).json({ error: '用户未找到' });
    }
    
    res.json({
      success: true,
      message: '已成功取消订阅营销邮件'
    });
    
  } catch (error) {
    console.error('取消订阅错误:', error);
    res.status(500).json({ error: error.message });
  }
};

// 分析用户活跃度趋势
exports.getUserActivityTrends = async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    
    // 按天统计用户活跃度
    const dailyActivity = await User.aggregate([
      {
        $match: {
          lastActiveAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$lastActiveAt" }
          },
          activeUsers: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    // 按注册时间分组分析流失率
    const cohortAnalysis = await User.aggregate([
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m", date: "$createdAt" }
          },
          totalUsers: { $sum: 1 },
          activeUsers: {
            $sum: {
              $cond: [
                { $gte: ["$lastActiveAt", startDate] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $addFields: {
          retentionRate: {
            $multiply: [
              { $divide: ["$activeUsers", "$totalUsers"] },
              100
            ]
          }
        }
      },
      {
        $sort: { _id: -1 }
      }
    ]);
    
    res.json({
      dailyActivity,
      cohortAnalysis,
      period: `${days} 天`
    });
    
  } catch (error) {
    console.error('获取用户活跃度趋势错误:', error);
    res.status(500).json({ error: error.message });
  }
};

module.exports = exports;exports.getEmailStats = async (req, res) => { res.json({ stats: { emailsSentLast7Days: 0, usersReturned: 0, returnRate: '0%' } }); };
