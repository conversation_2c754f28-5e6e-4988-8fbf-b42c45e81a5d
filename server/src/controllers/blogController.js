const BlogPost = require('../models/BlogPost');
const BlogCategory = require('../models/BlogCategory');
const BlogImage = require('../models/BlogImage');
const User = require('../models/User');
const slugify = require('../utils/slugify');
const { clearClusterPageCache } = require('../middleware/cache');

// Get all published blog posts
exports.getBlogPosts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const category = req.query.category;
    const tag = req.query.tag;
    const search = req.query.search;

    let query = { published: true };

    if (category) {
      query.category = category;
    }

    if (tag) {
      query.tags = tag;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { excerpt: { $regex: search, $options: 'i' } },
        { seoKeywords: { $regex: search, $options: 'i' } }
      ];
    }

    const posts = await BlogPost.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('author', 'name avatar')
      .select('title slug excerpt coverImage category tags readTime createdAt author published featured')
      .lean();

    const total = await BlogPost.countDocuments(query);

    // Add caching headers for better performance
    res.set('Cache-Control', 'public, max-age=300'); // 5 minutes cache

    res.json({
      posts,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all blog posts (including unpublished) for admin
exports.getAllBlogPosts = async (req, res) => {
  try {
    const posts = await BlogPost.find()
      .sort({ createdAt: -1 })
      .populate('author', 'name avatar');

    res.json(posts);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get a single blog post by slug
exports.getBlogPostBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const post = await BlogPost.findOne({
      slug,
      published: true
    }).populate('author', 'name avatar');

    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }

    // Ensure author information is available (simplified)
    if (!post.author) {
      post.author = { name: 'AI Pomo Team', avatar: '' };
    }

    // Add caching headers for better performance
    res.set('Cache-Control', 'public, max-age=600'); // 10 minutes cache for individual posts

    res.json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get a single blog post by ID (for admin editing)
exports.getBlogPostById = async (req, res) => {
  try {
    const { id } = req.params;

    const post = await BlogPost.findById(id)
      .populate('author', 'name avatar');

    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }

    // Ensure author information is available
    if (!post.author) {
      // Find a default admin user to use as author
      const adminUser = await User.findOne({ isAdmin: true });

      // If an admin user is found, use it as the author
      if (adminUser) {
        post.author = adminUser;
        await post.save();
      } else {
        // If no admin user is found, create a placeholder author object
        post.author = { name: 'AI Pomo Team', avatar: '' };
      }
    }

    res.json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new blog post
exports.createBlogPost = async (req, res) => {
  try {
    const {
      title, content, excerpt, coverImage,
      tags, category, readTime, published,
      featured, seoTitle, seoDescription, seoKeywords
    } = req.body;

    const postTags = tags || [];

    const post = new BlogPost({
      title,
      slug: slugify(title),
      content,
      excerpt,
      author: req.user.id,
      coverImage,
      tags: postTags,
      category,
      readTime,
      published: published || false,
      featured: featured || false,
      seoTitle: seoTitle || title,
      seoDescription: seoDescription || excerpt,
      seoKeywords: seoKeywords || postTags.join(', ')
    });

    await post.save();

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    res.status(201).json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update a blog post
exports.updateBlogPost = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title, content, excerpt, coverImage,
      tags, category, readTime, published,
      featured, seoTitle, seoDescription, seoKeywords
    } = req.body;

    let post = await BlogPost.findById(id);

    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }

    // Update fields
    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (excerpt !== undefined) updateData.excerpt = excerpt;
    if (coverImage !== undefined) updateData.coverImage = coverImage;
    if (tags !== undefined) updateData.tags = tags;
    if (category !== undefined) updateData.category = category;
    if (readTime !== undefined) updateData.readTime = readTime;
    if (published !== undefined) updateData.published = published;
    if (featured !== undefined) updateData.featured = featured;
    if (seoTitle !== undefined) updateData.seoTitle = seoTitle;
    if (seoDescription !== undefined) updateData.seoDescription = seoDescription;
    if (seoKeywords !== undefined) updateData.seoKeywords = seoKeywords;

    // If title is being updated, slug will be regenerated by the pre-save hook

    // Update post
    post = await BlogPost.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    res.json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete a blog post
exports.deleteBlogPost = async (req, res) => {
  try {
    const { id } = req.params;

    const post = await BlogPost.findByIdAndDelete(id);

    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    res.json({ message: 'Blog post removed' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all categories
exports.getCategories = async (req, res) => {
  try {
    const categories = await BlogCategory.find().sort({ name: 1 });
    res.json(categories);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new category
exports.createCategory = async (req, res) => {
  try {
    const { name, description } = req.body;

    const category = new BlogCategory({
      name,
      description
    });

    await category.save();

    res.status(201).json(category);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Import multiple blog posts from markdown content
exports.importBlogPosts = async (req, res) => {
  try {
    const { articles } = req.body;
    
    if (!articles || !Array.isArray(articles) || articles.length === 0) {
      return res.status(400).json({
        message: 'Articles array is required',
        success: false
      });
    }

    const importResults = [];
    const errors = [];

    for (let i = 0; i < articles.length; i++) {
      const article = articles[i];
      
      try {
        // Validate required fields
        if (!article.title || !article.content) {
          errors.push({
            index: i,
            error: 'Title and content are required',
            title: article.title || 'Untitled'
          });
          continue;
        }

        // Auto-determine category based on content and tags
        let selectedCategory = 'productivity'; // Default category
        const contentLower = (article.content + ' ' + article.title + ' ' + (article.tags || []).join(' ')).toLowerCase();
        
        if (contentLower.includes('time management') || contentLower.includes('pomodoro') || contentLower.includes('focus')) {
          selectedCategory = 'time-management';
        } else if (contentLower.includes('productivity') || contentLower.includes('efficiency')) {
          selectedCategory = 'productivity';
        } else if (contentLower.includes('technology') || contentLower.includes('software') || contentLower.includes('app')) {
          selectedCategory = 'technology';
        } else if (contentLower.includes('lifestyle') || contentLower.includes('habits') || contentLower.includes('mindfulness')) {
          selectedCategory = 'lifestyle';
        } else if (contentLower.includes('business') || contentLower.includes('entrepreneur')) {
          selectedCategory = 'business';
        }

        // Auto-select cover image using Pexels API based on tags and category
        let selectedCoverImage = article.coverImage;
        if (!selectedCoverImage) {
          try {
            // Create search query based on article tags and category
            let searchQuery = 'productivity'; // Default search term
            
            if (article.tags && article.tags.length > 0) {
              // Use the first relevant tag as search term
              const usefulTags = article.tags.filter(tag => 
                !['article', 'blog', 'post', 'content'].includes(tag.toLowerCase())
              );
              if (usefulTags.length > 0) {
                searchQuery = usefulTags[0];
              }
            } else {
              // Use category-based search terms
              const categorySearchTerms = {
                'time-management': 'time management productivity',
                'productivity': 'productivity workspace',
                'technology': 'technology computer',
                'lifestyle': 'lifestyle wellness',
                'business': 'business office'
              };
              searchQuery = categorySearchTerms[selectedCategory] || 'productivity';
            }

            // Use Pexels API to get a random image
            const axios = require('axios');
            const PEXELS_API_KEY = process.env.PEXELS_API_KEY || process.env.REACT_APP_PEXELS_API_KEY;
            
            if (PEXELS_API_KEY && PEXELS_API_KEY !== 'YOUR_PEXELS_API_KEY') {
              const pexelsResponse = await axios.get(
                `https://api.pexels.com/v1/search?query=${encodeURIComponent(searchQuery)}&per_page=20&page=1`,
                {
                  headers: {
                    'Authorization': PEXELS_API_KEY
                  }
                }
              );

              if (pexelsResponse.data && pexelsResponse.data.photos && pexelsResponse.data.photos.length > 0) {
                // Select a random image from the results
                const randomIndex = Math.floor(Math.random() * pexelsResponse.data.photos.length);
                const selectedPhoto = pexelsResponse.data.photos[randomIndex];
                selectedCoverImage = selectedPhoto.src.large;
              }
            }

            // Fallback to default image if Pexels fails
            if (!selectedCoverImage) {
              selectedCoverImage = 'https://images.pexels.com/photos/7776/garden-grass-meadow-green.jpg';
            }
          } catch (imageError) {
            console.error('Error selecting cover image from Pexels:', imageError);
            selectedCoverImage = 'https://images.pexels.com/photos/7776/garden-grass-meadow-green.jpg';
          }
        }

        // Create excerpt if not provided
        const postExcerpt = article.excerpt || article.description || article.content.substring(0, 160) + '...';

        // Calculate read time
        const postReadTime = article.readTime || Math.max(1, Math.ceil(article.content.split(/\s+/).length / 200));

        // Create the blog post
        const post = new BlogPost({
          title: article.title,
          slug: slugify(article.title),
          content: article.content,
          excerpt: postExcerpt,
          author: req.user.id,
          coverImage: selectedCoverImage,
          tags: article.tags || [],
          category: selectedCategory,
          readTime: postReadTime,
          published: article.published !== undefined ? article.published : true,
          featured: article.featured || false,
          seoTitle: article.seoTitle || article.title,
          seoDescription: article.seoDescription || article.description || postExcerpt,
          seoKeywords: article.seoKeywords || article.keywords || (article.tags ? article.tags.join(', ') : '')
        });

        await post.save();

        // Add to results
        importResults.push({
          id: post._id,
          title: post.title,
          slug: post.slug,
          url: `/blog/${post.slug}`,
          category: post.category,
          published: post.published,
          coverImage: post.coverImage
        });

      } catch (articleError) {
        console.error(`Error importing article ${i}:`, articleError);
        errors.push({
          index: i,
          error: articleError.message,
          title: article.title || 'Untitled'
        });
      }
    }

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    res.status(201).json({
      success: true,
      message: `Successfully imported ${importResults.length} articles${errors.length > 0 ? ` with ${errors.length} errors` : ''}`,
      imported: importResults,
      errors: errors,
      totalProcessed: articles.length,
      successCount: importResults.length,
      errorCount: errors.length
    });

  } catch (err) {
    console.error('Error importing blog posts:', err);
    res.status(500).json({
      message: 'Server error during import',
      success: false,
      error: err.message
    });
  }
};

// Create a blog post via API (for third-party integrations like n8n)
exports.createBlogPostViaApi = async (req, res) => {
  try {
    const {
      title, content, excerpt, coverImage,
      tags, category, readTime, published,
      featured, seoTitle, seoDescription, seoKeywords
    } = req.body;

    // Validate required fields
    if (!title || !content) {
      return res.status(400).json({
        message: 'Title and content are required',
        success: false
      });
    }

    // Check if category exists
    let categoryDoc;
    if (category) {
      categoryDoc = await BlogCategory.findOne({
        $or: [
          { slug: category.toLowerCase() },
          { name: new RegExp(`^${category}$`, 'i') }
        ]
      });

      if (!categoryDoc) {
        return res.status(400).json({
          message: `Category '${category}' not found`,
          success: false
        });
      }
    }

    // Create excerpt if not provided
    const postExcerpt = excerpt || content.substring(0, 160) + '...';

    // Calculate read time if not provided (average reading speed: 200 words per minute)
    const postReadTime = readTime || Math.max(1, Math.ceil(content.split(/\s+/).length / 200));

    // Auto-select cover image if not provided
    let selectedCoverImage = coverImage;
    if (!selectedCoverImage) {
      try {
        // Try to find an appropriate image based on category or tags
        const imageQuery = { isActive: true };

        if (categoryDoc) {
          // First try to match by category
          imageQuery.category = categoryDoc.slug;
        } else if (tags && tags.length > 0) {
          // If no category match, try to match by tags
          imageQuery.tags = { $in: tags };
        }

        // Get a random image from matching criteria
        const imageCount = await BlogImage.countDocuments(imageQuery);
        if (imageCount > 0) {
          const randomIndex = Math.floor(Math.random() * imageCount);
          const selectedImage = await BlogImage.findOne(imageQuery).skip(randomIndex);

          if (selectedImage) {
            selectedCoverImage = selectedImage.url;
            // Mark the image as used
            await selectedImage.incrementUsage();
          }
        }

        // Fallback to any random image if no specific match found
        if (!selectedCoverImage) {
          const fallbackImageCount = await BlogImage.countDocuments({ isActive: true });
          if (fallbackImageCount > 0) {
            const randomIndex = Math.floor(Math.random() * fallbackImageCount);
            const fallbackImage = await BlogImage.findOne({ isActive: true }).skip(randomIndex);
            if (fallbackImage) {
              selectedCoverImage = fallbackImage.url;
              await fallbackImage.incrementUsage();
            }
          }
        }

        // Final fallback to default image
        if (!selectedCoverImage) {
          selectedCoverImage = 'https://images.unsplash.com/photo-1513128034602-7814ccaddd4e';
        }
      } catch (imageError) {
        console.error('Error selecting cover image:', imageError);
        selectedCoverImage = 'https://images.unsplash.com/photo-1513128034602-7814ccaddd4e';
      }
    }

    // Create the blog post
    const post = new BlogPost({
      title,
      slug: slugify(title),
      content,
      excerpt: postExcerpt,
      author: req.user.id,
      coverImage: selectedCoverImage,
      tags: tags || [],
      category: categoryDoc ? categoryDoc.slug : 'uncategorized',
      readTime: postReadTime,
      published: published !== undefined ? published : true, // Default to published
      featured: featured || false,
      seoTitle: seoTitle || title,
      seoDescription: seoDescription || postExcerpt,
      seoKeywords: seoKeywords || (tags ? tags.join(', ') : '')
    });

    await post.save();

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    // Populate the author information
    const populatedPost = await BlogPost.findById(post._id).populate('author', 'name avatar');

    res.status(201).json({
      success: true,
      message: 'Blog post created successfully',
      post: {
        id: populatedPost._id,
        title: populatedPost.title,
        slug: populatedPost.slug,
        published: populatedPost.published,
        createdAt: populatedPost.createdAt,
        author: populatedPost.author
      }
    });
  } catch (err) {
    console.error('Error creating blog post via API:', err);
    res.status(500).json({
      message: 'Server error',
      success: false,
      error: err.message
    });
  }
};
