const { EmailTemplate, EmailSequence, EmailSegment, EmailCampaign } = require('../models/EmailCampaign');
const EmailLead = require('../models/EmailLead');
const EmailList = require('../models/EmailList');
const nodemailer = require('nodemailer');
const cron = require('node-cron');

// 邮件发送配置
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });
};

// === 邮件模板管理 ===

// 创建邮件模板
exports.createEmailTemplate = async (req, res) => {
  try {
    const { name, subject, htmlContent, textContent, type, variables } = req.body;
    
    const template = new EmailTemplate({
      name,
      subject,
      htmlContent,
      textContent,
      type,
      variables: variables || []
    });
    
    await template.save();
    res.json({ success: true, template });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 获取所有模板
exports.getEmailTemplates = async (req, res) => {
  try {
    const { type, page = 1, limit = 20 } = req.query;
    const filter = type ? { type } : {};
    
    const templates = await EmailTemplate.find(filter)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
      
    const total = await EmailTemplate.countDocuments(filter);
    
    res.json({
      templates,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 更新模板
exports.updateEmailTemplate = async (req, res) => {
  try {
    const template = await EmailTemplate.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: new Date() },
      { new: true }
    );
    
    if (!template) {
      return res.status(404).json({ error: '模板未找到' });
    }
    
    res.json({ success: true, template });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 删除模板
exports.deleteEmailTemplate = async (req, res) => {
  try {
    const template = await EmailTemplate.findByIdAndDelete(req.params.id);
    if (!template) {
      return res.status(404).json({ error: '模板未找到' });
    }
    
    res.json({ success: true, message: '模板已删除' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === 邮件序列管理 ===

// 创建邮件序列
exports.createEmailSequence = async (req, res) => {
  try {
    const { name, description, trigger, emails } = req.body;
    
    const sequence = new EmailSequence({
      name,
      description,
      trigger,
      emails: emails || []
    });
    
    await sequence.save();
    res.json({ success: true, sequence });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 获取邮件序列
exports.getEmailSequences = async (req, res) => {
  try {
    const sequences = await EmailSequence.find()
      .populate('emails.templateId', 'name subject type')
      .sort({ createdAt: -1 });
      
    res.json({ sequences });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === 用户分群管理 ===

// 创建用户分群
exports.createEmailSegment = async (req, res) => {
  try {
    const { name, description, filters, emailListId } = req.body;
    
    // 应用过滤器获取匹配的线索
    const leads = await applySegmentFilters(filters, emailListId);
    
    const segment = new EmailSegment({
      name,
      description,
      filters,
      emailList: emailListId,
      leads: leads.map(lead => lead._id),
      totalLeads: leads.length
    });
    
    await segment.save();
    res.json({ success: true, segment, matchedLeads: leads.length });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 应用分群过滤器
async function applySegmentFilters(filters, emailListId) {
  let query = {};
  
  if (emailListId) {
    const emailList = await EmailList.findById(emailListId).populate('subscribers');
    query._id = { $in: emailList.subscribers.map(sub => sub._id) };
  }
  
  // 构建查询条件
  filters.forEach(filter => {
    switch (filter.operator) {
      case 'equals':
        query[filter.field] = filter.value;
        break;
      case 'contains':
        query[filter.field] = { $regex: filter.value, $options: 'i' };
        break;
      case 'not_equals':
        query[filter.field] = { $ne: filter.value };
        break;
      case 'greater_than':
        query[filter.field] = { $gt: filter.value };
        break;
      case 'less_than':
        query[filter.field] = { $lt: filter.value };
        break;
      case 'in':
        query[filter.field] = { $in: filter.value };
        break;
    }
  });
  
  return await EmailLead.find(query);
}

// 获取用户分群
exports.getEmailSegments = async (req, res) => {
  try {
    const segments = await EmailSegment.find()
      .populate('emailList', 'name')
      .sort({ createdAt: -1 });
      
    res.json({ segments });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === 邮件营销活动管理 ===

// 创建营销活动
exports.createEmailCampaign = async (req, res) => {
  try {
    const {
      name,
      description,
      type,
      templateId,
      subject,
      textContent,
      htmlContent,
      targeting,
      scheduling,
      personalization,
      abTest
    } = req.body;
    
    // 计算目标受众数量
    const audienceCount = await calculateAudienceSize(targeting);
    
    const campaign = new EmailCampaign({
      name,
      description,
      type,
      templateId,
      subject,
      textContent,
      htmlContent,
      targeting: {
        ...targeting,
        estimatedReach: audienceCount
      },
      scheduling: scheduling || {},
      personalization: personalization || {},
      abTest: abTest || {},
      stats: {
        totalRecipients: audienceCount
      },
      createdBy: req.user._id
    });
    
    await campaign.save();
    res.json({ success: true, campaign, estimatedReach: audienceCount });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 计算目标受众大小
async function calculateAudienceSize(targeting) {
  let totalCount = 0;
  
  // 从邮件列表计算
  if (targeting.emailLists && targeting.emailLists.length > 0) {
    for (const listId of targeting.emailLists) {
      const list = await EmailList.findById(listId);
      if (list) {
        totalCount += list.subscriberCount || 0;
      }
    }
  }
  
  // 从分群计算
  if (targeting.segments && targeting.segments.length > 0) {
    for (const segmentId of targeting.segments) {
      const segment = await EmailSegment.findById(segmentId);
      if (segment) {
        totalCount += segment.totalLeads || 0;
      }
    }
  }
  
  return totalCount;
}

// 获取营销活动列表
exports.getEmailCampaigns = async (req, res) => {
  try {
    const { status, type, page = 1, limit = 20 } = req.query;
    const filter = {};
    
    if (status) filter['scheduling.status'] = status;
    if (type) filter.type = type;
    
    const campaigns = await EmailCampaign.find(filter)
      .populate('templateId', 'name type')
      .populate('targeting.emailLists', 'name subscriberCount')
      .populate('targeting.segments', 'name totalLeads')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
      
    const total = await EmailCampaign.countDocuments(filter);
    
    res.json({
      campaigns,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 获取单个营销活动详情
exports.getEmailCampaign = async (req, res) => {
  try {
    const campaign = await EmailCampaign.findById(req.params.id)
      .populate('templateId')
      .populate('targeting.emailLists', 'name subscriberCount')
      .populate('targeting.segments', 'name totalLeads')
      .populate({
        path: 'sendLog.leadId',
        select: 'email profile.occupation'
      });
      
    if (!campaign) {
      return res.status(404).json({ error: '营销活动未找到' });
    }
    
    res.json({ campaign });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 更新营销活动
exports.updateEmailCampaign = async (req, res) => {
  try {
    const campaign = await EmailCampaign.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: new Date() },
      { new: true }
    );
    
    if (!campaign) {
      return res.status(404).json({ error: '营销活动未找到' });
    }
    
    res.json({ success: true, campaign });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 启动营销活动
exports.startEmailCampaign = async (req, res) => {
  try {
    const campaign = await EmailCampaign.findById(req.params.id);
    if (!campaign) {
      return res.status(404).json({ error: '营销活动未找到' });
    }
    
    // 检查是否可以发送
    const canSend = campaign.canSend();
    if (!canSend.canSend) {
      return res.status(400).json({ error: canSend.reason });
    }
    
    // 更新状态
    campaign.scheduling.status = 'sending';
    campaign.scheduling.sentAt = new Date();
    await campaign.save();
    
    // 异步处理发送
    processCampaignSending(campaign._id);
    
    res.json({ success: true, message: '营销活动已启动' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 暂停营销活动
exports.pauseEmailCampaign = async (req, res) => {
  try {
    const campaign = await EmailCampaign.findByIdAndUpdate(
      req.params.id,
      { 'scheduling.status': 'paused' },
      { new: true }
    );
    
    if (!campaign) {
      return res.status(404).json({ error: '营销活动未找到' });
    }
    
    res.json({ success: true, message: '营销活动已暂停' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 处理营销活动发送
async function processCampaignSending(campaignId) {
  try {
    const campaign = await EmailCampaign.findById(campaignId)
      .populate('templateId')
      .populate('targeting.emailLists')
      .populate('targeting.segments');
      
    if (!campaign || campaign.scheduling.status !== 'sending') {
      return;
    }
    
    // 获取目标收件人
    const recipients = await getRecipients(campaign.targeting);
    
    // 更新发送进度
    campaign.sendingProgress.total = recipients.length;
    campaign.sendingProgress.totalBatches = Math.ceil(recipients.length / campaign.scheduling.batchSettings.batchSize);
    await campaign.save();
    
    // 分批发送
    const batchSize = campaign.scheduling.batchSettings.batchSize;
    const delay = campaign.scheduling.batchSettings.delayBetweenBatches;
    
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);
      
      // 发送批次
      await sendEmailBatch(campaign, batch);
      
      // 更新进度
      campaign.sendingProgress.current = Math.min(i + batchSize, recipients.length);
      campaign.sendingProgress.currentBatch = Math.floor(i / batchSize) + 1;
      campaign.sendingProgress.lastSentAt = new Date();
      
      if (i + batchSize < recipients.length) {
        campaign.sendingProgress.nextBatchAt = new Date(Date.now() + delay);
      }
      
      await campaign.save();
      
      // 等待下一批次
      if (i + batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // 完成发送
    campaign.scheduling.status = 'sent';
    campaign.sendingProgress.percentage = 100;
    await campaign.save();
    
    console.log(`营销活动 ${campaign.name} 发送完成`);
  } catch (error) {
    console.error('营销活动发送错误:', error);
    
    // 更新错误状态
    await EmailCampaign.findByIdAndUpdate(campaignId, {
      'scheduling.status': 'paused',
      $push: {
        errors: {
          errorType: 'sending_error',
          errorMessage: error.message,
          timestamp: new Date()
        }
      }
    });
  }
}

// 获取收件人列表
async function getRecipients(targeting) {
  let recipients = [];
  
  // 从邮件列表获取
  if (targeting.emailLists && targeting.emailLists.length > 0) {
    for (const list of targeting.emailLists) {
      const populatedList = await EmailList.findById(list._id || list).populate('subscribers');
      if (populatedList && populatedList.subscribers) {
        recipients = recipients.concat(populatedList.subscribers);
      }
    }
  }
  
  // 从分群获取
  if (targeting.segments && targeting.segments.length > 0) {
    for (const segment of targeting.segments) {
      const populatedSegment = await EmailSegment.findById(segment._id || segment).populate('leads');
      if (populatedSegment && populatedSegment.leads) {
        recipients = recipients.concat(populatedSegment.leads);
      }
    }
  }
  
  // 去重
  const uniqueRecipients = recipients.filter((recipient, index, self) => 
    index === self.findIndex(r => r.email === recipient.email)
  );
  
  // 应用标签过滤
  if (targeting.tags && targeting.tags.length > 0) {
    return uniqueRecipients.filter(recipient => 
      recipient.tags && recipient.tags.some(tag => targeting.tags.includes(tag))
    );
  }
  
  // 排除标签
  if (targeting.excludeTags && targeting.excludeTags.length > 0) {
    return uniqueRecipients.filter(recipient => 
      !recipient.tags || !recipient.tags.some(tag => targeting.excludeTags.includes(tag))
    );
  }
  
  return uniqueRecipients;
}

// 发送邮件批次
async function sendEmailBatch(campaign, recipients) {
  const transporter = createTransporter();
  
  for (const recipient of recipients) {
    try {
      // 个性化内容
      const personalizedContent = personalizeContent(campaign, recipient);
      
      // 准备邮件
      const mailOptions = {
        from: `${campaign.fromName} <${campaign.fromEmail}>`,
        to: recipient.email,
        subject: personalizedContent.subject,
        text: personalizedContent.textContent,
        html: personalizedContent.htmlContent,
        replyTo: campaign.replyToEmail
      };
      
      // 发送邮件
      const result = await transporter.sendMail(mailOptions);
      
      // 记录发送日志
      campaign.sendLog.push({
        leadId: recipient._id,
        email: recipient.email,
        status: 'sent',
        sentAt: new Date(),
        messageId: result.messageId
      });
      
      // 更新统计
      campaign.stats.emailsSent++;
      
    } catch (error) {
      console.error(`发送邮件给 ${recipient.email} 失败:`, error);
      
      // 记录错误
      campaign.errors.push({
        email: recipient.email,
        errorType: 'send_failed',
        errorMessage: error.message,
        timestamp: new Date()
      });
    }
  }
}

// 个性化内容
function personalizeContent(campaign, recipient) {
  let subject = campaign.subject;
  let textContent = campaign.textContent;
  let htmlContent = campaign.htmlContent;
  
  if (campaign.personalization.enabled) {
    // 替换个人化字段
    const replacements = {
      '{{name}}': recipient.profile?.name || '朋友',
      '{{firstName}}': recipient.profile?.firstName || '朋友',
      '{{email}}': recipient.email,
      '{{occupation}}': recipient.profile?.occupation || '',
      '{{company}}': recipient.profile?.company || ''
    };
    
    Object.keys(replacements).forEach(placeholder => {
      const value = replacements[placeholder];
      subject = subject.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
      textContent = textContent.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
      htmlContent = htmlContent.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
    });
  }
  
  return { subject, textContent, htmlContent };
}

// 获取营销活动统计
exports.getCampaignStats = async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const days = parseInt(period.replace('d', ''));
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const stats = await EmailCampaign.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: null,
          totalCampaigns: { $sum: 1 },
          totalSent: { $sum: '$stats.emailsSent' },
          totalOpened: { $sum: '$stats.emailsOpened' },
          totalClicked: { $sum: '$stats.emailsClicked' },
          totalRevenue: { $sum: '$stats.revenue' }
        }
      }
    ]);
    
    const campaignsByStatus = await EmailCampaign.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: '$scheduling.status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    const result = stats[0] || {
      totalCampaigns: 0,
      totalSent: 0,
      totalOpened: 0,
      totalClicked: 0,
      totalRevenue: 0
    };
    
    // 计算平均指标
    if (result.totalSent > 0) {
      result.avgOpenRate = ((result.totalOpened / result.totalSent) * 100).toFixed(2);
      result.avgClickRate = ((result.totalClicked / result.totalSent) * 100).toFixed(2);
    } else {
      result.avgOpenRate = 0;
      result.avgClickRate = 0;
    }
    
    res.json({
      overview: result,
      campaignsByStatus,
      period: `${days} days`
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 定时任务：处理计划发送的营销活动
cron.schedule('*/5 * * * *', async () => {
  try {
    const now = new Date();
    const scheduledCampaigns = await EmailCampaign.find({
      'scheduling.status': 'scheduled',
      'scheduling.scheduledAt': { $lte: now }
    });
    
    for (const campaign of scheduledCampaigns) {
      console.log(`启动计划营销活动: ${campaign.name}`);
      campaign.scheduling.status = 'sending';
      await campaign.save();
      
      // 异步处理发送
      processCampaignSending(campaign._id);
    }
  } catch (error) {
    console.error('定时任务错误:', error);
  }
});

module.exports = exports;