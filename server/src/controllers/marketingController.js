const MarketingCampaign = require('../models/MarketingCampaign');
const EmailLead = require('../models/EmailLead');
const ContentDistribution = require('../models/ContentDistribution');
const BacklinkOutreach = require('../models/BacklinkOutreach');
const BlogPost = require('../models/BlogPost');
const nodemailer = require('nodemailer');
const axios = require('axios');

// Marketing Dashboard Overview
exports.getDashboard = async (req, res) => {
  try {
    // Get date ranges for analytics
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get current totals
    const totalLeads = await EmailLead.countDocuments();
    const activeLeads = await EmailLead.countDocuments({ status: { $in: ['verified', 'subscribed'] } });
    const unsubscribedLeads = await EmailLead.countDocuments({ status: 'unsubscribed' });

    // Get recent leads (last 30 days)
    const recentLeads = await EmailLead.find({ createdAt: { $gte: thirtyDaysAgo } })
      .sort({ createdAt: -1 })
      .limit(50)
      .select('email source.type status createdAt engagement.score profile.occupation');

    // Calculate lead sources breakdown
    const sourcesBreakdown = await EmailLead.aggregate([
      { $group: { _id: '$source.type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Calculate daily lead trends (last 7 days)
    const dailyTrends = await EmailLead.aggregate([
      { $match: { createdAt: { $gte: sevenDaysAgo } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          leads: { $sum: 1 },
          verified: { $sum: { $cond: [{ $eq: ['$status', 'verified'] }, 1, 0] } },
          subscribed: { $sum: { $cond: [{ $eq: ['$status', 'subscribed'] }, 1, 0] } }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Calculate conversion rate
    const conversionRate = totalLeads > 0 ? ((activeLeads / totalLeads) * 100).toFixed(2) : 0;

    // Get top performing lead magnets
    const leadMagnets = await MarketingCampaign.find({ type: 'email' })
      .sort({ createdAt: -1 })
      .limit(10)
      .select('name emailLeads.magnets metrics.leads');

    res.json({
      overview: {
        totalLeads,
        activeLeads,
        conversionRate: parseFloat(conversionRate),
        unsubscribeRate: totalLeads > 0 ? ((unsubscribedLeads / totalLeads) * 100).toFixed(2) : 0
      },
      trends: {
        daily: dailyTrends,
        sources: sourcesBreakdown
      },
      recentActivity: {
        leads: recentLeads,
        leadMagnets
      }
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({ error: error.message });
  }
};

// === EMAIL LEAD GENERATION ===

// Create lead magnet landing page
exports.createLeadMagnet = async (req, res) => {
  try {
    const { title, type, description, targetAudience, downloadUrl } = req.body;
    
    const campaign = new MarketingCampaign({
      name: `Lead Magnet: ${title}`,
      type: 'email',
      createdBy: req.user._id,
      emailLeads: {
        magnets: [{
          title,
          type,
          downloadUrl,
          description
        }]
      }
    });

    await campaign.save();
    res.json({ success: true, campaign });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Capture email lead
exports.captureEmailLead = async (req, res) => {
  try {
    const { email, source, profile, gdprConsent } = req.body;
    
    // Check if lead already exists
    let lead = await EmailLead.findOne({ email });
    
    if (lead) {
      // Update existing lead
      if (source) lead.source = { ...lead.source, ...source };
      if (profile) lead.profile = { ...lead.profile, ...profile };
      lead.updatedAt = new Date();
    } else {
      // Create new lead
      lead = new EmailLead({
        email,
        source,
        profile,
        gdprConsent: gdprConsent ? {
          given: true,
          date: new Date(),
          ip: req.ip
        } : undefined
      });
    }
    
    await lead.save();
    
    // Send welcome email if configured
    if (lead.status === 'pending') {
      await sendWelcomeEmail(lead);
      lead.status = 'verified';
      await lead.save();
    }
    
    res.json({ success: true, leadId: lead._id });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get comprehensive lead analytics
exports.getLeadAnalytics = async (req, res) => {
  try {
    const { period = '30d', exportFormat } = req.query;
    const days = parseInt(period.replace('d', ''));
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Basic analytics
    const analytics = await EmailLead.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: {
            source: '$source.type',
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } }
          },
          count: { $sum: 1 },
          avgEngagement: { $avg: '$engagement.score' }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Get all leads for detailed analysis
    const allLeads = await EmailLead.find({ createdAt: { $gte: startDate } })
      .select('email source profile engagement status createdAt')
      .sort({ createdAt: -1 });

    // Top sources analysis
    const sourcesAnalysis = await EmailLead.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: '$source.type',
          count: { $sum: 1 },
          verified: { $sum: { $cond: [{ $eq: ['$status', 'verified'] }, 1, 0] } },
          subscribed: { $sum: { $cond: [{ $eq: ['$status', 'subscribed'] }, 1, 0] } },
          avgEngagement: { $avg: '$engagement.score' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Occupation breakdown
    const occupationBreakdown = await EmailLead.aggregate([
      { $match: { createdAt: { $gte: startDate }, 'profile.occupation': { $exists: true } } },
      { $group: { _id: '$profile.occupation', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Email domain analysis
    const domainAnalysis = allLeads.reduce((acc, lead) => {
      const domain = lead.email.split('@')[1];
      if (!acc[domain]) {
        acc[domain] = {
          domain,
          count: 0,
          students: 0,
          employees: 0,
          others: 0
        };
      }
      acc[domain].count++;
      
      switch (lead.profile?.occupation) {
        case 'student':
          acc[domain].students++;
          break;
        case 'employee':
          acc[domain].employees++;
          break;
        default:
          acc[domain].others++;
      }
      return acc;
    }, {});

    const topDomains = Object.values(domainAnalysis)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Engagement trends
    const engagementTrends = await EmailLead.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          avgEngagement: { $avg: '$engagement.score' },
          highEngagement: { $sum: { $cond: [{ $gt: ['$engagement.score', 70] }, 1, 0] } },
          totalLeads: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    const response = {
      summary: {
        totalLeads: allLeads.length,
        period: `${days} days`,
        avgEngagement: allLeads.reduce((sum, lead) => sum + (lead.engagement?.score || 0), 0) / allLeads.length || 0
      },
      analytics,
      sources: sourcesAnalysis,
      occupations: occupationBreakdown,
      domains: topDomains,
      engagementTrends,
      leads: exportFormat === 'full' ? allLeads : allLeads.slice(0, 50)
    };

    // Handle export formats
    if (exportFormat === 'csv') {
      const csv = generateLeadsCSV(allLeads);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="leads-${period}.csv"`);
      return res.send(csv);
    }

    res.json(response);
  } catch (error) {
    console.error('Lead analytics error:', error);
    res.status(500).json({ error: error.message });
  }
};

// Helper function to generate CSV
function generateLeadsCSV(leads) {
  const headers = ['Email', 'Source', 'Status', 'Occupation', 'Engagement Score', 'Created Date', 'Domain'];
  const rows = leads.map(lead => [
    lead.email,
    lead.source?.type || '',
    lead.status,
    lead.profile?.occupation || '',
    lead.engagement?.score || 0,
    lead.createdAt.toISOString().split('T')[0],
    lead.email.split('@')[1]
  ]);
  
  return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// === FORUM MARKETING ===

// Analyze Reddit subreddit with real API data
exports.analyzeForumCompetitors = async (req, res) => {
  try {
    const { platform, subreddit, keywords, limit = 100 } = req.body;
    
    if (platform !== 'reddit') {
      return res.status(400).json({ error: 'Currently only Reddit analysis is supported' });
    }

    console.log(`Analyzing r/${subreddit} for keywords: ${keywords.join(', ')}`);
    
    // Fetch real Reddit data
    const analysis = await analyzeRedditSubreddit(subreddit, keywords, limit);
    
    // Save analysis to database
    const campaign = new MarketingCampaign({
      name: `Reddit Analysis: r/${subreddit}`,
      type: 'forum',
      createdBy: req.user._id,
      forumTargets: [{
        platform: 'reddit',
        subreddit: subreddit,
        targetAudience: `r/${subreddit} community`,
        analysis: analysis
      }]
    });
    
    await campaign.save();
    
    res.json({
      success: true,
      analysis,
      campaignId: campaign._id,
      subreddit,
      analysisDate: new Date()
    });
    
  } catch (error) {
    console.error('Reddit analysis error:', error);
    res.status(500).json({ 
      error: 'Failed to analyze Reddit data', 
      details: error.message,
      suggestion: 'Try a different subreddit or check if it exists'
    });
  }
};

// Real Reddit API analysis function
async function analyzeRedditSubreddit(subreddit, keywords = [], limit = 100) {
  try {
    // Fetch hot posts from Reddit's JSON API
    const url = `https://www.reddit.com/r/${subreddit}/hot.json?limit=${limit}`;
    const headers = {
      'User-Agent': 'AI-Pomo Marketing Tool 1.0 (Educational Purpose)'
    };
    
    console.log(`Fetching data from: ${url}`);
    const response = await axios.get(url, { headers });
    
    if (!response.data || !response.data.data) {
      throw new Error('Invalid Reddit response format');
    }
    
    const posts = response.data.data.children.map(child => child.data);
    console.log(`Fetched ${posts.length} posts from r/${subreddit}`);
    
    // Filter posts by keywords if provided
    let relevantPosts = posts;
    if (keywords.length > 0) {
      relevantPosts = posts.filter(post => {
        const content = `${post.title} ${post.selftext || ''}`.toLowerCase();
        return keywords.some(keyword => content.includes(keyword.toLowerCase()));
      });
    }
    
    console.log(`Found ${relevantPosts.length} relevant posts`);
    
    if (relevantPosts.length === 0) {
      return {
        subreddit,
        keywords,
        totalPosts: 0,
        message: 'No posts found matching your keywords. Try broader keywords or check a different subreddit.',
        suggestions: ['Try more general terms', 'Check if the subreddit is active', 'Use fewer keywords']
      };
    }
    
    // Process posts for analysis
    const processedPosts = relevantPosts.map(post => ({
      id: post.id,
      title: post.title,
      author: post.author,
      upvotes: post.ups,
      downvotes: post.downs || 0,
      comments: post.num_comments,
      url: `https://reddit.com${post.permalink}`,
      createdAt: new Date(post.created_utc * 1000),
      flair: post.link_flair_text,
      selftext: post.selftext,
      ratio: post.upvote_ratio || 0,
      awards: post.total_awards_received || 0,
      domain: post.domain,
      isVideo: post.is_video,
      thumbnail: post.thumbnail
    }));
    
    // Calculate metrics
    const totalUpvotes = processedPosts.reduce((sum, post) => sum + post.upvotes, 0);
    const totalComments = processedPosts.reduce((sum, post) => sum + post.comments, 0);
    const avgUpvotes = Math.round(totalUpvotes / processedPosts.length);
    const avgComments = Math.round(totalComments / processedPosts.length);
    
    // Analyze posting times
    const timeDistribution = {};
    processedPosts.forEach(post => {
      const hour = post.createdAt.getHours();
      timeDistribution[hour] = (timeDistribution[hour] || 0) + 1;
    });
    
    const bestTimes = Object.entries(timeDistribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour, count]) => ({
        hour: parseInt(hour),
        posts: count,
        timeRange: `${hour}:00-${parseInt(hour) + 1}:00`,
        percentage: ((count / processedPosts.length) * 100).toFixed(1)
      }));
    
    // Categorize content types
    const contentTypes = categorizeRedditPosts(processedPosts);
    
    // Find high performers (top 20%)
    const sortedByEngagement = processedPosts
      .sort((a, b) => (b.upvotes + b.comments) - (a.upvotes + a.comments));
    const topPerformers = sortedByEngagement.slice(0, Math.max(5, Math.ceil(processedPosts.length * 0.2)));
    
    // Generate content recommendations
    const recommendations = generateRedditRecommendations(topPerformers, processedPosts);
    
    // Find opportunities (low comment posts with decent upvotes)
    const opportunities = processedPosts
      .filter(post => post.upvotes > avgUpvotes && post.comments < 5)
      .sort((a, b) => b.upvotes - a.upvotes)
      .slice(0, 10)
      .map(post => ({
        title: post.title,
        url: post.url,
        upvotes: post.upvotes,
        comments: post.comments,
        opportunity: 'Low competition - high visibility potential'
      }));
    
    return {
      subreddit,
      keywords,
      totalPosts: processedPosts.length,
      metrics: {
        avgUpvotes,
        avgComments,
        totalEngagement: totalUpvotes + totalComments,
        avgRatio: (processedPosts.reduce((sum, post) => sum + post.ratio, 0) / processedPosts.length).toFixed(2)
      },
      bestPostingTimes: bestTimes,
      contentTypes,
      topPerformers: topPerformers.slice(0, 5),
      recommendations,
      opportunities,
      analyzedAt: new Date()
    };
    
  } catch (error) {
    console.error('Reddit API error:', error);
    if (error.response?.status === 404) {
      throw new Error(`Subreddit r/${subreddit} not found or is private`);
    } else if (error.response?.status === 429) {
      throw new Error('Reddit API rate limit reached. Please try again in a few minutes.');
    } else if (error.code === 'ENOTFOUND') {
      throw new Error('Network error. Please check your internet connection.');
    }
    throw new Error(`Reddit API error: ${error.message}`);
  }
}

// Helper function to categorize Reddit posts
function categorizeRedditPosts(posts) {
  const categories = {
    question: { posts: [], avgEngagement: 0 },
    discussion: { posts: [], avgEngagement: 0 },
    showcase: { posts: [], avgEngagement: 0 },
    tip: { posts: [], avgEngagement: 0 },
    news: { posts: [], avgEngagement: 0 },
    other: { posts: [], avgEngagement: 0 }
  };

  posts.forEach(post => {
    const title = post.title.toLowerCase();
    let category = 'other';
    
    if (title.includes('?') || title.startsWith('how') || title.startsWith('what') || title.startsWith('why')) {
      category = 'question';
    } else if (title.includes('tip') || title.includes('advice') || title.includes('guide')) {
      category = 'tip';
    } else if (title.includes('built') || title.includes('made') || title.includes('created') || title.includes('my')) {
      category = 'showcase';
    } else if (title.includes('discuss') || title.includes('thoughts') || title.includes('opinion')) {
      category = 'discussion';
    } else if (post.domain && !post.domain.includes('reddit.com')) {
      category = 'news';
    }

    const engagement = post.upvotes + post.comments;
    categories[category].posts.push(post);
    
    const currentAvg = categories[category].avgEngagement;
    const count = categories[category].posts.length;
    categories[category].avgEngagement = ((currentAvg * (count - 1)) + engagement) / count;
  });

  return Object.entries(categories)
    .filter(([, data]) => data.posts.length > 0)
    .sort(([,a], [,b]) => b.avgEngagement - a.avgEngagement)
    .reduce((acc, [type, data]) => {
      acc[type] = {
        count: data.posts.length,
        avgEngagement: Math.round(data.avgEngagement),
        percentage: ((data.posts.length / posts.length) * 100).toFixed(1)
      };
      return acc;
    }, {});
}

// Generate actionable recommendations
function generateRedditRecommendations(topPosts, allPosts) {
  const recommendations = [];
  
  // Title length analysis
  const avgTitleLength = topPosts.reduce((sum, post) => sum + post.title.length, 0) / topPosts.length;
  recommendations.push({
    type: 'title_length',
    suggestion: `Optimal title length: ${Math.round(avgTitleLength)} characters`,
    evidence: `Top performing posts average ${Math.round(avgTitleLength)} characters`,
    action: avgTitleLength > 100 ? 'Use longer, descriptive titles' : 'Keep titles concise and clear'
  });
  
  // Question format analysis
  const questionPosts = topPosts.filter(post => post.title.includes('?'));
  if (questionPosts.length > topPosts.length * 0.3) {
    recommendations.push({
      type: 'question_format',
      suggestion: 'Question-based titles perform well',
      evidence: `${questionPosts.length}/${topPosts.length} top posts are questions`,
      action: 'Consider framing your content as questions to encourage engagement'
    });
  }
  
  // Timing recommendation
  const avgUpvotes = allPosts.reduce((sum, post) => sum + post.upvotes, 0) / allPosts.length;
  recommendations.push({
    type: 'engagement_threshold',
    suggestion: `Target ${Math.round(avgUpvotes * 1.5)}+ upvotes for visibility`,
    evidence: `Community average is ${Math.round(avgUpvotes)} upvotes`,
    action: 'Focus on high-quality content that exceeds community standards'
  });
  
  return recommendations;
}

// Schedule forum posts
exports.scheduleForumPost = async (req, res) => {
  try {
    const { platform, content, scheduling, targetConfig } = req.body;
    
    const campaign = await MarketingCampaign.findOneAndUpdate(
      { type: 'forum', createdBy: req.user._id },
      {
        $push: {
          forumTargets: {
            platform,
            ...targetConfig,
            templates: [{
              title: content.title,
              content: content.body,
              type: content.type || 'discussion'
            }]
          }
        }
      },
      { new: true, upsert: true }
    );
    
    res.json({ success: true, campaignId: campaign._id });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === CONTENT DISTRIBUTION ===

// Auto-distribute blog post
exports.distributeContent = async (req, res) => {
  try {
    const { blogPostId, platforms, scheduling } = req.body;
    
    const blogPost = await BlogPost.findById(blogPostId);
    if (!blogPost) {
      return res.status(404).json({ error: 'Blog post not found' });
    }
    
    const distribution = new ContentDistribution({
      originalPost: blogPostId,
      distributions: platforms.map(platform => ({
        platform: platform.name,
        platformSpecific: platform.config,
        content: {
          title: adaptTitleForPlatform(blogPost.title, platform.name),
          body: adaptContentForPlatform(blogPost.content, platform.name),
          excerpt: blogPost.excerpt
        },
        scheduling: {
          scheduledFor: scheduling[platform.name] || new Date(),
          status: 'scheduled'
        }
      }))
    });
    
    await distribution.save();
    
    // Process scheduled distributions
    await processScheduledDistributions(distribution._id);
    
    res.json({ success: true, distributionId: distribution._id });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get content performance
exports.getContentPerformance = async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const days = parseInt(period.replace('d', ''));
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const performance = await ContentDistribution.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      { $unwind: '$distributions' },
      {
        $group: {
          _id: '$distributions.platform',
          totalViews: { $sum: '$distributions.performance.views' },
          totalEngagement: { $sum: '$distributions.performance.engagement' },
          totalClicks: { $sum: '$distributions.performance.clicks' },
          posts: { $sum: 1 }
        }
      },
      { $sort: { totalViews: -1 } }
    ]);
    
    res.json({ performance });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === BACKLINK OUTREACH ===

// Find backlink opportunities
exports.findBacklinkOpportunities = async (req, res) => {
  try {
    const { keywords, competitors, filters } = req.body;
    
    // Use APIs like Ahrefs, SEMrush, or Moz to find opportunities
    const opportunities = await findBacklinkTargets(keywords, competitors, filters);
    
    // Save opportunities to database
    const savedOpportunities = await Promise.all(
      opportunities.map(async (opp) => {
        const backlink = new BacklinkOutreach({
          targetWebsite: opp.website,
          contactInfo: opp.contact,
          outreachCampaign: {
            type: opp.type,
            status: 'identified'
          },
          research: opp.research
        });
        
        // Calculate priority score
        backlink.calculatePriority();
        
        return await backlink.save();
      })
    );
    
    res.json({ opportunities: savedOpportunities });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Send outreach email
exports.sendOutreachEmail = async (req, res) => {
  try {
    const { backlinkId, templateId, customMessage } = req.body;
    
    const backlink = await BacklinkOutreach.findById(backlinkId);
    if (!backlink) {
      return res.status(404).json({ error: 'Backlink opportunity not found' });
    }
    
    const emailTemplate = await getOutreachTemplate(templateId, backlink);
    const finalMessage = customMessage || emailTemplate.body;
    
    // Send email using your existing email system
    await sendOutreachEmail(backlink.contactInfo.email, emailTemplate.subject, finalMessage);
    
    // Record communication
    backlink.communications.push({
      type: 'initial_email',
      subject: emailTemplate.subject,
      message: finalMessage,
      sentAt: new Date(),
      templateUsed: templateId
    });
    
    backlink.outreachCampaign.status = 'contacted';
    backlink.automation.followUpScheduled = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    
    await backlink.save();
    
    res.json({ success: true, backlink });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get outreach pipeline
exports.getOutreachPipeline = async (req, res) => {
  try {
    const pipeline = await BacklinkOutreach.aggregate([
      {
        $group: {
          _id: '$outreachCampaign.status',
          count: { $sum: 1 },
          avgDA: { $avg: '$targetWebsite.metrics.domainAuthority' },
          opportunities: { $push: '$$ROOT' }
        }
      },
      { $sort: { count: -1 } }
    ]);
    
    const stats = {
      total: await BacklinkOutreach.countDocuments(),
      contacted: await BacklinkOutreach.countDocuments({ 'outreachCampaign.status': 'contacted' }),
      accepted: await BacklinkOutreach.countDocuments({ 'result.accepted': true }),
      published: await BacklinkOutreach.countDocuments({ 'outreachCampaign.status': 'published' })
    };
    
    res.json({ pipeline, stats });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Helper Functions

async function sendWelcomeEmail(lead) {
  // Implementation depends on your existing email system
  console.log(`Sending welcome email to ${lead.email}`);
}

async function analyzeRedditCompetitors(subreddit, keywords) {
  // Use Reddit API to analyze top posts
  // This is a simplified version - you'd use the actual Reddit API
  return [
    {
      username: 'competitor1',
      profileUrl: `https://reddit.com/u/competitor1`,
      successfulPosts: [
        {
          url: `https://reddit.com/r/${subreddit}/example`,
          title: 'Example successful post',
          upvotes: 150,
          analysis: 'High engagement due to practical tips'
        }
      ]
    }
  ];
}

async function analyzeQuoraCompetitors(keywords) {
  // Similar implementation for Quora
  return [];
}

function adaptTitleForPlatform(title, platform) {
  switch (platform) {
    case 'reddit':
      return title; // Reddit users prefer straightforward titles
    case 'twitter':
      return title.length > 100 ? title.substring(0, 97) + '...' : title;
    case 'linkedin':
      return `Professional Insight: ${title}`;
    default:
      return title;
  }
}

function adaptContentForPlatform(content, platform) {
  switch (platform) {
    case 'twitter':
      return content.substring(0, 250) + '... Read more: [link]';
    case 'reddit':
      return content + '\n\nThoughts? Would love to hear your experiences!';
    case 'linkedin':
      return content + '\n\n#productivity #timemanagement #pomodoro';
    default:
      return content;
  }
}

async function processScheduledDistributions(distributionId) {
  // This would be handled by a background job
  console.log(`Processing scheduled distributions for ${distributionId}`);
}

async function findBacklinkTargets(keywords, competitors, filters) {
  // Integration with backlink research APIs
  return [];
}

async function getOutreachTemplate(templateId, backlink) {
  const templates = {
    'guest_post': {
      subject: `Guest Post Proposal for ${backlink.targetWebsite.domain}`,
      body: `Hi ${backlink.contactInfo.name || 'there'},\n\nI came across ${backlink.targetWebsite.url} and love your content on productivity tools...\n\nWould you be interested in a guest post about AI-powered productivity techniques?\n\nBest regards,\n[Your name]`
    },
    'resource_page': {
      subject: `Resource suggestion for ${backlink.targetWebsite.domain}`,
      body: `Hi ${backlink.contactInfo.name || 'there'},\n\nI noticed your excellent resource page on productivity tools...\n\nI thought you might be interested in our AI Pomo tool...\n\nBest regards,\n[Your name]`
    }
  };
  
  return templates[templateId] || templates['guest_post'];
}

async function sendOutreachEmail(email, subject, message) {
  // Use your existing email system
  console.log(`Sending outreach email to ${email}: ${subject}`);
}

module.exports = exports;