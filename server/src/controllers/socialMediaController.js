const { SocialMediaAccount, SocialContentTemplate, SocialMediaCampaign, SocialMediaPost } = require('../models/SocialMedia');
const axios = require('axios');
const cron = require('node-cron');

// Twitter API v2 客户端
class TwitterClient {
  constructor(credentials) {
    this.credentials = credentials;
    this.baseURL = 'https://api.twitter.com/2';
  }

  async postTweet(text, mediaIds = []) {
    try {
      const payload = { text };
      
      if (mediaIds.length > 0) {
        payload.media = { media_ids: mediaIds };
      }

      const response = await axios.post(`${this.baseURL}/tweets`, payload, {
        headers: {
          'Authorization': `Bearer ${this.credentials.bearerToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        postId: response.data.data.id,
        text: response.data.data.text,
        url: `https://twitter.com/user/status/${response.data.data.id}`
      };
    } catch (error) {
      console.error('Twitter API error:', error.response?.data);
      throw new Error(error.response?.data?.detail || 'Twitter posting failed');
    }
  }

  async uploadMedia(mediaUrl) {
    // 媒体上传实现
    try {
      // 这里需要实现媒体文件的上传逻辑
      // 返回 media_id
      return 'media_id_placeholder';
    } catch (error) {
      console.error('Twitter media upload failed:', error);
      throw error;
    }
  }

  async getTweetMetrics(tweetId) {
    try {
      const response = await axios.get(`${this.baseURL}/tweets/${tweetId}`, {
        params: {
          'tweet.fields': 'public_metrics,created_at'
        },
        headers: {
          'Authorization': `Bearer ${this.credentials.bearerToken}`
        }
      });

      const metrics = response.data.data.public_metrics;
      return {
        likes: metrics.like_count,
        shares: metrics.retweet_count,
        comments: metrics.reply_count,
        impressions: metrics.impression_count
      };
    } catch (error) {
      console.error('Twitter metrics fetch failed:', error);
      return null;
    }
  }
}

// LinkedIn API 客户端
class LinkedInClient {
  constructor(credentials) {
    this.credentials = credentials;
    this.baseURL = 'https://api.linkedin.com/v2';
  }

  async createPost(text, mediaUrls = []) {
    try {
      const payload = {
        author: `urn:li:person:${this.credentials.userId}`,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: text
            },
            shareMediaCategory: mediaUrls.length > 0 ? 'IMAGE' : 'NONE'
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
        }
      };

      if (mediaUrls.length > 0) {
        payload.specificContent['com.linkedin.ugc.ShareContent'].media = mediaUrls.map(url => ({
          status: 'READY',
          media: url
        }));
      }

      const response = await axios.post(`${this.baseURL}/ugcPosts`, payload, {
        headers: {
          'Authorization': `Bearer ${this.credentials.accessToken}`,
          'Content-Type': 'application/json',
          'X-Restli-Protocol-Version': '2.0.0'
        }
      });

      return {
        success: true,
        postId: response.data.id,
        url: `https://www.linkedin.com/feed/update/${response.data.id}`
      };
    } catch (error) {
      console.error('LinkedIn API error:', error.response?.data);
      throw new Error('LinkedIn posting failed');
    }
  }
}

// === 社交媒体账户管理 ===

// 添加社交媒体账户
exports.createSocialMediaAccount = async (req, res) => {
  try {
    const { platform, accountName, displayName, credentials, profile, limits } = req.body;
    
    // 验证账户凭据
    const validation = await validateAccountCredentials(platform, credentials);
    if (!validation.valid) {
      return res.status(400).json({ error: validation.error });
    }
    
    const account = new SocialMediaAccount({
      platform,
      accountName,
      displayName,
      credentials: encryptCredentials(credentials),
      profile: { ...profile, ...validation.profile },
      limits: limits || {}
    });
    
    await account.save();
    
    // 返回安全的账户信息
    const safeAccount = account.toObject();
    delete safeAccount.credentials;
    
    res.json({ success: true, account: safeAccount });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 验证社交媒体账户凭据
async function validateAccountCredentials(platform, credentials) {
  try {
    switch (platform) {
      case 'twitter':
        const twitter = new TwitterClient(credentials);
        // 简单验证 - 实际项目中需要调用验证端点
        if (credentials.bearerToken) {
          return { 
            valid: true, 
            profile: { verified: false } 
          };
        }
        return { valid: false, error: 'Invalid Twitter credentials' };
      
      case 'linkedin':
        const linkedin = new LinkedInClient(credentials);
        if (credentials.accessToken && credentials.userId) {
          return { 
            valid: true, 
            profile: { businessAccount: true } 
          };
        }
        return { valid: false, error: 'Invalid LinkedIn credentials' };
      
      default:
        return { valid: true, profile: {} };
    }
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

// 简单凭据加密
function encryptCredentials(credentials) {
  // 实际项目中应使用强加密
  return credentials;
}

// 获取社交媒体账户
exports.getSocialMediaAccounts = async (req, res) => {
  try {
    const { platform, status } = req.query;
    const filter = {};
    
    if (platform) filter.platform = platform;
    if (status) filter.status = status;
    
    const accounts = await SocialMediaAccount.find(filter)
      .select('-credentials')
      .sort({ createdAt: -1 });
    
    res.json({ accounts });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 更新账户信息
exports.updateSocialMediaAccount = async (req, res) => {
  try {
    const account = await SocialMediaAccount.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    ).select('-credentials');
    
    if (!account) {
      return res.status(404).json({ error: '账户未找到' });
    }
    
    res.json({ success: true, account });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === 内容模板管理 ===

// 创建内容模板
exports.createContentTemplate = async (req, res) => {
  try {
    const { name, platform, type, content, variables, settings } = req.body;
    
    const template = new SocialContentTemplate({
      name,
      platform,
      type,
      content,
      variables: variables || [],
      settings: settings || {}
    });
    
    await template.save();
    res.json({ success: true, template });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 获取内容模板
exports.getContentTemplates = async (req, res) => {
  try {
    const { platform, type, active } = req.query;
    const filter = {};
    
    if (platform) filter.platform = platform;
    if (type) filter.type = type;
    if (active !== undefined) filter.active = active === 'true';
    
    const templates = await SocialContentTemplate.find(filter)
      .sort({ createdAt: -1 });
    
    res.json({ templates });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 更新内容模板
exports.updateContentTemplate = async (req, res) => {
  try {
    const template = await SocialContentTemplate.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );
    
    if (!template) {
      return res.status(404).json({ error: '模板未找到' });
    }
    
    res.json({ success: true, template });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === 社交媒体活动管理 ===

// 创建社交媒体活动
exports.createSocialMediaCampaign = async (req, res) => {
  try {
    const {
      name,
      description,
      type,
      contentConfig,
      accountConfig,
      scheduling,
      personalization,
      automation,
      moderation
    } = req.body;
    
    const campaign = new SocialMediaCampaign({
      name,
      description,
      type,
      contentConfig,
      accountConfig,
      scheduling,
      personalization: personalization || {},
      automation: automation || {},
      moderation: moderation || {},
      createdBy: req.user._id
    });
    
    // 计算下次发布时间
    if (scheduling.frequency !== 'once') {
      campaign.scheduling.nextPostTime = calculateNextPostTime(scheduling);
    }
    
    await campaign.save();
    res.json({ success: true, campaign });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 计算下次发布时间
function calculateNextPostTime(scheduling) {
  const now = new Date();
  
  switch (scheduling.frequency) {
    case 'daily':
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      if (scheduling.customFrequency && scheduling.customFrequency.times.length > 0) {
        const time = scheduling.customFrequency.times[0];
        const [hour, minute] = time.split(':').map(Number);
        tomorrow.setHours(hour, minute, 0, 0);
      }
      return tomorrow;
    
    case 'twice_daily':
      const nextTime = new Date(now);
      nextTime.setHours(nextTime.getHours() + 12);
      return nextTime;
    
    case 'weekly':
      const nextWeek = new Date(now);
      nextWeek.setDate(nextWeek.getDate() + 7);
      return nextWeek;
    
    case 'custom':
      if (scheduling.customFrequency && scheduling.customFrequency.interval) {
        const nextCustom = new Date(now);
        nextCustom.setMinutes(nextCustom.getMinutes() + scheduling.customFrequency.interval);
        return nextCustom;
      }
      break;
  }
  
  return new Date(now.getTime() + 24 * 60 * 60 * 1000); // 默认24小时后
}

// 获取社交媒体活动
exports.getSocialMediaCampaigns = async (req, res) => {
  try {
    const { status, type, platform } = req.query;
    const filter = {};
    
    if (status) filter.status = status;
    if (type) filter.type = type;
    
    const campaigns = await SocialMediaCampaign.find(filter)
      .populate('contentConfig.templates', 'name type platform')
      .populate('accountConfig.accounts', 'platform displayName status')
      .sort({ createdAt: -1 });
    
    res.json({ campaigns });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 启动社交媒体活动
exports.startSocialMediaCampaign = async (req, res) => {
  try {
    const campaign = await SocialMediaCampaign.findById(req.params.id);
    if (!campaign) {
      return res.status(404).json({ error: '活动未找到' });
    }
    
    // 验证活动配置
    const validation = validateCampaignConfig(campaign);
    if (!validation.valid) {
      return res.status(400).json({ error: validation.error });
    }
    
    campaign.status = 'active';
    if (!campaign.scheduling.nextPostTime) {
      campaign.scheduling.nextPostTime = calculateNextPostTime(campaign.scheduling);
    }
    
    await campaign.save();
    
    // 立即创建第一个待发布的帖子
    await scheduleNextSocialPost(campaign);
    
    res.json({ success: true, message: '社交媒体活动已启动' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 验证活动配置
function validateCampaignConfig(campaign) {
  if (!campaign.contentConfig.templates || campaign.contentConfig.templates.length === 0) {
    if (!campaign.contentConfig.customContent || campaign.contentConfig.customContent.length === 0) {
      return { valid: false, error: '未设置内容模板或自定义内容' };
    }
  }
  
  if (!campaign.accountConfig.accounts || campaign.accountConfig.accounts.length === 0) {
    return { valid: false, error: '未设置发布账户' };
  }
  
  return { valid: true };
}

// 暂停社交媒体活动
exports.pauseSocialMediaCampaign = async (req, res) => {
  try {
    const campaign = await SocialMediaCampaign.findByIdAndUpdate(
      req.params.id,
      { status: 'paused' },
      { new: true }
    );
    
    if (!campaign) {
      return res.status(404).json({ error: '活动未找到' });
    }
    
    res.json({ success: true, message: '社交媒体活动已暂停' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// === 自动发布执行 ===

// 调度下一个社交媒体帖子
async function scheduleNextSocialPost(campaign) {
  try {
    // 选择内容
    const content = await selectContent(campaign);
    if (!content) {
      console.error(`Campaign ${campaign.name}: No content available`);
      return;
    }
    
    // 选择账户
    const accounts = await selectAccounts(campaign);
    if (!accounts || accounts.length === 0) {
      console.error(`Campaign ${campaign.name}: No accounts available`);
      return;
    }
    
    // 为每个选中的账户创建帖子
    for (const account of accounts) {
      const processedContent = await processContent(content, account, campaign);
      
      const post = new SocialMediaPost({
        campaignId: campaign._id,
        templateId: content.templateId,
        accountId: account._id,
        platform: account.platform,
        content: processedContent,
        publishing: {
          status: 'queued',
          scheduledFor: campaign.scheduling.nextPostTime
        }
      });
      
      await post.save();
      
      // 如果是跨平台发布，添加延迟
      if (campaign.accountConfig.crossPost && accounts.length > 1) {
        campaign.scheduling.nextPostTime = new Date(
          campaign.scheduling.nextPostTime.getTime() + campaign.accountConfig.postDelay
        );
      }
    }
    
    // 更新下次发布时间
    campaign.scheduling.nextPostTime = calculateNextPostTime(campaign.scheduling);
    await campaign.save();
    
    console.log(`Scheduled ${accounts.length} posts for campaign ${campaign.name}`);
  } catch (error) {
    console.error('Error scheduling social media post:', error);
  }
}

// 选择内容
async function selectContent(campaign) {
  // 优先使用自定义内容
  if (campaign.contentConfig.customContent && campaign.contentConfig.customContent.length > 0) {
    const unused = campaign.contentConfig.customContent.filter(content => !content.used);
    if (unused.length > 0) {
      return unused[0];
    }
  }
  
  // 使用模板
  if (campaign.contentConfig.templates && campaign.contentConfig.templates.length > 0) {
    const templates = await SocialContentTemplate.find({
      _id: { $in: campaign.contentConfig.templates },
      active: true
    });
    
    if (templates.length === 0) return null;
    
    if (campaign.contentConfig.rotateTemplates) {
      return templates.sort((a, b) => a.performance.used - b.performance.used)[0];
    } else {
      return templates[Math.floor(Math.random() * templates.length)];
    }
  }
  
  return null;
}

// 选择账户
async function selectAccounts(campaign) {
  const accounts = await SocialMediaAccount.find({
    _id: { $in: campaign.accountConfig.accounts },
    status: 'active'
  });
  
  const availableAccounts = accounts.filter(account => account.canPost().canPost);
  
  if (availableAccounts.length === 0) return [];
  
  if (campaign.accountConfig.crossPost) {
    return availableAccounts; // 返回所有可用账户
  } else {
    // 选择一个账户
    if (campaign.accountConfig.rotateAccounts) {
      return [availableAccounts.sort((a, b) => a.usage.postsToday - b.usage.postsToday)[0]];
    } else {
      return [availableAccounts[Math.floor(Math.random() * availableAccounts.length)]];
    }
  }
}

// 处理内容（个性化、变量替换等）
async function processContent(content, account, campaign) {
  let processedText = content.text || content.content?.text || '';
  let hashtags = content.hashtags || content.content?.hashtags || [];
  let mentions = content.mentions || content.content?.mentions || [];
  
  // 变量替换
  if (content.variables) {
    content.variables.forEach(variable => {
      const placeholder = variable.placeholder;
      const value = variable.defaultValue || '';
      processedText = processedText.replace(new RegExp(placeholder, 'g'), value);
    });
  }
  
  // 个性化处理
  if (campaign.personalization.enabled) {
    // 动态标签
    if (campaign.personalization.dynamicHashtags) {
      hashtags = await addTrendingHashtags(hashtags, account.platform);
    }
    
    // 时间相关内容
    processedText = addTimeBasedContent(processedText);
  }
  
  // 平台特定处理
  processedText = adaptContentForPlatform(processedText, account.platform, account.limits);
  
  return {
    text: processedText,
    hashtags,
    mentions,
    media: content.media || [],
    links: content.links || []
  };
}

// 添加热门标签
async function addTrendingHashtags(existingHashtags, platform) {
  // 这里可以集成热门标签API
  const trendingHashtags = {
    twitter: ['#productivity', '#timemanagement', '#focus'],
    linkedin: ['#productivity', '#worklife', '#efficiency'],
    instagram: ['#productivity', '#motivation', '#goals']
  };
  
  const trending = trendingHashtags[platform] || [];
  const combined = [...existingHashtags, ...trending];
  
  // 去重并限制数量
  return [...new Set(combined)].slice(0, 10);
}

// 添加时间相关内容
function addTimeBasedContent(text) {
  const now = new Date();
  const hour = now.getHours();
  
  let timeGreeting = '';
  if (hour < 12) {
    timeGreeting = '早上好！';
  } else if (hour < 18) {
    timeGreeting = '下午好！';
  } else {
    timeGreeting = '晚上好！';
  }
  
  return text.replace('{{timeGreeting}}', timeGreeting);
}

// 平台适配
function adaptContentForPlatform(text, platform, limits) {
  const maxLength = limits.maxCharacters || 280;
  
  switch (platform) {
    case 'twitter':
      if (text.length > 280) {
        text = text.substring(0, 277) + '...';
      }
      break;
    
    case 'linkedin':
      // LinkedIn 允许更长的内容
      if (text.length > 3000) {
        text = text.substring(0, 2997) + '...';
      }
      break;
    
    case 'instagram':
      // Instagram 内容可以更加视觉化
      break;
  }
  
  return text;
}

// 执行实际发布
async function executePost(post) {
  try {
    post.publishing.status = 'publishing';
    await post.save();
    
    const account = await SocialMediaAccount.findById(post.accountId);
    if (!account || !account.canPost().canPost) {
      throw new Error('Account not available for posting');
    }
    
    let result;
    
    switch (post.platform) {
      case 'twitter':
        result = await postToTwitter(post, account);
        break;
      case 'linkedin':
        result = await postToLinkedIn(post, account);
        break;
      default:
        throw new Error(`Platform ${post.platform} not supported`);
    }
    
    // 更新发布状态
    post.publishing.status = 'published';
    post.publishing.publishedAt = new Date();
    post.publishing.platformPostId = result.postId;
    post.publishing.platformUrl = result.url;
    
    // 更新账户使用统计
    account.usage.postsToday++;
    account.usage.postsThisHour++;
    account.usage.lastPostTime = new Date();
    
    await Promise.all([post.save(), account.save()]);
    
    // 更新活动统计
    await SocialMediaCampaign.findByIdAndUpdate(post.campaignId, {
      $inc: { 'stats.totalPosts': 1, 'stats.successfulPosts': 1 }
    });
    
    console.log(`Successfully posted to ${post.platform}: ${result.url}`);
    
  } catch (error) {
    console.error('Social media post execution failed:', error);
    
    post.publishing.status = 'failed';
    post.publishing.errorMessage = error.message;
    post.publishing.retryCount++;
    
    await post.save();
    
    await SocialMediaCampaign.findByIdAndUpdate(post.campaignId, {
      $inc: { 'stats.totalPosts': 1, 'stats.failedPosts': 1 }
    });
  }
}

// 发布到 Twitter
async function postToTwitter(post, account) {
  const twitter = new TwitterClient(account.credentials);
  
  // 处理媒体上传
  const mediaIds = [];
  for (const media of post.content.media || []) {
    if (media.url) {
      const mediaId = await twitter.uploadMedia(media.url);
      mediaIds.push(mediaId);
    }
  }
  
  // 组合文本和标签
  let fullText = post.content.text;
  if (post.content.hashtags && post.content.hashtags.length > 0) {
    fullText += ' ' + post.content.hashtags.join(' ');
  }
  
  return await twitter.postTweet(fullText, mediaIds);
}

// 发布到 LinkedIn
async function postToLinkedIn(post, account) {
  const linkedin = new LinkedInClient(account.credentials);
  
  const mediaUrls = post.content.media?.map(media => media.url) || [];
  
  return await linkedin.createPost(post.content.text, mediaUrls);
}

// 获取发布记录
exports.getSocialMediaPosts = async (req, res) => {
  try {
    const { campaignId, platform, status, page = 1, limit = 20 } = req.query;
    const filter = {};
    
    if (campaignId) filter.campaignId = campaignId;
    if (platform) filter.platform = platform;
    if (status) filter['publishing.status'] = status;
    
    const posts = await SocialMediaPost.find(filter)
      .populate('campaignId', 'name')
      .populate('templateId', 'name type')
      .populate('accountId', 'platform displayName')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await SocialMediaPost.countDocuments(filter);
    
    res.json({
      posts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 手动触发发布
exports.triggerSocialPost = async (req, res) => {
  try {
    const { campaignId } = req.body;
    
    const campaign = await SocialMediaCampaign.findById(campaignId);
    if (!campaign) {
      return res.status(404).json({ error: '活动未找到' });
    }
    
    if (campaign.status !== 'active') {
      return res.status(400).json({ error: '活动未激活' });
    }
    
    await scheduleNextSocialPost(campaign);
    
    res.json({ success: true, message: '已创建新的发布任务' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 获取活动统计
exports.getSocialMediaStats = async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const days = parseInt(period.replace('d', ''));
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const campaignStats = await SocialMediaCampaign.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: null,
          totalCampaigns: { $sum: 1 },
          activeCampaigns: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
          totalPosts: { $sum: '$stats.totalPosts' },
          totalLikes: { $sum: '$stats.totalLikes' },
          totalShares: { $sum: '$stats.totalShares' },
          totalComments: { $sum: '$stats.totalComments' },
          totalReach: { $sum: '$stats.totalReach' }
        }
      }
    ]);
    
    const platformStats = await SocialMediaPost.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: '$platform',
          posts: { $sum: 1 },
          published: { $sum: { $cond: [{ $eq: ['$publishing.status', 'published'] }, 1, 0] } },
          avgLikes: { $avg: '$metrics.likes' },
          avgShares: { $avg: '$metrics.shares' },
          avgComments: { $avg: '$metrics.comments' }
        }
      }
    ]);
    
    const result = campaignStats[0] || {
      totalCampaigns: 0,
      activeCampaigns: 0,
      totalPosts: 0,
      totalLikes: 0,
      totalShares: 0,
      totalComments: 0,
      totalReach: 0
    };
    
    // 计算总体参与率
    if (result.totalReach > 0) {
      const totalEngagement = result.totalLikes + result.totalShares + result.totalComments;
      result.avgEngagementRate = ((totalEngagement / result.totalReach) * 100).toFixed(2);
    } else {
      result.avgEngagementRate = 0;
    }
    
    res.json({
      overview: result,
      platformBreakdown: platformStats,
      period: `${days} days`
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// 定时任务：执行待发布的帖子
cron.schedule('*/5 * * * *', async () => {
  try {
    const now = new Date();
    const postsToExecute = await SocialMediaPost.find({
      'publishing.status': 'queued',
      'publishing.scheduledFor': { $lte: now }
    }).limit(10);
    
    for (const post of postsToExecute) {
      await executePost(post);
      // 帖子间添加延迟
      await new Promise(resolve => setTimeout(resolve, 30000));
    }
  } catch (error) {
    console.error('Social media cron job error:', error);
  }
});

// 定时任务：为活跃活动调度新帖子
cron.schedule('0 */4 * * *', async () => {
  try {
    const now = new Date();
    const campaignsToSchedule = await SocialMediaCampaign.find({
      status: 'active',
      'scheduling.nextPostTime': { $lte: now }
    });
    
    for (const campaign of campaignsToSchedule) {
      await scheduleNextSocialPost(campaign);
    }
  } catch (error) {
    console.error('Social media campaign scheduling error:', error);
  }
});

module.exports = exports;