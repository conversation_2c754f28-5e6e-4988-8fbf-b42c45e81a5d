const { ForumAccount, PostTemplate, ForumPostCampaign, ForumPost, ForumCommunity } = require('../models/ForumPost');
const axios = require('axios');
const cron = require('node-cron');

// Reddit API 客户端
class RedditClient {\n  constructor(credentials) {\n    this.credentials = credentials;\n    this.accessToken = null;\n    this.userAgent = 'AI-Pomo Marketing Bot 1.0';\n  }\n\n  async authenticate() {\n    try {\n      const auth = Buffer.from(`${this.credentials.clientId}:${this.credentials.clientSecret}`).toString('base64');\n      \n      const response = await axios.post('https://www.reddit.com/api/v1/access_token', \n        'grant_type=password&username=' + encodeURIComponent(this.credentials.username) + \n        '&password=' + encodeURIComponent(this.credentials.password),\n        {\n          headers: {\n            'Authorization': `Basic ${auth}`,\n            'User-Agent': this.userAgent,\n            'Content-Type': 'application/x-www-form-urlencoded'\n          }\n        }\n      );\n      \n      this.accessToken = response.data.access_token;\n      return true;\n    } catch (error) {\n      console.error('Reddit authentication failed:', error.response?.data);\n      return false;\n    }\n  }\n\n  async submitPost(subreddit, title, text, url = null) {\n    if (!this.accessToken) {\n      await this.authenticate();\n    }\n\n    try {\n      const postData = {\n        sr: subreddit,\n        kind: url ? 'link' : 'self',\n        title: title,\n        api_type: 'json'\n      };\n\n      if (url) {\n        postData.url = url;\n      } else {\n        postData.text = text;\n      }\n\n      const response = await axios.post('https://oauth.reddit.com/api/submit', postData, {\n        headers: {\n          'Authorization': `Bearer ${this.accessToken}`,\n          'User-Agent': this.userAgent,\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        transformRequest: [function (data) {\n          return Object.keys(data)\n            .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(data[key]))\n            .join('&');\n        }]\n      });\n\n      if (response.data.json.errors.length > 0) {\n        throw new Error('Reddit API error: ' + JSON.stringify(response.data.json.errors));\n      }\n\n      return {\n        success: true,\n        postId: response.data.json.data.id,\n        url: response.data.json.data.url\n      };\n    } catch (error) {\n      console.error('Reddit post submission failed:', error.response?.data);\n      throw error;\n    }\n  }\n\n  async getPostStats(postId) {\n    try {\n      const response = await axios.get(`https://oauth.reddit.com/comments/${postId}`, {\n        headers: {\n          'Authorization': `Bearer ${this.accessToken}`,\n          'User-Agent': this.userAgent\n        }\n      });\n\n      const post = response.data[0].data.children[0].data;\n      return {\n        upvotes: post.ups,\n        downvotes: post.downs,\n        score: post.score,\n        comments: post.num_comments,\n        upvoteRatio: post.upvote_ratio\n      };\n    } catch (error) {\n      console.error('Failed to get Reddit post stats:', error);\n      return null;\n    }\n  }\n}\n\n// === 论坛账户管理 ===\n\n// 创建论坛账户\nexports.createForumAccount = async (req, res) => {\n  try {\n    const { platform, accountName, credentials, profile, dailyLimits } = req.body;\n    \n    // 验证账户凭据\n    const isValid = await validateAccountCredentials(platform, credentials);\n    if (!isValid) {\n      return res.status(400).json({ error: '账户凭据验证失败' });\n    }\n    \n    const account = new ForumAccount({\n      platform,\n      accountName,\n      credentials: encryptCredentials(credentials), // 实际项目中需要加密\n      profile: profile || {},\n      dailyLimits: dailyLimits || {}\n    });\n    \n    await account.save();\n    \n    // 返回时不包含敏感信息\n    const safeAccount = account.toObject();\n    delete safeAccount.credentials;\n    \n    res.json({ success: true, account: safeAccount });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 验证账户凭据\nasync function validateAccountCredentials(platform, credentials) {\n  try {\n    switch (platform) {\n      case 'reddit':\n        const reddit = new RedditClient(credentials);\n        return await reddit.authenticate();\n      \n      case 'twitter':\n        // Twitter API validation logic\n        return true; // placeholder\n      \n      default:\n        return true; // placeholder for other platforms\n    }\n  } catch (error) {\n    console.error(`Validation failed for ${platform}:`, error);\n    return false;\n  }\n}\n\n// 简单的凭据加密（实际项目中应使用更强的加密）\nfunction encryptCredentials(credentials) {\n  // 这里应该使用真正的加密\n  return credentials;\n}\n\n// 获取论坛账户列表\nexports.getForumAccounts = async (req, res) => {\n  try {\n    const { platform, status } = req.query;\n    const filter = {};\n    \n    if (platform) filter.platform = platform;\n    if (status) filter.status = status;\n    \n    const accounts = await ForumAccount.find(filter)\n      .select('-credentials') // 不返回敏感信息\n      .sort({ createdAt: -1 });\n    \n    res.json({ accounts });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 更新账户状态\nexports.updateForumAccount = async (req, res) => {\n  try {\n    const account = await ForumAccount.findByIdAndUpdate(\n      req.params.id,\n      req.body,\n      { new: true }\n    ).select('-credentials');\n    \n    if (!account) {\n      return res.status(404).json({ error: '账户未找到' });\n    }\n    \n    res.json({ success: true, account });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// === 发帖模板管理 ===\n\n// 创建发帖模板\nexports.createPostTemplate = async (req, res) => {\n  try {\n    const { name, platform, type, content, targeting, variables } = req.body;\n    \n    const template = new PostTemplate({\n      name,\n      platform,\n      type,\n      content,\n      targeting: targeting || {},\n      variables: variables || []\n    });\n    \n    await template.save();\n    res.json({ success: true, template });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 获取发帖模板\nexports.getPostTemplates = async (req, res) => {\n  try {\n    const { platform, type, active } = req.query;\n    const filter = {};\n    \n    if (platform) filter.platform = platform;\n    if (type) filter.type = type;\n    if (active !== undefined) filter.active = active === 'true';\n    \n    const templates = await PostTemplate.find(filter)\n      .sort({ createdAt: -1 });\n    \n    res.json({ templates });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 更新模板\nexports.updatePostTemplate = async (req, res) => {\n  try {\n    const template = await PostTemplate.findByIdAndUpdate(\n      req.params.id,\n      req.body,\n      { new: true }\n    );\n    \n    if (!template) {\n      return res.status(404).json({ error: '模板未找到' });\n    }\n    \n    res.json({ success: true, template });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// === 发帖活动管理 ===\n\n// 创建发帖活动\nexports.createPostCampaign = async (req, res) => {\n  try {\n    const {\n      name,\n      description,\n      postConfig,\n      targeting,\n      scheduling,\n      contentVariation,\n      automation\n    } = req.body;\n    \n    const campaign = new ForumPostCampaign({\n      name,\n      description,\n      postConfig,\n      targeting,\n      scheduling,\n      contentVariation: contentVariation || {},\n      automation: automation || {},\n      createdBy: req.user._id\n    });\n    \n    // 计算下次发帖时间\n    if (scheduling.frequency !== 'manual') {\n      campaign.scheduling.nextPostAt = calculateNextPostTime(scheduling);\n    }\n    \n    await campaign.save();\n    res.json({ success: true, campaign });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 计算下次发帖时间\nfunction calculateNextPostTime(scheduling) {\n  const now = new Date();\n  const startDate = new Date(scheduling.startDate || now);\n  \n  if (startDate > now) {\n    return startDate;\n  }\n  \n  switch (scheduling.frequency) {\n    case 'daily':\n      return new Date(now.getTime() + 24 * 60 * 60 * 1000);\n    case 'every_other_day':\n      return new Date(now.getTime() + 48 * 60 * 60 * 1000);\n    case 'weekly':\n      return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);\n    default:\n      return now;\n  }\n}\n\n// 获取发帖活动列表\nexports.getPostCampaigns = async (req, res) => {\n  try {\n    const { status, platform } = req.query;\n    const filter = {};\n    \n    if (status) filter.status = status;\n    if (platform) filter['targeting.platforms'] = platform;\n    \n    const campaigns = await ForumPostCampaign.find(filter)\n      .populate('postConfig.templates', 'name type platform')\n      .populate('postConfig.accounts', 'platform accountName status')\n      .sort({ createdAt: -1 });\n    \n    res.json({ campaigns });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 启动发帖活动\nexports.startPostCampaign = async (req, res) => {\n  try {\n    const campaign = await ForumPostCampaign.findById(req.params.id);\n    if (!campaign) {\n      return res.status(404).json({ error: '活动未找到' });\n    }\n    \n    // 验证活动配置\n    const validation = validateCampaignConfig(campaign);\n    if (!validation.valid) {\n      return res.status(400).json({ error: validation.error });\n    }\n    \n    campaign.status = 'active';\n    if (!campaign.scheduling.nextPostAt) {\n      campaign.scheduling.nextPostAt = calculateNextPostTime(campaign.scheduling);\n    }\n    \n    await campaign.save();\n    \n    // 立即创建第一个待发布的帖子\n    await scheduleNextPost(campaign);\n    \n    res.json({ success: true, message: '发帖活动已启动' });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 验证活动配置\nfunction validateCampaignConfig(campaign) {\n  if (!campaign.postConfig.templates || campaign.postConfig.templates.length === 0) {\n    return { valid: false, error: '未设置发帖模板' };\n  }\n  \n  if (!campaign.postConfig.accounts || campaign.postConfig.accounts.length === 0) {\n    return { valid: false, error: '未设置发帖账户' };\n  }\n  \n  if (!campaign.targeting.communities || campaign.targeting.communities.length === 0) {\n    return { valid: false, error: '未设置目标社区' };\n  }\n  \n  return { valid: true };\n}\n\n// 暂停发帖活动\nexports.pausePostCampaign = async (req, res) => {\n  try {\n    const campaign = await ForumPostCampaign.findByIdAndUpdate(\n      req.params.id,\n      { status: 'paused' },\n      { new: true }\n    );\n    \n    if (!campaign) {\n      return res.status(404).json({ error: '活动未找到' });\n    }\n    \n    res.json({ success: true, message: '发帖活动已暂停' });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// === 自动发帖执行 ===\n\n// 调度下一个帖子\nasync function scheduleNextPost(campaign) {\n  try {\n    // 选择模板\n    const template = await selectTemplate(campaign);\n    if (!template) {\n      console.error(`Campaign ${campaign.name}: No template available`);\n      return;\n    }\n    \n    // 选择账户\n    const account = await selectAccount(campaign);\n    if (!account) {\n      console.error(`Campaign ${campaign.name}: No account available`);\n      return;\n    }\n    \n    // 选择社区\n    const community = selectCommunity(campaign, template.platform);\n    if (!community) {\n      console.error(`Campaign ${campaign.name}: No community available`);\n      return;\n    }\n    \n    // 生成内容\n    const content = generatePostContent(template, campaign.contentVariation);\n    \n    // 创建待发布的帖子\n    const post = new ForumPost({\n      campaignId: campaign._id,\n      templateId: template._id,\n      accountId: account._id,\n      platform: template.platform,\n      community: {\n        name: community.name,\n        id: community.id,\n        url: community.url\n      },\n      content,\n      posting: {\n        status: 'queued',\n        scheduledFor: campaign.scheduling.nextPostAt\n      }\n    });\n    \n    await post.save();\n    \n    // 更新下次发帖时间\n    campaign.scheduling.nextPostAt = calculateNextPostTime(campaign.scheduling);\n    await campaign.save();\n    \n    console.log(`Scheduled post for campaign ${campaign.name} at ${post.posting.scheduledFor}`);\n  } catch (error) {\n    console.error('Error scheduling post:', error);\n  }\n}\n\n// 选择模板\nasync function selectTemplate(campaign) {\n  const templates = await PostTemplate.find({\n    _id: { $in: campaign.postConfig.templates },\n    active: true\n  });\n  \n  if (templates.length === 0) return null;\n  \n  if (campaign.postConfig.rotateTemplates) {\n    // 基于使用次数选择最少使用的模板\n    return templates.sort((a, b) => a.performance.used - b.performance.used)[0];\n  } else {\n    // 随机选择\n    return templates[Math.floor(Math.random() * templates.length)];\n  }\n}\n\n// 选择账户\nasync function selectAccount(campaign) {\n  const accounts = await ForumAccount.find({\n    _id: { $in: campaign.postConfig.accounts },\n    status: 'active'\n  });\n  \n  // 过滤可以发帖的账户\n  const availableAccounts = accounts.filter(account => account.canPost());\n  \n  if (availableAccounts.length === 0) return null;\n  \n  if (campaign.postConfig.rotateAccounts) {\n    // 选择今日使用最少的账户\n    return availableAccounts.sort((a, b) => a.usage.postsToday - b.usage.postsToday)[0];\n  } else {\n    return availableAccounts[Math.floor(Math.random() * availableAccounts.length)];\n  }\n}\n\n// 选择社区\nfunction selectCommunity(campaign, platform) {\n  const platformCommunities = campaign.targeting.communities.filter(\n    community => community.platform === platform\n  );\n  \n  if (platformCommunities.length === 0) return null;\n  \n  // 根据活跃度和成员数加权选择\n  const weightedCommunities = platformCommunities.map(community => ({\n    ...community,\n    weight: community.members * (community.activity === 'high' ? 3 : community.activity === 'medium' ? 2 : 1)\n  }));\n  \n  const totalWeight = weightedCommunities.reduce((sum, community) => sum + community.weight, 0);\n  const random = Math.random() * totalWeight;\n  \n  let currentWeight = 0;\n  for (const community of weightedCommunities) {\n    currentWeight += community.weight;\n    if (random <= currentWeight) {\n      return community;\n    }\n  }\n  \n  return platformCommunities[0];\n}\n\n// 生成帖子内容\nfunction generatePostContent(template, contentVariation) {\n  let title = template.content.title;\n  let body = template.content.body;\n  \n  if (contentVariation.enabled) {\n    // 应用标题变化\n    if (contentVariation.titleVariations && contentVariation.titleVariations.length > 0) {\n      const variation = contentVariation.titleVariations[\n        Math.floor(Math.random() * contentVariation.titleVariations.length)\n      ];\n      title = variation;\n    }\n    \n    // 应用内容变化\n    if (contentVariation.bodyVariations && contentVariation.bodyVariations.length > 0) {\n      const variation = contentVariation.bodyVariations[\n        Math.floor(Math.random() * contentVariation.bodyVariations.length)\n      ];\n      body = variation;\n    }\n    \n    // 简单的文本变化\n    if (contentVariation.spinText) {\n      title = applyTextSpinning(title);\n      body = applyTextSpinning(body);\n    }\n  }\n  \n  return {\n    title,\n    body,\n    hashtags: template.content.hashtags || [],\n    mentions: template.content.mentions || [],\n    media: template.content.media || []\n  };\n}\n\n// 简单的文本变化\nfunction applyTextSpinning(text) {\n  const synonyms = {\n    '很好': ['优秀', '出色', '极佳', '卓越'],\n    '帮助': ['协助', '支持', '辅助', '援助'],\n    '提高': ['改善', '增强', '优化', '提升'],\n    '工具': ['软件', '应用', '程序', '系统']\n  };\n  \n  let result = text;\n  Object.keys(synonyms).forEach(word => {\n    if (result.includes(word) && Math.random() > 0.5) {\n      const replacement = synonyms[word][Math.floor(Math.random() * synonyms[word].length)];\n      result = result.replace(word, replacement);\n    }\n  });\n  \n  return result;\n}\n\n// 执行实际发帖\nasync function executePost(post) {\n  try {\n    post.posting.status = 'posting';\n    await post.save();\n    \n    // 获取账户信息\n    const account = await ForumAccount.findById(post.accountId);\n    if (!account || !account.canPost()) {\n      throw new Error('Account not available for posting');\n    }\n    \n    let result;\n    \n    switch (post.platform) {\n      case 'reddit':\n        result = await postToReddit(post, account);\n        break;\n      case 'twitter':\n        result = await postToTwitter(post, account);\n        break;\n      default:\n        throw new Error(`Platform ${post.platform} not supported`);\n    }\n    \n    // 更新发帖状态\n    post.posting.status = 'posted';\n    post.posting.postedAt = new Date();\n    post.posting.platformPostId = result.postId;\n    post.posting.platformUrl = result.url;\n    \n    // 更新账户使用统计\n    account.usage.postsToday++;\n    account.lastActivity = new Date();\n    \n    await Promise.all([post.save(), account.save()]);\n    \n    // 更新模板统计\n    await PostTemplate.findByIdAndUpdate(post.templateId, {\n      $inc: { 'performance.used': 1 }\n    });\n    \n    // 更新活动统计\n    await ForumPostCampaign.findByIdAndUpdate(post.campaignId, {\n      $inc: { 'stats.totalPosts': 1, 'stats.successfulPosts': 1 }\n    });\n    \n    console.log(`Successfully posted to ${post.platform}: ${result.url}`);\n    \n  } catch (error) {\n    console.error('Post execution failed:', error);\n    \n    post.posting.status = 'failed';\n    post.posting.errorMessage = error.message;\n    post.posting.retryCount++;\n    \n    await post.save();\n    \n    // 更新活动统计\n    await ForumPostCampaign.findByIdAndUpdate(post.campaignId, {\n      $inc: { 'stats.totalPosts': 1, 'stats.failedPosts': 1 }\n    });\n  }\n}\n\n// 发布到 Reddit\nasync function postToReddit(post, account) {\n  const reddit = new RedditClient(account.credentials);\n  \n  return await reddit.submitPost(\n    post.community.name,\n    post.content.title,\n    post.content.body\n  );\n}\n\n// 发布到 Twitter（示例）\nasync function postToTwitter(post, account) {\n  // Twitter API integration would go here\n  return {\n    postId: 'twitter_' + Date.now(),\n    url: 'https://twitter.com/fake_post'\n  };\n}\n\n// 获取帖子列表\nexports.getForumPosts = async (req, res) => {\n  try {\n    const { campaignId, platform, status, page = 1, limit = 20 } = req.query;\n    const filter = {};\n    \n    if (campaignId) filter.campaignId = campaignId;\n    if (platform) filter.platform = platform;\n    if (status) filter['posting.status'] = status;\n    \n    const posts = await ForumPost.find(filter)\n      .populate('campaignId', 'name')\n      .populate('templateId', 'name type')\n      .populate('accountId', 'platform accountName')\n      .sort({ createdAt: -1 })\n      .limit(limit * 1)\n      .skip((page - 1) * limit);\n    \n    const total = await ForumPost.countDocuments(filter);\n    \n    res.json({\n      posts,\n      pagination: {\n        page: parseInt(page),\n        limit: parseInt(limit),\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 手动触发发帖\nexports.triggerPost = async (req, res) => {\n  try {\n    const { campaignId } = req.body;\n    \n    const campaign = await ForumPostCampaign.findById(campaignId);\n    if (!campaign) {\n      return res.status(404).json({ error: '活动未找到' });\n    }\n    \n    if (campaign.status !== 'active') {\n      return res.status(400).json({ error: '活动未激活' });\n    }\n    \n    await scheduleNextPost(campaign);\n    \n    res.json({ success: true, message: '已创建新的发帖任务' });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 获取活动统计\nexports.getCampaignStats = async (req, res) => {\n  try {\n    const { period = '30d' } = req.query;\n    const days = parseInt(period.replace('d', ''));\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - days);\n    \n    const stats = await ForumPostCampaign.aggregate([\n      { $match: { createdAt: { $gte: startDate } } },\n      {\n        $group: {\n          _id: null,\n          totalCampaigns: { $sum: 1 },\n          activeCampaigns: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },\n          totalPosts: { $sum: '$stats.totalPosts' },\n          successfulPosts: { $sum: '$stats.successfulPosts' },\n          totalUpvotes: { $sum: '$stats.totalUpvotes' },\n          totalComments: { $sum: '$stats.totalComments' }\n        }\n      }\n    ]);\n    \n    const platformStats = await ForumPost.aggregate([\n      { $match: { createdAt: { $gte: startDate } } },\n      {\n        $group: {\n          _id: '$platform',\n          posts: { $sum: 1 },\n          successful: { $sum: { $cond: [{ $eq: ['$posting.status', 'posted'] }, 1, 0] } },\n          avgUpvotes: { $avg: '$performance.upvotes' },\n          avgComments: { $avg: '$performance.comments' }\n        }\n      }\n    ]);\n    \n    const result = stats[0] || {\n      totalCampaigns: 0,\n      activeCampaigns: 0,\n      totalPosts: 0,\n      successfulPosts: 0,\n      totalUpvotes: 0,\n      totalComments: 0\n    };\n    \n    res.json({\n      overview: result,\n      platformBreakdown: platformStats,\n      period: `${days} days`\n    });\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n};\n\n// 定时任务：执行待发布的帖子\ncron.schedule('*/5 * * * *', async () => {\n  try {\n    const now = new Date();\n    const postsToExecute = await ForumPost.find({\n      'posting.status': 'queued',\n      'posting.scheduledFor': { $lte: now }\n    }).limit(10); // 限制并发数量\n    \n    for (const post of postsToExecute) {\n      await executePost(post);\n      // 在帖子之间添加延迟\n      await new Promise(resolve => setTimeout(resolve, 30000)); // 30秒延迟\n    }\n  } catch (error) {\n    console.error('Cron job error:', error);\n  }\n});\n\n// 定时任务：为活跃的活动调度新帖子\ncron.schedule('0 */6 * * *', async () => {\n  try {\n    const now = new Date();\n    const campaignsToSchedule = await ForumPostCampaign.find({\n      status: 'active',\n      'scheduling.nextPostAt': { $lte: now }\n    });\n    \n    for (const campaign of campaignsToSchedule) {\n      await scheduleNextPost(campaign);\n    }\n  } catch (error) {\n    console.error('Campaign scheduling error:', error);\n  }\n});\n\nmodule.exports = exports;