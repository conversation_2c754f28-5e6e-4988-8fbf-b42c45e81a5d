const { 
  BacklinkProspect, 
  OutreachCampaign, 
  OutreachEmail, 
  BacklinkResult,
  BacklinkOutreach 
} = require('../models/BacklinkOutreach');
const nodemailer = require('nodemailer');
const cheerio = require('cheerio');
const axios = require('axios');

// 外链机会发现类
class BacklinkDiscovery {
  // 分析竞争对手的外链
  static async analyzeCompetitorBacklinks(competitorUrl) {
    try {
      // 这里应该集成真实的SEO工具API，如Ahrefs, SEMrush等
      // 为了演示，我们使用模拟数据
      const mockBacklinks = [
        {
          url: 'https://productivityblog.com/best-tools',
          domain: 'productivityblog.com',
          title: 'Best Productivity Tools 2024',
          metrics: {
            domainAuthority: 65,
            pageAuthority: 58,
            organicTraffic: 15000
          },
          anchorText: 'time management app',
          discovered: {
            method: 'competitor_analysis',
            source: competitorUrl
          }
        },
        {
          url: 'https://techreview.io/pomodoro-apps',
          domain: 'techreview.io',
          title: 'Top Pomodoro Apps Review',
          metrics: {
            domainAuthority: 72,
            pageAuthority: 61,
            organicTraffic: 22000
          },
          anchorText: 'productivity software',
          discovered: {
            method: 'competitor_analysis',
            source: competitorUrl
          }
        }
      ];

      return mockBacklinks;
    } catch (error) {
      console.error('Error analyzing competitor backlinks:', error);
      return [];
    }
  }

  // 根据关键词寻找外链机会
  static async findByKeywords(keywords) {
    try {
      // 集成Google Search API或其他搜索工具
      const searchQueries = [
        `"${keywords[0]}" + write for us`,
        `"${keywords[0]}" + guest post`,
        `"${keywords[0]}" + submit article`,
        `best ${keywords[0]} tools`,
        `${keywords[0]} resources`
      ];

      const opportunities = [];
      
      for (const query of searchQueries) {
        // 模拟搜索结果
        const mockResults = [
          {
            url: `https://example-blog.com/productivity-resources`,
            domain: 'example-blog.com',
            title: 'Productivity Resources Directory',
            description: 'Comprehensive list of productivity tools and apps',
            discovered: {
              method: 'keyword_research',
              source: query
            }
          }
        ];
        
        opportunities.push(...mockResults);
      }

      return opportunities;
    } catch (error) {
      console.error('Error finding opportunities by keywords:', error);
      return [];
    }
  }

  // 发现断链建设机会
  static async findBrokenLinkOpportunities(targetDomain) {
    try {
      // 集成工具来找到指向目标域名的断链
      const brokenLinks = [
        {
          sourceUrl: 'https://productivity-tips.com/tools-list',
          brokenUrl: `https://${targetDomain}/old-page`,
          suggestedReplacement: `https://${targetDomain}/new-page`,
          contactEmail: '<EMAIL>',
          discovered: {
            method: 'broken_link_building',
            source: 'link_checker'
          }
        }
      ];

      return brokenLinks;
    } catch (error) {
      console.error('Error finding broken link opportunities:', error);
      return [];
    }
  }
}

// 联系人查找器
class ContactFinder {
  // 从网站提取联系信息
  static async extractContactInfo(url) {
    try {
      const response = await axios.get(url, { timeout: 10000 });
      const $ = cheerio.load(response.data);
      
      const contactInfo = {
        emails: [],
        socialProfiles: {},
        contactPage: null
      };

      // 查找邮箱地址
      const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
      const text = $.text();
      const emails = text.match(emailRegex) || [];
      
      // 过滤掉常见的无用邮箱
      const filteredEmails = emails.filter(email => 
        !email.includes('example.com') && 
        !email.includes('test.com') &&
        !email.includes('noreply') &&
        !email.includes('no-reply')
      );
      
      contactInfo.emails = [...new Set(filteredEmails)];

      // 查找社交媒体链接
      $('a[href*="twitter.com"], a[href*="linkedin.com"], a[href*="facebook.com"]').each((i, el) => {
        const href = $(el).attr('href');
        if (href.includes('twitter.com')) {
          contactInfo.socialProfiles.twitter = href;
        } else if (href.includes('linkedin.com')) {
          contactInfo.socialProfiles.linkedin = href;
        } else if (href.includes('facebook.com')) {
          contactInfo.socialProfiles.facebook = href;
        }
      });

      // 查找联系页面
      $('a').each((i, el) => {
        const text = $(el).text().toLowerCase();
        const href = $(el).attr('href');
        if ((text.includes('contact') || text.includes('about') || text.includes('team')) && href) {
          contactInfo.contactPage = new URL(href, url).href;
        }
      });

      return contactInfo;
    } catch (error) {
      console.error('Error extracting contact info:', error);
      return { emails: [], socialProfiles: {}, contactPage: null };
    }
  }

  // 验证邮箱地址
  static async verifyEmail(email) {
    try {
      // 这里可以集成邮箱验证服务
      // 简单的格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    } catch (error) {
      return false;
    }
  }
}

// 邮件发送器
class OutreachMailer {
  static createTransporter() {
    return nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  // 创建个性化邮件模板
  static createPersonalizedEmail(template, prospect, campaign) {
    let subject = template.subject || '合作机会：AI-Pomo 生产力工具';
    let body = template.body || this.getDefaultTemplate();

    // 替换占位符
    const replacements = {
      '{name}': prospect.contactName || 'Hi there',
      '{website}': prospect.domain,
      '{title}': prospect.title || prospect.domain,
      '{anchor_text}': campaign.linkTarget.anchorText || 'AI-Pomo',
      '{target_url}': campaign.linkTarget.url,
      '{content_topic}': campaign.description || '生产力工具',
      '{keywords}': campaign.targetKeywords?.join(', ') || '生产力, 时间管理'
    };

    Object.entries(replacements).forEach(([placeholder, value]) => {
      subject = subject.replace(new RegExp(placeholder, 'g'), value);
      body = body.replace(new RegExp(placeholder, 'g'), value);
    });

    return { subject, body };
  }

  static getDefaultTemplate() {
    return `
Hi {name},

I hope you're doing well! I'm reaching out because I came across {website} and was impressed by your content about productivity and time management.

I'm the developer of AI-Pomo (https://ai-pomo.com), an AI-powered Pomodoro timer that helps people boost their productivity through intelligent task management and focus sessions.

I noticed you have a great article about {content_topic}, and I think your audience would find value in our tool. Would you be interested in:

1. **Guest Post**: I could write a detailed article about "{content_topic}" that naturally mentions AI-Pomo as a helpful tool
2. **Product Review**: I'd be happy to provide free premium access for you to review our features
3. **Resource Page Addition**: If you have a tools/resources page, AI-Pomo would be a great addition

Our tool has helped thousands of users improve their productivity by an average of 35%, and we've been featured in several productivity blogs.

I'd love to discuss how we can provide value to your audience. Would you be open to a quick collaboration?

Best regards,
AI-Pomo Team
https://ai-pomo.com

P.S. I'd be happy to offer your readers a special discount code if that would be helpful!
    `.trim();
  }

  // 发送外联邮件
  static async sendOutreachEmail(emailData) {
    try {
      const transporter = this.createTransporter();
      
      const mailOptions = {
        from: `"AI-Pomo Team" <${process.env.SMTP_USER}>`,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.body.replace(/\n/g, '<br>'),
        text: emailData.body,
        headers: {
          'X-Campaign-ID': emailData.campaignId,
          'X-Prospect-ID': emailData.prospectId
        }
      };

      const result = await transporter.sendMail(mailOptions);
      return {
        success: true,
        messageId: result.messageId,
        response: result.response
      };
    } catch (error) {
      console.error('Error sending outreach email:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 控制器方法
exports.getProspects = async (req, res) => {
  try {
    const { 
      status, 
      priority, 
      category, 
      minDA = 0, 
      maxDA = 100,
      page = 1, 
      limit = 20,
      sortBy = 'lastUpdated',
      sortOrder = 'desc'
    } = req.query;

    const filter = {};
    if (status) filter.status = status;
    if (priority) filter.priority = priority;
    if (category) filter.categories = { $in: [category] };
    if (minDA || maxDA) {
      filter['metrics.domainAuthority'] = { 
        $gte: parseInt(minDA), 
        $lte: parseInt(maxDA) 
      };
    }

    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const prospects = await BacklinkProspect.find(filter)
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .exec();

    const total = await BacklinkProspect.countDocuments(filter);

    res.json({
      prospects,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching prospects:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.createProspect = async (req, res) => {
  try {
    const prospectData = req.body;
    
    // 如果提供了URL，尝试提取联系信息
    if (prospectData.url && !prospectData.contactEmail) {
      const contactInfo = await ContactFinder.extractContactInfo(prospectData.url);
      if (contactInfo.emails.length > 0) {
        prospectData.contactEmail = contactInfo.emails[0];
      }
      if (Object.keys(contactInfo.socialProfiles).length > 0) {
        prospectData.socialMedia = contactInfo.socialProfiles;
      }
    }

    const prospect = new BacklinkProspect(prospectData);
    await prospect.save();

    res.status(201).json(prospect);
  } catch (error) {
    console.error('Error creating prospect:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.updateProspect = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = { ...req.body, lastUpdated: new Date() };

    const prospect = await BacklinkProspect.findByIdAndUpdate(
      id, 
      updateData, 
      { new: true }
    );

    if (!prospect) {
      return res.status(404).json({ error: 'Prospect not found' });
    }

    res.json(prospect);
  } catch (error) {
    console.error('Error updating prospect:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.discoverProspects = async (req, res) => {
  try {
    const { method, source, keywords } = req.body;

    let discoveries = [];

    switch (method) {
      case 'competitor_analysis':
        discoveries = await BacklinkDiscovery.analyzeCompetitorBacklinks(source);
        break;
      case 'keyword_research':
        discoveries = await BacklinkDiscovery.findByKeywords(keywords);
        break;
      case 'broken_link_building':
        discoveries = await BacklinkDiscovery.findBrokenLinkOpportunities(source);
        break;
    }

    // 保存发现的机会到数据库
    const savedProspects = [];
    for (const discovery of discoveries) {
      try {
        const existingProspect = await BacklinkProspect.findOne({ 
          url: discovery.url 
        });

        if (!existingProspect) {
          const prospect = new BacklinkProspect(discovery);
          await prospect.save();
          savedProspects.push(prospect);
        }
      } catch (saveError) {
        console.error('Error saving prospect:', saveError);
      }
    }

    res.json({
      discovered: discoveries.length,
      saved: savedProspects.length,
      prospects: savedProspects
    });
  } catch (error) {
    console.error('Error discovering prospects:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.getCampaigns = async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    
    const filter = { createdBy: req.user.id };
    if (status) filter.status = status;

    const campaigns = await OutreachCampaign.find(filter)
      .populate('prospects.prospect')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await OutreachCampaign.countDocuments(filter);

    res.json({
      campaigns,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.createCampaign = async (req, res) => {
  try {
    const campaignData = {
      ...req.body,
      createdBy: req.user.id
    };

    const campaign = new OutreachCampaign(campaignData);
    await campaign.save();

    res.status(201).json(campaign);
  } catch (error) {
    console.error('Error creating campaign:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.startCampaign = async (req, res) => {
  try {
    const { id } = req.params;
    
    const campaign = await OutreachCampaign.findById(id)
      .populate('prospects.prospect');

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    if (campaign.createdBy.toString() !== req.user.id) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    // 开始发送邮件
    let emailsSent = 0;
    const emailResults = [];

    for (const prospectEntry of campaign.prospects) {
      try {
        const prospect = prospectEntry.prospect;
        
        if (!prospect.contactEmail) {
          continue;
        }

        const emailTemplate = OutreachMailer.createPersonalizedEmail(
          campaign.emailTemplate,
          prospect,
          campaign
        );

        const emailData = {
          to: prospect.contactEmail,
          subject: emailTemplate.subject,
          body: emailTemplate.body,
          campaignId: campaign._id,
          prospectId: prospect._id
        };

        const result = await OutreachMailer.sendOutreachEmail(emailData);

        if (result.success) {
          // 记录邮件发送
          const outreachEmail = new OutreachEmail({
            campaign: campaign._id,
            prospect: prospect._id,
            emailType: 'initial',
            to: prospect.contactEmail,
            subject: emailTemplate.subject,
            body: emailTemplate.body,
            sentAt: new Date(),
            status: 'sent',
            trackingId: result.messageId
          });
          await outreachEmail.save();

          emailsSent++;
          emailResults.push({ prospect: prospect._id, success: true });
        } else {
          emailResults.push({ 
            prospect: prospect._id, 
            success: false, 
            error: result.error 
          });
        }

        // 延迟避免被标记为垃圾邮件
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (emailError) {
        console.error('Error sending email to prospect:', emailError);
        emailResults.push({ 
          prospect: prospectEntry.prospect._id, 
          success: false, 
          error: emailError.message 
        });
      }
    }

    // 更新活动状态和统计
    campaign.status = 'active';
    campaign.stats.totalSent += emailsSent;
    await campaign.save();

    res.json({
      message: `Campaign started. ${emailsSent} emails sent successfully.`,
      emailsSent,
      results: emailResults
    });
  } catch (error) {
    console.error('Error starting campaign:', error);
    res.status(500).json({ error: error.message });
  }
};

exports.getOutreachStats = async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = await Promise.all([
      BacklinkProspect.countDocuments(),
      BacklinkProspect.countDocuments({ status: 'contacted' }),
      BacklinkProspect.countDocuments({ status: 'replied' }),
      BacklinkProspect.countDocuments({ status: 'published' }),
      OutreachCampaign.countDocuments({ createdBy: userId }),
      OutreachCampaign.countDocuments({ createdBy: userId, status: 'active' }),
      OutreachEmail.countDocuments({ status: 'sent' }),
      BacklinkResult.countDocuments({ status: 'live' })
    ]);

    res.json({
      totalProspects: stats[0],
      contacted: stats[1],
      replied: stats[2],
      linksReceived: stats[3],
      totalCampaigns: stats[4],
      activeCampaigns: stats[5],
      emailsSent: stats[6],
      liveBacklinks: stats[7],
      replyRate: stats[1] > 0 ? ((stats[2] / stats[1]) * 100).toFixed(1) : 0,
      successRate: stats[1] > 0 ? ((stats[3] / stats[1]) * 100).toFixed(1) : 0
    });
  } catch (error) {
    console.error('Error fetching outreach stats:', error);
    res.status(500).json({ error: error.message });
  }
};

module.exports = exports;