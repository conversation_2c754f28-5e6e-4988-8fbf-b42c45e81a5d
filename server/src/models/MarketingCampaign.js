const mongoose = require('mongoose');

const marketingCampaignSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['email', 'forum', 'social', 'blog', 'backlink'],
    required: true
  },
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'active', 'paused', 'completed'],
    default: 'draft'
  },
  
  // Email Lead Generation
  emailLeads: {
    sources: [{
      type: {
        type: String,
        enum: ['landing_page', 'exit_intent', 'content_upgrade', 'webinar', 'free_tool']
      },
      url: String,
      title: String,
      description: String,
      isActive: { type: Boolean, default: true }
    }],
    magnets: [{
      title: String,
      type: { type: String, enum: ['ebook', 'template', 'checklist', 'course', 'tool_access'] },
      downloadUrl: String,
      conversionRate: { type: Number, default: 0 }
    }]
  },
  
  // Forum Marketing
  forumTargets: [{
    platform: {
      type: String,
      enum: ['reddit', 'quora', 'tumblr', 'hackernews', 'producthunt', 'other']
    },
    subreddit: String, // For Reddit
    topics: [String], // For Quora
    tags: [String], // For Tumblr
    targetAudience: String,
    postingSchedule: {
      frequency: String, // "2 per week", "daily", etc.
      bestTimes: [String] // Best posting times
    },
    templates: [{
      title: String,
      content: String,
      type: { type: String, enum: ['question', 'answer', 'discussion', 'showcase'] },
      performance: {
        upvotes: { type: Number, default: 0 },
        comments: { type: Number, default: 0 },
        clicks: { type: Number, default: 0 }
      }
    }],
    competitors: [{
      username: String,
      profileUrl: String,
      successfulPosts: [{
        url: String,
        title: String,
        upvotes: Number,
        analysis: String
      }]
    }]
  }],
  
  // Social Media
  socialChannels: [{
    platform: {
      type: String,
      enum: ['twitter', 'linkedin', 'facebook', 'instagram', 'tiktok', 'youtube']
    },
    accountUrl: String,
    contentCalendar: [{
      date: Date,
      content: String,
      mediaUrls: [String],
      hashtags: [String],
      status: {
        type: String,
        enum: ['scheduled', 'published', 'failed'],
        default: 'scheduled'
      }
    }],
    automationRules: [{
      trigger: String, // "new_blog_post", "user_milestone", etc.
      action: String,
      template: String,
      isActive: { type: Boolean, default: true }
    }]
  }],
  
  // Content Distribution
  contentTargets: [{
    platform: {
      type: String,
      enum: ['medium', 'dev.to', 'hashnode', 'linkedin', 'substack', 'other']
    },
    accountUrl: String,
    apiKey: String, // Encrypted
    categories: [String],
    publishedArticles: [{
      originalUrl: String, // AI Pomo blog URL
      publishedUrl: String,
      title: String,
      publishedAt: Date,
      views: { type: Number, default: 0 },
      clicks: { type: Number, default: 0 }
    }]
  }],
  
  // Backlink Outreach
  backlinkTargets: [{
    website: String,
    domainAuthority: Number,
    contactInfo: {
      email: String,
      name: String,
      position: String
    },
    status: {
      type: String,
      enum: ['identified', 'contacted', 'negotiating', 'accepted', 'rejected', 'published'],
      default: 'identified'
    },
    outreachTemplate: String,
    followUps: [{
      date: Date,
      message: String,
      response: String
    }],
    result: {
      linkUrl: String,
      anchorText: String,
      publishedAt: Date
    }
  }],
  
  // Performance Metrics
  metrics: {
    leads: {
      total: { type: Number, default: 0 },
      converted: { type: Number, default: 0 },
      conversionRate: { type: Number, default: 0 }
    },
    traffic: {
      referral: { type: Number, default: 0 },
      direct: { type: Number, default: 0 },
      organic: { type: Number, default: 0 }
    },
    engagement: {
      posts: { type: Number, default: 0 },
      upvotes: { type: Number, default: 0 },
      comments: { type: Number, default: 0 },
      shares: { type: Number, default: 0 }
    },
    backlinks: {
      total: { type: Number, default: 0 },
      doFollow: { type: Number, default: 0 },
      avgDA: { type: Number, default: 0 }
    }
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
marketingCampaignSchema.index({ type: 1, status: 1 });
marketingCampaignSchema.index({ 'forumTargets.platform': 1 });
marketingCampaignSchema.index({ 'socialChannels.platform': 1 });

module.exports = mongoose.model('MarketingCampaign', marketingCampaignSchema);