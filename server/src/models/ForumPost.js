const mongoose = require('mongoose');

// 论坛账户管理 Schema
const forumAccountSchema = new mongoose.Schema({
  platform: { 
    type: String, 
    enum: ['reddit', 'quora', 'tumblr', 'twitter', 'linkedin', 'zhihu', 'xia<PERSON><PERSON><PERSON>'], 
    required: true 
  },
  accountName: { type: String, required: true },
  credentials: {
    username: String,
    password: String, // 加密存储
    accessToken: String,
    refreshToken: String,
    apiKey: String,
    clientId: String,
    clientSecret: String
  },
  profile: {
    displayName: String,
    bio: String,
    avatarUrl: String,
    followers: Number,
    reputation: Number,
    karma: Number, // Reddit specific
    verified: Boolean
  },
  status: { 
    type: String, 
    enum: ['active', 'suspended', 'banned', 'limited'], 
    default: 'active' 
  },
  lastActivity: { type: Date, default: Date.now },
  dailyLimits: {
    posts: { type: Number, default: 5 },
    comments: { type: Number, default: 20 },
    likes: { type: Number, default: 100 },
    follows: { type: Number, default: 50 }
  },
  usage: {
    postsToday: { type: Number, default: 0 },
    commentsToday: { type: Number, default: 0 },
    likesToday: { type: Number, default: 0 },
    followsToday: { type: Number, default: 0 },
    lastResetDate: { type: Date, default: Date.now }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 发帖内容模板 Schema
const postTemplateSchema = new mongoose.Schema({
  name: { type: String, required: true },
  platform: { 
    type: String, 
    enum: ['reddit', 'quora', 'tumblr', 'twitter', 'linkedin', 'zhihu', 'xiaohongshu'], 
    required: true 
  },
  type: { 
    type: String, 
    enum: ['promotion', 'educational', 'engagement', 'support', 'news'], 
    required: true 
  },
  content: {
    title: { type: String, required: true },
    body: { type: String, required: true },
    hashtags: [String],
    mentions: [String],
    media: [{
      type: String, // 'image', 'video', 'gif'
      url: String,
      caption: String
    }]
  },
  targeting: {
    subreddits: [String], // Reddit specific
    communities: [String], // Generic communities
    topics: [String],
    keywords: [String],
    audienceType: String
  },
  variables: [{
    placeholder: String,
    description: String,
    defaultValue: String
  }],
  performance: {
    used: { type: Number, default: 0 },
    avgUpvotes: { type: Number, default: 0 },
    avgComments: { type: Number, default: 0 },
    avgEngagement: { type: Number, default: 0 }
  },
  active: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 论坛发帖活动 Schema
const forumPostCampaignSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['draft', 'scheduled', 'active', 'paused', 'completed'], 
    default: 'draft' 
  },
  
  // 发帖配置
  postConfig: {
    templates: [{ type: mongoose.Schema.Types.ObjectId, ref: 'PostTemplate' }],
    accounts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ForumAccount' }],
    rotateTemplates: { type: Boolean, default: true },
    rotateAccounts: { type: Boolean, default: true }
  },
  
  // 目标平台和社区
  targeting: {
    platforms: [String],
    communities: [{
      platform: String,
      name: String,
      id: String,
      members: Number,
      activity: String, // 'high', 'medium', 'low'
      rules: [String],
      bestTimes: [String]
    }],
    keywords: [String],
    excludeKeywords: [String]
  },
  
  // 发帖时间表
  scheduling: {
    timezone: { type: String, default: 'Asia/Shanghai' },
    frequency: { 
      type: String, 
      enum: ['daily', 'every_other_day', 'weekly', 'manual'], 
      default: 'daily' 
    },
    timesPerDay: { type: Number, default: 1 },
    preferredTimes: [String], // ['09:00', '14:00', '20:00']
    daysOfWeek: [Number], // 0=Sunday, 6=Saturday
    startDate: Date,
    endDate: Date,
    nextPostAt: Date
  },
  
  // 内容变化设置
  contentVariation: {
    enabled: { type: Boolean, default: true },
    titleVariations: [String],
    bodyVariations: [String],
    hashtagRotation: { type: Boolean, default: true },
    spinText: { type: Boolean, default: false } // 简单的文本变化
  },
  
  // 自动化设置
  automation: {
    autoApprove: { type: Boolean, default: false },
    respondToComments: { type: Boolean, default: true },
    responseTemplates: [String],
    upvoteOwnPosts: { type: Boolean, default: false },
    crossPostDelay: { type: Number, default: 3600000 }, // 1 hour in ms
    deleteOnNegativeScore: { type: Boolean, default: false },
    negativeScoreThreshold: { type: Number, default: -5 }
  },
  
  // 统计和性能
  stats: {
    totalPosts: { type: Number, default: 0 },
    successfulPosts: { type: Number, default: 0 },
    failedPosts: { type: Number, default: 0 },
    totalUpvotes: { type: Number, default: 0 },
    totalComments: { type: Number, default: 0 },
    totalViews: { type: Number, default: 0 },
    avgEngagement: { type: Number, default: 0 },
    bestPerforming: {
      platform: String,
      community: String,
      postId: String,
      score: Number
    }
  },
  
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});\n\n// 单个论坛帖子记录 Schema\nconst forumPostSchema = new mongoose.Schema({\n  campaignId: { type: mongoose.Schema.Types.ObjectId, ref: 'ForumPostCampaign', required: true },\n  templateId: { type: mongoose.Schema.Types.ObjectId, ref: 'PostTemplate' },\n  accountId: { type: mongoose.Schema.Types.ObjectId, ref: 'ForumAccount', required: true },\n  \n  platform: { \n    type: String, \n    enum: ['reddit', 'quora', 'tumblr', 'twitter', 'linkedin', 'zhihu', 'xiaohongshu'], \n    required: true \n  },\n  community: {\n    name: String,\n    id: String,\n    url: String\n  },\n  \n  content: {\n    title: { type: String, required: true },\n    body: { type: String, required: true },\n    hashtags: [String],\n    mentions: [String],\n    media: [{\n      type: String,\n      url: String,\n      caption: String\n    }]\n  },\n  \n  posting: {\n    status: { \n      type: String, \n      enum: ['queued', 'posting', 'posted', 'failed', 'deleted'], \n      default: 'queued' \n    },\n    scheduledFor: Date,\n    postedAt: Date,\n    platformPostId: String,\n    platformUrl: String,\n    errorMessage: String,\n    retryCount: { type: Number, default: 0 },\n    maxRetries: { type: Number, default: 3 }\n  },\n  \n  performance: {\n    views: { type: Number, default: 0 },\n    upvotes: { type: Number, default: 0 },\n    downvotes: { type: Number, default: 0 },\n    comments: { type: Number, default: 0 },\n    shares: { type: Number, default: 0 },\n    clicks: { type: Number, default: 0 },\n    engagement: { type: Number, default: 0 },\n    score: { type: Number, default: 0 },\n    lastUpdated: { type: Date, default: Date.now }\n  },\n  \n  interactions: [{\n    type: { type: String, enum: ['comment', 'reply', 'vote', 'report'] },\n    content: String,\n    timestamp: { type: Date, default: Date.now },\n    userId: String,\n    automated: { type: Boolean, default: false }\n  }],\n  \n  createdAt: { type: Date, default: Date.now },\n  updatedAt: { type: Date, default: Date.now }\n});\n\n// 论坛社区数据 Schema\nconst forumCommunitySchema = new mongoose.Schema({\n  platform: { \n    type: String, \n    enum: ['reddit', 'quora', 'tumblr', 'twitter', 'linkedin', 'zhihu', 'xiaohongshu'], \n    required: true \n  },\n  name: { type: String, required: true },\n  id: { type: String, required: true }, // platform specific ID\n  url: String,\n  \n  stats: {\n    members: Number,\n    activeUsers: Number,\n    postsPerDay: Number,\n    growthRate: Number,\n    engagementRate: Number\n  },\n  \n  characteristics: {\n    category: String,\n    topics: [String],\n    language: { type: String, default: 'zh' },\n    timeZone: String,\n    averageAge: String,\n    genderSplit: {\n      male: Number,\n      female: Number,\n      other: Number\n    }\n  },\n  \n  rules: [{\n    title: String,\n    description: String,\n    severity: String // 'strict', 'moderate', 'relaxed'\n  }],\n  \n  bestPractices: {\n    bestTimes: [String],\n    contentTypes: [String], // 'text', 'image', 'video', 'link'\n    toneRecommendation: String,\n    hashtagUsage: String,\n    linkPolicy: String\n  },\n  \n  moderation: {\n    automated: Boolean,\n    responseTime: String,\n    strictness: String,\n    commonViolations: [String]\n  },\n  \n  relevanceScore: { type: Number, default: 0 }, // 对产品的相关性\n  opportunityScore: { type: Number, default: 0 }, // 营销机会评分\n  lastAnalyzed: { type: Date, default: Date.now },\n  \n  createdAt: { type: Date, default: Date.now },\n  updatedAt: { type: Date, default: Date.now }\n});\n\n// 方法：重置每日使用计数\nforumAccountSchema.methods.resetDailyUsage = function() {\n  const today = new Date();\n  const lastReset = this.usage.lastResetDate;\n  \n  if (today.toDateString() !== lastReset.toDateString()) {\n    this.usage.postsToday = 0;\n    this.usage.commentsToday = 0;\n    this.usage.likesToday = 0;\n    this.usage.followsToday = 0;\n    this.usage.lastResetDate = today;\n  }\n};\n\n// 方法：检查是否可以发帖\nforumAccountSchema.methods.canPost = function() {\n  this.resetDailyUsage();\n  return this.usage.postsToday < this.dailyLimits.posts && this.status === 'active';\n};\n\n// 方法：计算活动表现\nforumPostCampaignSchema.methods.calculatePerformance = function() {\n  if (this.stats.totalPosts > 0) {\n    this.stats.avgEngagement = (\n      (this.stats.totalUpvotes + this.stats.totalComments) / this.stats.totalPosts\n    ).toFixed(2);\n  }\n};\n\n// 更新时间戳\nforumAccountSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\npostTemplateSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\nforumPostCampaignSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  this.calculatePerformance();\n  next();\n});\n\nforumPostSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\nforumCommunitySchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\n// 索引\nforumAccountSchema.index({ platform: 1, accountName: 1 }, { unique: true });\nforumPostSchema.index({ campaignId: 1, 'posting.status': 1 });\nforumPostSchema.index({ platform: 1, 'posting.scheduledFor': 1 });\nforumCommunitySchema.index({ platform: 1, name: 1 }, { unique: true });\n\nconst ForumAccount = mongoose.model('ForumAccount', forumAccountSchema);\nconst PostTemplate = mongoose.model('PostTemplate', postTemplateSchema);\nconst ForumPostCampaign = mongoose.model('ForumPostCampaign', forumPostCampaignSchema);\nconst ForumPost = mongoose.model('ForumPost', forumPostSchema);\nconst ForumCommunity = mongoose.model('ForumCommunity', forumCommunitySchema);\n\nmodule.exports = {\n  ForumAccount,\n  PostTemplate,\n  ForumPostCampaign,\n  ForumPost,\n  ForumCommunity\n};