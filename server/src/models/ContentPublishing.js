const mongoose = require('mongoose');

// 内容发布平台配置 Schema
const publishingPlatformSchema = new mongoose.Schema({
  name: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['blog', 'cms', 'forum', 'social', 'newsletter'], 
    required: true 
  },
  platform: { 
    type: String, 
    enum: ['wordpress', 'medium', 'devto', 'hashnode', 'ghost', 'notion', 'juejin', 'csdn', 'zhihu', 'jianshu', 'segmentfault', 'cnblogs', 'oschina'], 
    required: true 
  },
  
  // API 配置
  apiConfig: {
    baseUrl: String,
    apiKey: String,
    accessToken: String,
    refreshToken: String,
    clientId: String,
    clientSecret: String,
    webhookUrl: String,
    customHeaders: [{
      key: String,
      value: String
    }]
  },
  
  // 认证信息
  credentials: {
    username: String,
    password: String, // 加密存储
    email: String,
    apiKey: String,
    sessionToken: String,
    cookieData: String,
    twoFactorSecret: String
  },
  
  // 平台特性
  capabilities: {
    supportsDrafts: { type: Boolean, default: true },
    supportsScheduling: { type: Boolean, default: false },
    supportsTags: { type: Boolean, default: true },
    supportsCategories: { type: Boolean, default: true },
    supportsImages: { type: Boolean, default: true },
    supportsVideos: { type: Boolean, default: false },
    supportsMarkdown: { type: Boolean, default: false },
    supportsHtml: { type: Boolean, default: true },
    maxTitleLength: { type: Number, default: 100 },
    maxContentLength: { type: Number, default: 50000 },
    maxTags: { type: Number, default: 10 },
    allowedImageFormats: [String],
    requiresFeaturedImage: { type: Boolean, default: false }
  },
  
  // 发布设置
  publishingSettings: {
    defaultStatus: { type: String, enum: ['draft', 'published', 'scheduled'], default: 'draft' },
    autoPublish: { type: Boolean, default: false },
    defaultCategory: String,
    defaultTags: [String],
    contentTemplate: String, // 内容包装模板
    footerTemplate: String, // 页脚模板
    seoSettings: {
      metaDescription: String,
      canonicalUrl: String,
      noIndex: { type: Boolean, default: false }
    }
  },
  
  // 使用限制
  rateLimits: {
    postsPerDay: { type: Number, default: 10 },
    postsPerHour: { type: Number, default: 2 },
    requestsPerMinute: { type: Number, default: 60 }
  },
  
  // 使用统计
  usage: {
    postsToday: { type: Number, default: 0 },
    postsThisMonth: { type: Number, default: 0 },
    lastPostTime: Date,
    lastResetDate: { type: Date, default: Date.now },
    totalRequests: { type: Number, default: 0 },
    failedRequests: { type: Number, default: 0 }
  },
  
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'error', 'rate_limited', 'suspended'], 
    default: 'active' 
  },
  lastTestDate: Date,
  lastError: String,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 文章内容模板 Schema
const articleTemplateSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  category: { 
    type: String, 
    enum: ['tutorial', 'guide', 'review', 'news', 'opinion', 'case_study', 'announcement', 'tips'], 
    required: true 
  },
  
  // 内容结构
  structure: {
    title: { type: String, required: true },
    subtitle: String,
    introduction: String,
    sections: [{
      heading: String,
      content: String,
      type: { type: String, enum: ['text', 'list', 'code', 'quote', 'image'] },
      order: Number
    }],
    conclusion: String,
    callToAction: String
  },
  
  // SEO 设置
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: String,
    canonicalUrl: String
  },
  
  // 内容变量
  variables: [{
    name: String,
    placeholder: String,
    type: { type: String, enum: ['text', 'number', 'date', 'url', 'image'] },
    required: { type: Boolean, default: false },
    defaultValue: String,
    description: String
  }],
  
  // 发布配置
  publishingConfig: {
    platforms: [{ type: mongoose.Schema.Types.ObjectId, ref: 'PublishingPlatform' }],
    defaultTags: [String],
    defaultCategory: String,
    featuredImage: String,
    publishDelay: { type: Number, default: 0 }, // 分钟
    crossPostDelay: { type: Number, default: 60 } // 跨平台发布延迟
  },
  
  // 性能统计
  performance: {
    used: { type: Number, default: 0 },
    avgViews: { type: Number, default: 0 },
    avgShares: { type: Number, default: 0 },
    avgComments: { type: Number, default: 0 },
    avgRating: { type: Number, default: 0 },
    bestPerformingPost: {
      postId: String,
      platform: String,
      views: Number,
      engagement: Number
    }
  },
  
  active: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 内容发布活动 Schema
const contentCampaignSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['draft', 'active', 'paused', 'completed'], 
    default: 'draft' 
  },
  
  // 活动类型
  type: { 
    type: String, 
    enum: ['single_article', 'article_series', 'content_repurposing', 'cross_platform'], 
    required: true 
  },
  
  // 内容配置
  contentConfig: {
    templates: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ArticleTemplate' }],
    customArticles: [{
      title: String,
      content: String,
      tags: [String],
      category: String,
      featuredImage: String,
      scheduledFor: Date
    }],
    contentSources: [{
      type: { type: String, enum: ['blog_post', 'social_post', 'email', 'video_transcript'] },
      sourceId: String,
      adaptationInstructions: String
    }]
  },
  
  // 平台配置
  platformConfig: {
    platforms: [{ type: mongoose.Schema.Types.ObjectId, ref: 'PublishingPlatform' }],
    publishingStrategy: { 
      type: String, 
      enum: ['simultaneous', 'sequential', 'staggered'], 
      default: 'sequential' 
    },
    platformDelay: { type: Number, default: 3600000 }, // 1 hour in ms
    platformPriority: [{
      platformId: { type: mongoose.Schema.Types.ObjectId, ref: 'PublishingPlatform' },
      priority: Number
    }]
  },
  
  // 发布调度
  scheduling: {
    frequency: { 
      type: String, 
      enum: ['once', 'daily', 'weekly', 'bi_weekly', 'monthly', 'custom'], 
      default: 'once' 
    },
    customSchedule: [{
      dayOfWeek: Number, // 0-6
      time: String, // "HH:MM"
      timezone: String
    }],
    startDate: Date,
    endDate: Date,
    nextPublishTime: Date,
    publishImmediately: { type: Boolean, default: false }
  },
  
  // 内容适配设置
  contentAdaptation: {
    enabled: { type: Boolean, default: true },
    platformSpecific: [{
      platformId: { type: mongoose.Schema.Types.ObjectId, ref: 'PublishingPlatform' },
      titlePrefix: String,
      titleSuffix: String,
      contentPrefix: String,
      contentSuffix: String,
      tagsMapping: [{
        originalTag: String,
        platformTag: String
      }]
    }],
    seoOptimization: { type: Boolean, default: true },
    imageOptimization: { type: Boolean, default: true }
  },
  
  // 自动化设置
  automation: {
    autoSeo: { type: Boolean, default: true },
    autoTags: { type: Boolean, default: true },
    autoImages: { type: Boolean, default: false },
    autoPromotion: {
      enabled: { type: Boolean, default: false },
      socialPlatforms: [String],
      promotionDelay: { type: Number, default: 1800000 } // 30 minutes
    },
    contentSyncing: {
      enabled: { type: Boolean, default: false },
      syncUpdates: { type: Boolean, default: true },
      syncDeletions: { type: Boolean, default: false }
    }
  },
  
  // 内容审核
  moderation: {
    requireApproval: { type: Boolean, default: false },
    plagiarismCheck: { type: Boolean, default: true },
    grammarCheck: { type: Boolean, default: true },
    brandGuidelines: {
      enabled: { type: Boolean, default: false },
      keywords: [String],
      bannedWords: [String],
      toneGuidelines: String
    }
  },
  
  // 统计数据
  stats: {
    totalArticles: { type: Number, default: 0 },
    publishedArticles: { type: Number, default: 0 },
    failedPublications: { type: Number, default: 0 },
    totalViews: { type: Number, default: 0 },
    totalShares: { type: Number, default: 0 },
    totalComments: { type: Number, default: 0 },
    avgEngagement: { type: Number, default: 0 },
    
    // 平台表现
    platformPerformance: [{
      platformId: { type: mongoose.Schema.Types.ObjectId, ref: 'PublishingPlatform' },
      articles: Number,
      views: Number,
      shares: Number,
      comments: Number,
      avgRating: Number
    }]
  },
  
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 发布文章记录 Schema
const publishedArticleSchema = new mongoose.Schema({
  campaignId: { type: mongoose.Schema.Types.ObjectId, ref: 'ContentCampaign' },
  templateId: { type: mongoose.Schema.Types.ObjectId, ref: 'ArticleTemplate' },
  platformId: { type: mongoose.Schema.Types.ObjectId, ref: 'PublishingPlatform', required: true },
  
  // 文章内容
  content: {
    title: { type: String, required: true },
    subtitle: String,
    body: { type: String, required: true },
    excerpt: String,
    tags: [String],
    category: String,
    featuredImage: {
      url: String,
      alt: String,
      caption: String
    },
    images: [{
      url: String,
      alt: String,
      caption: String,
      position: String
    }]
  },
  
  // SEO 信息
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: String,
    canonicalUrl: String
  },
  
  // 发布状态
  publishing: {
    status: { 
      type: String, 
      enum: ['draft', 'queued', 'publishing', 'published', 'failed', 'updated', 'deleted'], 
      default: 'draft' 
    },
    scheduledFor: Date,
    publishedAt: Date,
    platformPostId: String,
    platformUrl: String,
    platformStatus: String, // 平台特定状态
    errorMessage: String,
    retryCount: { type: Number, default: 0 },
    maxRetries: { type: Number, default: 3 }
  },
  
  // 性能指标
  metrics: {
    views: { type: Number, default: 0 },
    uniqueViews: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    bookmarks: { type: Number, default: 0 },
    readTime: Number, // 平均阅读时间（秒）
    bounceRate: Number,
    engagementRate: { type: Number, default: 0 },
    
    // 详细分析
    trafficSources: [{
      source: String,
      visits: Number,
      percentage: Number
    }],
    
    lastUpdated: { type: Date, default: Date.now }
  },
  
  // 用户互动
  interactions: [{
    type: { type: String, enum: ['comment', 'like', 'share', 'bookmark', 'mention'] },
    userId: String,
    username: String,
    content: String,
    timestamp: { type: Date, default: Date.now },
    platform: String
  }],
  
  // 版本管理
  versions: [{
    version: Number,
    title: String,
    content: String,
    updatedAt: Date,
    updateReason: String
  }],
  
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 方法：重置使用统计
publishingPlatformSchema.methods.resetUsageCounters = function() {
  const now = new Date();
  
  if (now.toDateString() !== this.usage.lastResetDate.toDateString()) {
    this.usage.postsToday = 0;
    this.usage.lastResetDate = now;
  }
  
  const currentMonth = now.getMonth();
  const lastResetMonth = this.usage.lastResetDate.getMonth();
  
  if (currentMonth !== lastResetMonth) {
    this.usage.postsThisMonth = 0;
  }
};

// 方法：检查是否可以发布
publishingPlatformSchema.methods.canPublish = function() {
  this.resetUsageCounters();
  
  if (this.status !== 'active') {
    return { canPublish: false, reason: '平台未激活' };
  }
  
  if (this.usage.postsToday >= this.rateLimits.postsPerDay) {
    return { canPublish: false, reason: '已达到每日发布限制' };
  }
  
  // 检查发布间隔
  if (this.usage.lastPostTime) {
    const timeSinceLastPost = Date.now() - this.usage.lastPostTime.getTime();
    const minInterval = (60 / this.rateLimits.postsPerHour) * 60 * 1000; // 计算最小间隔
    
    if (timeSinceLastPost < minInterval) {
      return { canPublish: false, reason: '发布间隔太短' };
    }
  }
  
  return { canPublish: true };
};

// 方法：计算活动表现
contentCampaignSchema.methods.calculatePerformance = function() {
  if (this.stats.publishedArticles > 0) {
    const totalEngagement = this.stats.totalShares + this.stats.totalComments;
    this.stats.avgEngagement = (totalEngagement / this.stats.publishedArticles).toFixed(2);
  }
};

// 更新时间戳
publishingPlatformSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

articleTemplateSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

contentCampaignSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  this.calculatePerformance();
  next();
});

publishedArticleSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// 索引
publishingPlatformSchema.index({ platform: 1, status: 1 });
publishedArticleSchema.index({ campaignId: 1, 'publishing.status': 1 });
publishedArticleSchema.index({ platformId: 1, 'publishing.scheduledFor': 1 });

// 避免重复编译模型
const PublishingPlatform = mongoose.models.PublishingPlatform || mongoose.model('PublishingPlatform', publishingPlatformSchema);
const ArticleTemplate = mongoose.models.ArticleTemplate || mongoose.model('ArticleTemplate', articleTemplateSchema);
const ContentCampaign = mongoose.models.ContentCampaign || mongoose.model('ContentCampaign', contentCampaignSchema);
const PublishedArticle = mongoose.models.PublishedArticle || mongoose.model('PublishedArticle', publishedArticleSchema);

module.exports = {
  PublishingPlatform,
  ArticleTemplate,
  ContentCampaign,
  PublishedArticle
};