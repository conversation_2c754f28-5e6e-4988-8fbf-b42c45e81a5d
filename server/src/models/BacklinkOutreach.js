const mongoose = require('mongoose');

// 外链机会网站信息
const backlinkProspectSchema = new mongoose.Schema({
  url: {
    type: String,
    required: true,
    unique: true
  },
  domain: {
    type: String,
    required: true
  },
  title: String,
  description: String,
  contactEmail: String,
  contactName: String,
  contactTitle: String,
  socialMedia: {
    twitter: String,
    linkedin: String,
    facebook: String
  },
  metrics: {
    domainAuthority: Number,
    pageAuthority: Number,
    backlinks: Number,
    organicTraffic: Number,
    alexa: Number,
    mozRank: Number
  },
  categories: [String],
  tags: [String],
  language: {
    type: String,
    default: 'en'
  },
  country: String,
  discovered: {
    method: {
      type: String,
      enum: ['manual', 'competitor_analysis', 'keyword_research', 'tool_import', 'referral'],
      default: 'manual'
    },
    source: String,
    date: {
      type: Date,
      default: Date.now
    }
  },
  status: {
    type: String,
    enum: ['discovered', 'researched', 'contacted', 'replied', 'agreed', 'published', 'rejected', 'unresponsive'],
    default: 'discovered'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  notes: String,
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

const backlinkOutreachSchema = new mongoose.Schema({
  targetWebsite: {
    url: String,
    domain: String,
    title: String,
    description: String,
    metrics: {
      domainAuthority: Number,
      pageAuthority: Number,
      organicTraffic: Number,
      backlinks: Number,
      referring_domains: Number
    },
    category: String, // productivity, tech, education, etc.
    language: String,
    country: String
  },
  
  contactInfo: {
    name: String,
    email: String,
    position: String,
    socialProfiles: {
      twitter: String,
      linkedin: String
    },
    verified: {
      type: Boolean,
      default: false
    }
  },
  
  outreachCampaign: {
    status: {
      type: String,
      enum: ['identified', 'researched', 'contacted', 'follow_up_1', 'follow_up_2', 'negotiating', 'accepted', 'rejected', 'published', 'declined'],
      default: 'identified'
    },
    type: {
      type: String,
      enum: ['guest_post', 'resource_page', 'broken_link', 'mention', 'review', 'directory'],
      required: true
    },
    priority: {
      type: String,
      enum: ['high', 'medium', 'low'],
      default: 'medium'
    }
  },
  
  communications: [{
    type: {
      type: String,
      enum: ['initial_email', 'follow_up', 'response', 'negotiation', 'final']
    },
    subject: String,
    message: String,
    sentAt: Date,
    opened: {
      type: Boolean,
      default: false
    },
    replied: {
      type: Boolean,
      default: false
    },
    response: String,
    responseAt: Date,
    templateUsed: String
  }],
  
  proposal: {
    content: {
      title: String,
      outline: String,
      wordCount: Number,
      topics: [String]
    },
    anchorText: String,
    linkUrl: String,
    compensation: {
      type: {
        type: String,
        enum: ['free', 'paid', 'reciprocal', 'product']
      },
      amount: Number,
      currency: String,
      description: String
    }
  },
  
  result: {
    accepted: Boolean,
    publishedUrl: String,
    publishedAt: Date,
    anchorText: String,
    linkType: {
      type: String,
      enum: ['dofollow', 'nofollow', 'sponsored']
    },
    linkPosition: String, // header, content, footer, sidebar
    traffic: {
      clicks: { type: Number, default: 0 },
      conversions: { type: Number, default: 0 }
    },
    value: Number // Estimated value of the backlink
  },
  
  research: {
    contentGaps: [String], // What content they're missing
    competitorBacklinks: [{
      competitor: String,
      url: String,
      anchorText: String
    }],
    writingStyle: String,
    contentPreferences: String,
    guidelines: String
  },
  
  automation: {
    followUpScheduled: Date,
    reminderSet: Boolean,
    autoFollowUp: {
      type: Boolean,
      default: true
    },
    maxFollowUps: {
      type: Number,
      default: 2
    }
  },
  
  notes: String,
  tags: [String],
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes
backlinkOutreachSchema.index({ 'targetWebsite.domain': 1 });
backlinkOutreachSchema.index({ 'outreachCampaign.status': 1 });
backlinkOutreachSchema.index({ 'outreachCampaign.type': 1 });
backlinkOutreachSchema.index({ 'outreachCampaign.priority': 1 });

// Methods
backlinkOutreachSchema.methods.calculatePriority = function() {
  let score = 0;
  
  // Domain Authority weight
  if (this.targetWebsite.metrics.domainAuthority) {
    score += this.targetWebsite.metrics.domainAuthority;
  }
  
  // Traffic weight
  if (this.targetWebsite.metrics.organicTraffic) {
    score += Math.min(this.targetWebsite.metrics.organicTraffic / 1000, 50);
  }
  
  // Relevance (category match)
  const relevantCategories = ['productivity', 'tech', 'education', 'business'];
  if (relevantCategories.includes(this.targetWebsite.category)) {
    score += 20;
  }
  
  // Set priority based on score
  if (score >= 80) {
    this.outreachCampaign.priority = 'high';
  } else if (score >= 50) {
    this.outreachCampaign.priority = 'medium';
  } else {
    this.outreachCampaign.priority = 'low';
  }
  
  return score;
};

backlinkOutreachSchema.methods.scheduleFollowUp = function(days = 7) {
  const followUpDate = new Date();
  followUpDate.setDate(followUpDate.getDate() + days);
  this.automation.followUpScheduled = followUpDate;
  this.automation.reminderSet = true;
};

// 外联活动
const outreachCampaignSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  description: String,
  targetKeywords: [String],
  linkTarget: {
    url: {
      type: String,
      required: true
    },
    anchorText: String,
    content: String
  },
  emailTemplate: {
    subject: String,
    body: String,
    followUpSubject: String,
    followUpBody: String
  },
  prospects: [{
    prospect: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BacklinkProspect'
    },
    personalizedNote: String,
    customEmail: String,
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  schedule: {
    startDate: Date,
    endDate: Date,
    followUpDelay: {
      type: Number,
      default: 7 // days
    },
    maxFollowUps: {
      type: Number,
      default: 2
    }
  },
  stats: {
    totalSent: {
      type: Number,
      default: 0
    },
    opened: {
      type: Number,
      default: 0
    },
    replied: {
      type: Number,
      default: 0
    },
    linksReceived: {
      type: Number,
      default: 0
    }
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'completed'],
    default: 'draft'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// 外联邮件记录
const outreachEmailSchema = new mongoose.Schema({
  campaign: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'OutreachCampaign',
    required: true
  },
  prospect: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BacklinkProspect',
    required: true
  },
  emailType: {
    type: String,
    enum: ['initial', 'follow_up_1', 'follow_up_2', 'custom'],
    default: 'initial'
  },
  to: {
    type: String,
    required: true
  },
  subject: {
    type: String,
    required: true
  },
  body: {
    type: String,
    required: true
  },
  sentAt: Date,
  openedAt: Date,
  clickedAt: Date,
  repliedAt: Date,
  replyContent: String,
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'sent', 'delivered', 'opened', 'clicked', 'replied', 'bounced', 'failed'],
    default: 'draft'
  },
  trackingId: String,
  externalId: String, // For email service provider ID
  scheduledFor: Date,
  errorMessage: String
}, {
  timestamps: true
});

// 外链建设结果
const backlinkResultSchema = new mongoose.Schema({
  campaign: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'OutreachCampaign'
  },
  prospect: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BacklinkProspect',
    required: true
  },
  sourceUrl: {
    type: String,
    required: true
  },
  targetUrl: {
    type: String,
    required: true
  },
  anchorText: String,
  linkType: {
    type: String,
    enum: ['dofollow', 'nofollow', 'sponsored', 'ugc'],
    default: 'dofollow'
  },
  placement: {
    type: String,
    enum: ['content', 'sidebar', 'footer', 'navigation', 'author_bio', 'resource_page'],
    default: 'content'
  },
  discoveredAt: {
    type: Date,
    default: Date.now
  },
  verifiedAt: Date,
  status: {
    type: String,
    enum: ['pending', 'live', 'removed', 'changed', 'broken'],
    default: 'pending'
  },
  value: {
    estimated: Number, // Estimated SEO value
    actual: Number     // Actual traffic/conversion value
  },
  notes: String
}, {
  timestamps: true
});

// 创建模型，避免重复编译
const BacklinkProspect = mongoose.models.BacklinkProspect || mongoose.model('BacklinkProspect', backlinkProspectSchema);
const OutreachCampaign = mongoose.models.OutreachCampaign || mongoose.model('OutreachCampaign', outreachCampaignSchema);
const OutreachEmail = mongoose.models.OutreachEmail || mongoose.model('OutreachEmail', outreachEmailSchema);
const BacklinkResult = mongoose.models.BacklinkResult || mongoose.model('BacklinkResult', backlinkResultSchema);
const BacklinkOutreach = mongoose.models.BacklinkOutreach || mongoose.model('BacklinkOutreach', backlinkOutreachSchema);

module.exports = {
  BacklinkProspect,
  OutreachCampaign,
  OutreachEmail,
  BacklinkResult,
  BacklinkOutreach
};