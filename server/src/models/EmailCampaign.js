const mongoose = require('mongoose');

// 邮件模板 Schema
const emailTemplateSchema = new mongoose.Schema({
  name: { type: String, required: true },
  subject: { type: String, required: true },
  htmlContent: { type: String, required: true },
  textContent: { type: String },
  variables: [{
    key: String,
    description: String,
    defaultValue: String
  }],
  type: { 
    type: String, 
    enum: ['welcome', 'nurture', 'promotion', 'reactivation', 'educational'], 
    required: true 
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 邮件序列 Schema
const emailSequenceSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  trigger: {
    type: { type: String, enum: ['signup', 'purchase', 'abandon', 'inactive', 'manual'], required: true },
    conditions: {
      waitDays: Number,
      segmentRules: [{
        field: String,
        operator: String,
        value: mongoose.Schema.Types.Mixed
      }]
    }
  },
  emails: [{
    templateId: { type: mongoose.Schema.Types.ObjectId, ref: 'EmailTemplate' },
    delayDays: { type: Number, default: 0 },
    delayHours: { type: Number, default: 0 },
    conditions: [{
      field: String,
      operator: String,
      value: mongoose.Schema.Types.Mixed
    }]
  }],
  active: { type: Boolean, default: true },
  stats: {
    enrolled: { type: Number, default: 0 },
    completed: { type: Number, default: 0 },
    avgOpenRate: { type: Number, default: 0 },
    avgClickRate: { type: Number, default: 0 }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 邮件用户群体分段 Schema
const emailSegmentSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  filters: [{
    field: String,
    operator: String,
    value: mongoose.Schema.Types.Mixed
  }],
  emailList: { type: mongoose.Schema.Types.ObjectId, ref: 'EmailList' },
  leads: [{ type: mongoose.Schema.Types.ObjectId, ref: 'EmailLead' }],
  totalLeads: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: Date.now },
  createdAt: { type: Date, default: Date.now }
});

const emailCampaignSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  description: { type: String, trim: true },
  type: { 
    type: String, 
    enum: ['one-time', 'sequence', 'drip', 'newsletter', 'automation'], 
    required: true,
    default: 'one-time'
  },
  
  // 模板和内容
  templateId: { type: mongoose.Schema.Types.ObjectId, ref: 'EmailTemplate' },
  subject: { type: String, required: true, trim: true },
  textContent: { type: String, required: true },
  htmlContent: { type: String, required: true },
  
  // 发送设置
  fromName: { type: String, required: true, default: 'AI-Pomo Team' },
  fromEmail: { type: String, required: true, default: '<EMAIL>' },
  replyToEmail: { type: String, default: '<EMAIL>' },
  
  // 自动化序列配置
  sequenceConfig: {
    sequenceId: { type: mongoose.Schema.Types.ObjectId, ref: 'EmailSequence' },
    triggerEvent: { type: String, enum: ['signup', 'purchase', 'abandon', 'inactive'] },
    delaySettings: {
      days: { type: Number, default: 0 },
      hours: { type: Number, default: 0 },
      minutes: { type: Number, default: 0 }
    }
  },
  
  // 目标受众
  targeting: {
    emailLists: [{ type: mongoose.Schema.Types.ObjectId, ref: 'EmailList', required: true }],
    segments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'EmailSegment' }],
    tags: [{ type: String, trim: true }],
    excludeTags: [{ type: String, trim: true }],
    filters: [{
      field: String,
      operator: String,
      value: mongoose.Schema.Types.Mixed
    }]
  },
  
  // 发送时间设置
  scheduling: {
    status: {
      type: String,
      enum: ['draft', 'scheduled', 'sending', 'sent', 'paused', 'cancelled'],
      default: 'draft'
    },
    scheduledAt: Date,
    sentAt: Date,
    timezone: { type: String, default: 'Asia/Shanghai' },
    sendTime: {
      hour: Number,
      minute: Number
    },
    daysOfWeek: [Number], // 0-6, Sunday to Saturday
    batchSettings: {
      batchSize: { type: Number, default: 100, min: 1, max: 1000 },
      delayBetweenBatches: { type: Number, default: 60000, min: 10000 },
      emailsPerHour: { type: Number, default: 500 },
      emailsPerDay: { type: Number, default: 5000 }
    }
  },
  
  // 个性化
  personalization: {
    enabled: { type: Boolean, default: true },
    mergeFields: [{
      placeholder: String,
      defaultValue: String
    }],
    dynamicContent: [{
      placeholder: String,
      rules: [{
        condition: String,
        content: String
      }]
    }]
  },
  
  // A/B 测试
  abTest: {
    enabled: { type: Boolean, default: false },
    testType: { type: String, enum: ['subject', 'content', 'sendTime', 'fromName'] },
    variants: [{
      name: String,
      subject: String,
      content: String,
      percentage: Number,
      metrics: {
        sent: { type: Number, default: 0 },
        opened: { type: Number, default: 0 },
        clicked: { type: Number, default: 0 },
        converted: { type: Number, default: 0 }
      }
    }],
    testDuration: { type: Number, default: 24 }, // hours
    winnerCriteria: { type: String, enum: ['open_rate', 'click_rate', 'conversion_rate'] },
    winner: String
  },
  
  // 统计数据
  stats: {
    totalRecipients: { type: Number, default: 0 },
    emailsSent: { type: Number, default: 0 },
    emailsDelivered: { type: Number, default: 0 },
    emailsBounced: { type: Number, default: 0 },
    emailsOpened: { type: Number, default: 0 },
    emailsClicked: { type: Number, default: 0 },
    emailsUnsubscribed: { type: Number, default: 0 },
    emailsComplained: { type: Number, default: 0 },
    conversions: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 }
  },
  
  // 发送进度
  sendingProgress: {
    current: { type: Number, default: 0 },
    total: { type: Number, default: 0 },
    percentage: { type: Number, default: 0 },
    currentBatch: { type: Number, default: 0 },
    totalBatches: { type: Number, default: 0 },
    lastSentAt: Date,
    nextBatchAt: Date,
    estimatedCompletion: Date
  },
  
  // 错误日志
  errors: [{
    email: String,
    errorType: String,
    errorMessage: String,
    timestamp: { type: Date, default: Date.now }
  }],
  
  // 发送日志
  sendLog: [{
    leadId: { type: mongoose.Schema.Types.ObjectId, ref: 'EmailLead' },
    email: String,
    status: { type: String, enum: ['queued', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained', 'unsubscribed'] },
    sentAt: Date,
    deliveredAt: Date,
    openedAt: Date,
    clickedAt: Date,
    variant: String, // for A/B testing
    messageId: String,
    errorMessage: String
  }],
  
  // 系统字段
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 营销活动方法
emailCampaignSchema.methods.calculateStats = function() {
  if (this.stats.emailsDelivered > 0) {
    this.stats.openRate = ((this.stats.emailsOpened / this.stats.emailsDelivered) * 100).toFixed(2);
    this.stats.clickRate = ((this.stats.emailsClicked / this.stats.emailsDelivered) * 100).toFixed(2);
    this.stats.unsubscribeRate = ((this.stats.emailsUnsubscribed / this.stats.emailsDelivered) * 100).toFixed(2);
    this.stats.conversionRate = ((this.stats.conversions / this.stats.emailsDelivered) * 100).toFixed(2);
  }
  
  if (this.sendingProgress.total > 0) {
    this.sendingProgress.percentage = Math.round(
      (this.sendingProgress.current / this.sendingProgress.total) * 100
    );
  }
};

emailCampaignSchema.methods.canSend = function() {
  const now = new Date();
  
  if (this.scheduling.status !== 'scheduled') {
    return { canSend: false, reason: '活动未处于已计划状态' };
  }
  
  if (this.scheduling.scheduledAt && this.scheduling.scheduledAt > now) {
    return { canSend: false, reason: '尚未到达发送时间' };
  }
  
  if (this.targeting.emailLists.length === 0) {
    return { canSend: false, reason: '未设置目标邮件列表' };
  }
  
  return { canSend: true };
};

// 更新时间戳
emailCampaignSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  this.calculateStats();
  next();
});

// 索引
emailCampaignSchema.index({ name: 1 });
emailCampaignSchema.index({ 'scheduling.status': 1 });
emailCampaignSchema.index({ createdBy: 1 });
emailCampaignSchema.index({ 'targeting.emailLists': 1 });
emailCampaignSchema.index({ 'scheduling.scheduledAt': 1 });
emailCampaignSchema.index({ createdAt: -1 });

// Virtual 计算属性
emailCampaignSchema.virtual('openRate').get(function() {
  if (this.stats.emailsDelivered === 0) return 0;
  return ((this.stats.emailsOpened / this.stats.emailsDelivered) * 100).toFixed(2);
});

emailCampaignSchema.virtual('clickRate').get(function() {
  if (this.stats.emailsDelivered === 0) return 0;
  return ((this.stats.emailsClicked / this.stats.emailsDelivered) * 100).toFixed(2);
});

emailCampaignSchema.virtual('conversionRate').get(function() {
  if (this.stats.emailsDelivered === 0) return 0;
  return ((this.stats.conversions / this.stats.emailsDelivered) * 100).toFixed(2);
});

// 避免重复编译模型
const EmailTemplate = mongoose.models.EmailTemplate || mongoose.model('EmailTemplate', emailTemplateSchema);
const EmailSequence = mongoose.models.EmailSequence || mongoose.model('EmailSequence', emailSequenceSchema);
const EmailSegment = mongoose.models.EmailSegment || mongoose.model('EmailSegment', emailSegmentSchema);
const EmailCampaign = mongoose.models.EmailCampaign || mongoose.model('EmailCampaign', emailCampaignSchema);

module.exports = { EmailTemplate, EmailSequence, EmailSegment, EmailCampaign };