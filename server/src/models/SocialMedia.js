const mongoose = require('mongoose');

// 社交媒体账户 Schema
const socialMediaAccountSchema = new mongoose.Schema({
  platform: { 
    type: String, 
    enum: ['twitter', 'linkedin', 'facebook', 'instagram', 'tiktok', 'weibo', 'x<PERSON><PERSON><PERSON><PERSON>', 'douyin'], 
    required: true 
  },
  accountName: { type: String, required: true },
  displayName: { type: String, required: true },
  
  // API 凭据
  credentials: {
    accessToken: String,
    refreshToken: String,
    apiKey: String,
    apiSecret: String,
    bearerToken: String,
    userId: String,
    pageId: String, // Facebook/Instagram pages
    businessAccountId: String
  },
  
  // 账户信息
  profile: {
    bio: String,
    website: String,
    location: String,
    avatarUrl: String,
    bannerUrl: String,
    followers: { type: Number, default: 0 },
    following: { type: Number, default: 0 },
    posts: { type: Number, default: 0 },
    verified: { type: Boolean, default: false },
    businessAccount: { type: Boolean, default: false }
  },
  
  // 发布限制
  limits: {
    postsPerDay: { type: Number, default: 10 },
    postsPerHour: { type: Number, default: 2 },
    maxHashtags: { type: Number, default: 30 },
    maxMentions: { type: Number, default: 10 },
    maxCharacters: { type: Number, default: 280 },
    allowImages: { type: Boolean, default: true },
    allowVideos: { type: Boolean, default: true },
    allowLinks: { type: Boolean, default: true }
  },
  
  // 使用统计
  usage: {
    postsToday: { type: Number, default: 0 },
    postsThisHour: { type: Number, default: 0 },
    lastPostTime: Date,
    lastResetDate: { type: Date, default: Date.now },
    lastResetHour: { type: Number, default: new Date().getHours() }
  },
  
  status: { 
    type: String, 
    enum: ['active', 'suspended', 'rate_limited', 'expired', 'error'], 
    default: 'active' 
  },
  lastActivity: { type: Date, default: Date.now },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 社交媒体内容模板 Schema
const socialContentTemplateSchema = new mongoose.Schema({
  name: { type: String, required: true },
  platform: { 
    type: String, 
    enum: ['twitter', 'linkedin', 'facebook', 'instagram', 'tiktok', 'weibo', 'xiaohongshu', 'douyin', 'all'], 
    required: true 
  },
  type: { 
    type: String, 
    enum: ['promotional', 'educational', 'entertaining', 'news', 'engagement', 'behind_scenes'], 
    required: true 
  },
  
  content: {
    text: { type: String, required: true },
    hashtags: [String],
    mentions: [String],
    emojis: [String],
    callToAction: String,
    
    // 多媒体内容
    media: [{
      type: { type: String, enum: ['image', 'video', 'gif', 'carousel'] },
      url: String,
      localPath: String,
      caption: String,
      altText: String,
      duration: Number // 视频时长（秒）
    }],
    
    // 链接
    links: [{
      url: String,
      title: String,
      description: String,
      previewImage: String
    }]
  },
  
  // 个性化变量
  variables: [{
    name: String,
    placeholder: String,
    type: { type: String, enum: ['text', 'number', 'date', 'url'] },
    required: { type: Boolean, default: false },
    defaultValue: String,
    options: [String] // 预定义选项
  }],
  
  // 发布设置
  settings: {
    autoHashtags: { type: Boolean, default: true },
    trending: { type: Boolean, default: false },
    geotagging: {
      enabled: { type: Boolean, default: false },
      location: String,
      coordinates: {
        lat: Number,
        lng: Number
      }
    },
    crossPost: { type: Boolean, default: false },
    crossPostPlatforms: [String]
  },
  
  // 性能统计
  performance: {
    used: { type: Number, default: 0 },
    avgLikes: { type: Number, default: 0 },
    avgShares: { type: Number, default: 0 },
    avgComments: { type: Number, default: 0 },
    avgReach: { type: Number, default: 0 },
    avgEngagement: { type: Number, default: 0 },
    bestPerformingPost: {
      postId: String,
      metrics: {
        likes: Number,
        shares: Number,
        comments: Number,
        reach: Number
      }
    }
  },
  
  active: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 社交媒体发布活动 Schema
const socialMediaCampaignSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['draft', 'scheduled', 'active', 'paused', 'completed'], 
    default: 'draft' 
  },
  
  // 活动类型
  type: { 
    type: String, 
    enum: ['single_post', 'series', 'continuous', 'event_based'], 
    required: true 
  },
  
  // 内容配置
  contentConfig: {
    templates: [{ type: mongoose.Schema.Types.ObjectId, ref: 'SocialContentTemplate' }],
    rotateTemplates: { type: Boolean, default: true },
    customContent: [{
      text: String,
      hashtags: [String],
      media: [{
        type: String,
        url: String,
        caption: String
      }],
      scheduledFor: Date
    }]
  },
  
  // 账户配置
  accountConfig: {
    accounts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'SocialMediaAccount' }],
    rotateAccounts: { type: Boolean, default: false },
    crossPost: { type: Boolean, default: false },
    postDelay: { type: Number, default: 300000 } // 5分钟延迟
  },
  
  // 发布调度
  scheduling: {
    timezone: { type: String, default: 'Asia/Shanghai' },
    frequency: { 
      type: String, 
      enum: ['once', 'daily', 'twice_daily', 'weekly', 'custom'], 
      default: 'daily' 
    },
    customFrequency: {
      interval: Number, // 间隔时间（分钟）
      times: [String] // 具体时间点 ['09:00', '15:00', '21:00']
    },
    daysOfWeek: [Number], // 0=Sunday, 6=Saturday
    startDate: Date,
    endDate: Date,
    nextPostTime: Date,
    
    // 最佳发布时间优化\n    optimizeTiming: { type: Boolean, default: true },\n    bestTimes: [{\n      dayOfWeek: Number,\n      hour: Number,\n      score: Number // 根据历史数据计算的效果评分\n    }]\n  },\n  \n  // 内容个性化\n  personalization: {\n    enabled: { type: Boolean, default: true },\n    audienceSegments: [{\n      name: String,\n      criteria: {\n        location: [String],\n        interests: [String],\n        demographics: {\n          ageRange: String,\n          gender: String\n        }\n      },\n      customContent: String\n    }],\n    dynamicHashtags: { type: Boolean, default: true },\n    trendingTopics: { type: Boolean, default: true }\n  },\n  \n  // 自动化设置\n  automation: {\n    autoReply: { type: Boolean, default: false },\n    replyTemplates: [{\n      trigger: String, // 关键词或条件\n      response: String,\n      delay: Number // 延迟时间（分钟）\n    }],\n    autoLike: { type: Boolean, default: false },\n    autoFollow: { type: Boolean, default: false },\n    engagementBoost: {\n      enabled: { type: Boolean, default: false },\n      likesAfterPost: Number,\n      commentsAfterPost: Number,\n      sharesAfterPost: Number\n    }\n  },\n  \n  // 内容审核\n  moderation: {\n    requireApproval: { type: Boolean, default: false },\n    bannedWords: [String],\n    sentimentCheck: { type: Boolean, default: true },\n    brandSafetyCheck: { type: Boolean, default: true }\n  },\n  \n  // 统计数据\n  stats: {\n    totalPosts: { type: Number, default: 0 },\n    successfulPosts: { type: Number, default: 0 },\n    failedPosts: { type: Number, default: 0 },\n    totalLikes: { type: Number, default: 0 },\n    totalShares: { type: Number, default: 0 },\n    totalComments: { type: Number, default: 0 },\n    totalReach: { type: Number, default: 0 },\n    totalImpressions: { type: Number, default: 0 },\n    avgEngagementRate: { type: Number, default: 0 },\n    \n    // 平台分布\n    platformStats: [{\n      platform: String,\n      posts: Number,\n      likes: Number,\n      shares: Number,\n      comments: Number,\n      reach: Number\n    }]\n  },\n  \n  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },\n  createdAt: { type: Date, default: Date.now },\n  updatedAt: { type: Date, default: Date.now }\n});\n\n// 社交媒体发布记录 Schema\nconst socialMediaPostSchema = new mongoose.Schema({\n  campaignId: { type: mongoose.Schema.Types.ObjectId, ref: 'SocialMediaCampaign' },\n  templateId: { type: mongoose.Schema.Types.ObjectId, ref: 'SocialContentTemplate' },\n  accountId: { type: mongoose.Schema.Types.ObjectId, ref: 'SocialMediaAccount', required: true },\n  \n  platform: { \n    type: String, \n    enum: ['twitter', 'linkedin', 'facebook', 'instagram', 'tiktok', 'weibo', 'xiaohongshu', 'douyin'], \n    required: true \n  },\n  \n  content: {\n    text: { type: String, required: true },\n    hashtags: [String],\n    mentions: [String],\n    media: [{\n      type: String,\n      url: String,\n      platformMediaId: String,\n      caption: String\n    }],\n    links: [{\n      url: String,\n      title: String,\n      description: String\n    }]\n  },\n  \n  // 发布状态\n  publishing: {\n    status: { \n      type: String, \n      enum: ['draft', 'queued', 'publishing', 'published', 'failed', 'deleted'], \n      default: 'draft' \n    },\n    scheduledFor: Date,\n    publishedAt: Date,\n    platformPostId: String,\n    platformUrl: String,\n    errorMessage: String,\n    retryCount: { type: Number, default: 0 },\n    maxRetries: { type: Number, default: 3 }\n  },\n  \n  // 性能指标\n  metrics: {\n    likes: { type: Number, default: 0 },\n    shares: { type: Number, default: 0 },\n    comments: { type: Number, default: 0 },\n    saves: { type: Number, default: 0 },\n    reach: { type: Number, default: 0 },\n    impressions: { type: Number, default: 0 },\n    clicks: { type: Number, default: 0 },\n    engagementRate: { type: Number, default: 0 },\n    \n    // 详细互动数据\n    interactions: [{\n      type: { type: String, enum: ['like', 'share', 'comment', 'save', 'click'] },\n      userId: String,\n      username: String,\n      timestamp: { type: Date, default: Date.now },\n      content: String // 评论内容\n    }],\n    \n    lastUpdated: { type: Date, default: Date.now }\n  },\n  \n  // A/B 测试数据\n  abTest: {\n    variant: String,\n    testGroup: String\n  },\n  \n  createdAt: { type: Date, default: Date.now },\n  updatedAt: { type: Date, default: Date.now }\n});\n\n// 方法：重置使用统计\nsocialMediaAccountSchema.methods.resetUsageCounters = function() {\n  const now = new Date();\n  const currentHour = now.getHours();\n  \n  // 重置每日计数\n  if (now.toDateString() !== this.usage.lastResetDate.toDateString()) {\n    this.usage.postsToday = 0;\n    this.usage.lastResetDate = now;\n  }\n  \n  // 重置每小时计数\n  if (currentHour !== this.usage.lastResetHour) {\n    this.usage.postsThisHour = 0;\n    this.usage.lastResetHour = currentHour;\n  }\n};\n\n// 方法：检查是否可以发布\nsocialMediaAccountSchema.methods.canPost = function() {\n  this.resetUsageCounters();\n  \n  if (this.status !== 'active') {\n    return { canPost: false, reason: '账户未激活' };\n  }\n  \n  if (this.usage.postsToday >= this.limits.postsPerDay) {\n    return { canPost: false, reason: '已达到每日发布限制' };\n  }\n  \n  if (this.usage.postsThisHour >= this.limits.postsPerHour) {\n    return { canPost: false, reason: '已达到每小时发布限制' };\n  }\n  \n  // 检查发布间隔（避免过于频繁）\n  if (this.usage.lastPostTime) {\n    const timeSinceLastPost = Date.now() - this.usage.lastPostTime.getTime();\n    const minInterval = 60000; // 最少1分钟间隔\n    \n    if (timeSinceLastPost < minInterval) {\n      return { canPost: false, reason: '发布间隔太短' };\n    }\n  }\n  \n  return { canPost: true };\n};\n\n// 方法：计算活动表现\nsocialMediaCampaignSchema.methods.calculatePerformance = function() {\n  if (this.stats.totalPosts > 0) {\n    const totalEngagement = this.stats.totalLikes + this.stats.totalShares + this.stats.totalComments;\n    this.stats.avgEngagementRate = ((totalEngagement / this.stats.totalReach) * 100).toFixed(2);\n  }\n};\n\n// 更新时间戳\nsocialMediaAccountSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\nsocialContentTemplateSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\nsocialMediaCampaignSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  this.calculatePerformance();\n  next();\n});\n\nsocialMediaPostSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\n// 索引\nsocialMediaAccountSchema.index({ platform: 1, accountName: 1 }, { unique: true });\nsocialContentTemplateSchema.index({ platform: 1, type: 1 });\nsocialMediaPostSchema.index({ campaignId: 1, 'publishing.status': 1 });\nsocialMediaPostSchema.index({ platform: 1, 'publishing.scheduledFor': 1 });\n\nconst SocialMediaAccount = mongoose.model('SocialMediaAccount', socialMediaAccountSchema);\nconst SocialContentTemplate = mongoose.model('SocialContentTemplate', socialContentTemplateSchema);\nconst SocialMediaCampaign = mongoose.model('SocialMediaCampaign', socialMediaCampaignSchema);\nconst SocialMediaPost = mongoose.model('SocialMediaPost', socialMediaPostSchema);\n\nmodule.exports = {\n  SocialMediaAccount,\n  SocialContentTemplate,\n  SocialMediaCampaign,\n  SocialMediaPost\n};