const mongoose = require('mongoose');

const contentDistributionSchema = new mongoose.Schema({
  originalPost: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BlogPost',
    required: true
  },
  distributions: [{
    platform: {
      type: String,
      enum: ['reddit', 'quora', 'tumblr', 'hackernews', 'medium', 'dev.to', 'hashnode', 'linkedin', 'twitter', 'facebook'],
      required: true
    },
    platformSpecific: {
      // Reddit
      subreddit: String,
      flairId: String,
      
      // Quora
      space: String,
      topics: [String],
      
      // Medium
      publicationId: String,
      tags: [String],
      
      // LinkedIn
      visibility: String, // 'PUBLIC', 'CONNECTIONS'
      
      // Twitter
      hashtags: [String],
      mentions: [String]
    },
    content: {
      title: String,
      body: String,
      excerpt: String,
      adaptedFor: String // How content was adapted for this platform
    },
    scheduling: {
      scheduledFor: Date,
      timezone: String,
      status: {
        type: String,
        enum: ['draft', 'scheduled', 'published', 'failed'],
        default: 'draft'
      }
    },
    published: {
      url: String,
      publishedAt: Date,
      platformPostId: String
    },
    performance: {
      views: { type: Number, default: 0 },
      upvotes: { type: Number, default: 0 },
      downvotes: { type: Number, default: 0 },
      comments: { type: Number, default: 0 },
      shares: { type: Number, default: 0 },
      clicks: { type: Number, default: 0 },
      engagement: { type: Number, default: 0 }, // Platform-specific engagement metric
      lastUpdated: Date
    },
    error: {
      message: String,
      code: String,
      occurredAt: Date
    }
  }],
  
  // AI Analysis for Content Optimization
  aiAnalysis: {
    platformRecommendations: [{
      platform: String,
      score: Number, // 0-100
      reasoning: String,
      suggestedTime: String,
      suggestedTitle: String,
      suggestedTags: [String]
    }],
    competitorAnalysis: [{
      platform: String,
      topPerformingPosts: [{
        url: String,
        title: String,
        engagement: Number,
        insights: String
      }]
    }],
    contentSuggestions: {
      titleVariants: [String],
      excerptVariants: [String],
      hashtagSuggestions: [String],
      bestPostingTimes: [{
        platform: String,
        times: [String]
      }]
    }
  },
  
  metrics: {
    totalViews: { type: Number, default: 0 },
    totalEngagement: { type: Number, default: 0 },
    totalClicks: { type: Number, default: 0 },
    conversionRate: { type: Number, default: 0 },
    leadGenerated: { type: Number, default: 0 }
  },
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Auto-update metrics
contentDistributionSchema.methods.updateMetrics = function() {
  this.metrics.totalViews = this.distributions.reduce((sum, dist) => sum + (dist.performance.views || 0), 0);
  this.metrics.totalEngagement = this.distributions.reduce((sum, dist) => 
    sum + (dist.performance.upvotes || 0) + (dist.performance.comments || 0) + (dist.performance.shares || 0), 0
  );
  this.metrics.totalClicks = this.distributions.reduce((sum, dist) => sum + (dist.performance.clicks || 0), 0);
  
  if (this.metrics.totalViews > 0) {
    this.metrics.conversionRate = (this.metrics.totalClicks / this.metrics.totalViews) * 100;
  }
  
  this.updatedAt = new Date();
};

module.exports = mongoose.model('ContentDistribution', contentDistributionSchema);