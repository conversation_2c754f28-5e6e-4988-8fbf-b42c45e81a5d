const mongoose = require('mongoose');

const emailLeadSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  status: {
    type: String,
    enum: ['pending', 'verified', 'subscribed', 'unsubscribed', 'bounced'],
    default: 'pending'
  },
  source: {
    type: {
      type: String,
      enum: ['landing_page', 'exit_intent', 'content_upgrade', 'webinar', 'free_tool', 'blog_subscription', 'manual'],
      required: true
    },
    url: String,
    campaign: String,
    referrer: String
  },
  profile: {
    name: String,
    occupation: {
      type: String,
      enum: ['student', 'employee', 'freelancer', 'entrepreneur', 'other']
    },
    interests: [String],
    timezone: String,
    language: {
      type: String,
      default: 'en'
    }
  },
  engagement: {
    score: {
      type: Number,
      default: 0
    },
    emailsOpened: {
      type: Number,
      default: 0
    },
    linksClicked: {
      type: Number,
      default: 0
    },
    lastOpenedAt: Date,
    lastClickedAt: Date
  },
  leadMagnets: [{
    type: String,
    downloadedAt: Date
  }],
  tags: [String],
  lists: [{
    name: String,
    subscribedAt: Date,
    unsubscribedAt: Date
  }],
  emailHistory: [{
    campaignId: mongoose.Schema.Types.ObjectId,
    subject: String,
    sentAt: Date,
    opened: {
      type: Boolean,
      default: false
    },
    openedAt: Date,
    clicked: {
      type: Boolean,
      default: false
    },
    clickedLinks: [{
      url: String,
      clickedAt: Date
    }]
  }],
  preferences: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'occasional'],
      default: 'weekly'
    },
    topics: [String],
    contentTypes: [{
      type: String,
      enum: ['tips', 'case_studies', 'tutorials', 'news', 'offers']
    }]
  },
  gdprConsent: {
    given: {
      type: Boolean,
      default: false
    },
    date: Date,
    ip: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes
emailLeadSchema.index({ email: 1 });
emailLeadSchema.index({ 'source.type': 1 });
emailLeadSchema.index({ status: 1 });
emailLeadSchema.index({ 'engagement.score': -1 });
emailLeadSchema.index({ tags: 1 });

// Methods
emailLeadSchema.methods.updateEngagementScore = function() {
  const baseScore = 10;
  const openWeight = 1;
  const clickWeight = 3;
  const recentActivityBonus = 5;
  
  let score = baseScore;
  score += this.engagement.emailsOpened * openWeight;
  score += this.engagement.linksClicked * clickWeight;
  
  // Bonus for recent activity (within 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  if (this.engagement.lastOpenedAt && this.engagement.lastOpenedAt > thirtyDaysAgo) {
    score += recentActivityBonus;
  }
  
  this.engagement.score = score;
  return score;
};

module.exports = mongoose.model('EmailLead', emailLeadSchema);