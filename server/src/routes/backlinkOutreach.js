const express = require('express');
const router = express.Router();
const backlinkOutreachController = require('../controllers/backlinkOutreachController');
const auth = require('../middleware/auth');

// Middleware to verify admin status
const isAdmin = (req, res, next) => {
  if (!req.user || !req.user.isAdmin) {
    return res.status(403).json({ error: 'Access denied. Admin privileges required.' });
  }
  next();
};

// 外链机会管理路由
router.get('/prospects', auth.authenticateJWT, isAdmin, backlinkOutreachController.getProspects);
router.post('/prospects', auth.authenticateJWT, isAdmin, backlinkOutreachController.createProspect);
router.put('/prospects/:id', auth.authenticateJWT, isAdmin, backlinkOutreachController.updateProspect);
router.post('/discover-prospects', auth.authenticateJWT, isAdmin, backlinkOutreachController.discoverProspects);

// 外联活动管理路由
router.get('/campaigns', auth.authenticateJWT, isAdmin, backlinkOutreachController.getCampaigns);
router.post('/campaigns', auth.authenticateJWT, isAdmin, backlinkOutreachController.createCampaign);
router.post('/campaigns/:id/start', auth.authenticateJWT, isAdmin, backlinkOutreachController.startCampaign);

// 统计数据路由
router.get('/outreach-stats', auth.authenticateJWT, isAdmin, backlinkOutreachController.getOutreachStats);

module.exports = router;