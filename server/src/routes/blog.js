const express = require('express');
const router = express.Router();
const blogController = require('../controllers/blogController');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');
const { authenticateApiKey } = require('../middleware/apiKeyAuth');

// Public routes
router.get('/', blogController.getBlogPosts);
router.get('/post/:slug', blogController.getBlogPostBySlug);
router.get('/categories', blogController.getCategories);

// Admin routes
router.get('/all', auth.authenticateJWT, isAdmin, blogController.getAllBlogPosts);
router.get('/:id', auth.authenticateJWT, isAdmin, blogController.getBlogPostById);
router.post('/', auth.authenticateJWT, isAdmin, blogController.createBlogPost);
router.post('/import', auth.authenticateJWT, isAdmin, blogController.importBlogPosts);
router.put('/:id', auth.authenticateJWT, isAdmin, blogController.updateBlogPost);
router.delete('/:id', auth.authenticateJWT, isAdmin, blogController.deleteBlogPost);
router.post('/categories', auth.authenticateJWT, isAdmin, blogController.createCategory);

// API routes for third-party integrations
router.post('/api', authenticateApiKey(['blog:write']), blogController.createBlogPostViaApi);

module.exports = router;
