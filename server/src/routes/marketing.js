const express = require('express');
const router = express.Router();
const marketingController = require('../controllers/marketingController');
const { requireAuth, requireAdmin } = require('../middleware/auth');

// === DASHBOARD ===
router.get('/dashboard', requireAdmin, marketingController.getDashboard);

// === EMAIL LEAD GENERATION ===
router.post('/leads/capture', marketingController.captureEmailLead);
router.post('/leads/magnet', requireAdmin, marketingController.createLeadMagnet);
router.get('/leads/analytics', requireAdmin, marketingController.getLeadAnalytics);

// === FORUM MARKETING ===
router.post('/forum/analyze-competitors', requireAdmin, marketingController.analyzeForumCompetitors);
router.post('/forum/schedule-post', requireAdmin, marketingController.scheduleForumPost);

// === CONTENT DISTRIBUTION ===
router.post('/content/distribute', requireAdmin, marketingController.distributeContent);
router.get('/content/performance', requireAdmin, marketingController.getContentPerformance);

// === BACKLINK OUTREACH ===
router.post('/backlinks/find-opportunities', requireAdmin, marketingController.findBacklinkOpportunities);
router.post('/backlinks/send-outreach', requireAdmin, marketingController.sendOutreachEmail);
router.get('/backlinks/pipeline', requireAdmin, marketingController.getOutreachPipeline);

module.exports = router;