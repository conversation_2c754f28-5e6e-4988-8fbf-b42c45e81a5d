const passport = require('passport');
const isAdminMiddleware = require('./isAdmin');

// Middleware to authenticate JWT token
exports.authenticateJWT = passport.authenticate('jwt', { session: false });

// Alias for consistency
exports.requireAuth = exports.authenticateJWT;

// Combined middleware for admin routes (auth + admin check)
exports.requireAdmin = [exports.authenticateJWT, isAdminMiddleware];
